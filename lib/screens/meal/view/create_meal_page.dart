import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/constants/config.dart';
import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/models/overpass.dart';
import 'package:ai_vanse/screens/home/<USER>/food_post.dart';
import 'package:ai_vanse/screens/meal/controllers/create_meal_controller.dart';
import 'package:ai_vanse/screens/meal/view/widgets/tag_dialog.dart';
import 'package:ai_vanse/screens/post/views/gallery_picker_screen.dart';
import 'package:ai_vanse/utilities/media_services.dart';
import 'package:ai_vanse/utilities/snackbar.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:translator/translator.dart';

class CreateMealPage extends StatefulWidget {
  const CreateMealPage({super.key});

  @override
  State<CreateMealPage> createState() => _CreateMealPageState();
}

class _CreateMealPageState extends State<CreateMealPage> {
  late final CreateMealController controller = Get.find();
  final TextEditingController _nameController = TextEditingController();
  final List<String> defaultTags = [
    'Weight Loss',
    'On Diet',
    'Vegan',
    'Green',
    'Meat Lover',
    'Fiber',
  ];
  List<String> selectedTags = [];

  int _rating = 0;
  bool _addNutritionContent = false;
  String _caption = '';
  String _restaurantLocation = '';
  bool _isAnalyzing = false;
  Nutrition? _nutrition;
  String? _foodNames;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(int maxCount, RequestType requestType) async {
    await MediaServices().requestPermissions();
    if (!MediaServices().permissionsGranted) {
      if (mounted) {
        showSnackBar(context, 'Please grant permissions to access media');
      }
      return;
    }

    if (mounted) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const GalleryPickerScreen()),
      );

      if (result != null && result is AssetEntity) {
        setState(() {
          controller.selectedAsset = result;
        });
      }
    }
  }

  Future<String?> _showInputDialog(String title, String? initialValue) async {
    String? result = initialValue;
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.topCenter,
          child: Material(
            color: Colors.transparent,
            child: Container(
              height: 245.sp,
              decoration: BoxDecoration(color: Colors.white),
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'Back',
                            style: TextStyle(color: Colors.blue, fontSize: 16),
                          ),
                        ),
                        Text(
                          title,
                          style: TextStyle(
                            color: Colors.green,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            'OK',
                            style: TextStyle(color: Colors.blue, fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: TextField(
                      controller: TextEditingController(text: initialValue),
                      autofocus: true,
                      onChanged: (value) {
                        result = value;
                      },
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        hintText: 'Enter $title',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
    return result;
  }

  Widget _buildImagePicker() {
    return GestureDetector(
      onTap: () {
        // _pickImage(1, RequestType.image);
      },
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.0)),
        clipBehavior: Clip.antiAlias,
        height: 300.sp,
        child: Center(child: _buildImage()),
      ),
    );
  }

  Widget _buildImage() {
    if (controller.selectedAsset != null) {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: AssetEntityImage(
          controller.selectedAsset!,
          isOriginal: false,
          filterQuality: FilterQuality.high,
          fit: BoxFit.cover,
          thumbnailSize: const ThumbnailSize.square(2000),
        ),
      );
    }
    if (controller.imageFile != null) {
      return FutureBuilder<Uint8List>(
        future: controller.imageFile!.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.hasData) return Image.memory(snapshot.data!);
          return Center(child: CircularProgressIndicator());
        },
      );
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.add_photo_alternate_outlined, size: 48),
        SizedBox(height: 8),
        Text('Add Meal Photo'),
      ],
    );
  }

  Widget _buildRatingSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AppText('Rating', appTextStyle: AppTextStyle.bodyText),
        Row(
          children: List.generate(5, (index) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  _rating = index + 1;
                });
              },
              child: Padding(
                padding: EdgeInsets.only(right: 4.w),
                child: Opacity(
                  opacity: index < _rating ? 1.0 : 0.3,
                  child: Image.asset(
                    Assets.images.rating.path,
                    width: 30.sp,
                    height: 30.sp,
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildSelectedTags() {
    if (selectedTags.isEmpty) {
      return SliverToBoxAdapter(
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () async {
            await showDialog(
              context: context,
              builder:
                  (context) => TagDialog(
                    defaultTags: defaultTags,
                    selectedTags: selectedTags,
                    onTagsSelected: (tags) {
                      setState(() {
                        selectedTags = tags;
                      });
                    },
                  ),
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
            decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: AppText(
              'Add a tag...',
              appTextStyle: AppTextStyle.bodyText,
              color: Colors.grey.shade400,
            ),
          ),
        ),
      );
    }

    return SliverToBoxAdapter(
      child: GestureDetector(
        onTap: () async {
          await showDialog(
            context: context,
            builder:
                (context) => TagDialog(
                  defaultTags: defaultTags,
                  selectedTags: selectedTags,
                  onTagsSelected: (tags) {
                    setState(() {
                      selectedTags = tags;
                    });
                  },
                ),
          );
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
          ),
          child: SizedBox(
            height: 36.sp,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: selectedTags.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.sp),
              itemBuilder: (context, index) {
                final tag = selectedTags[index];
                return Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.sp,
                    vertical: 8.sp,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(20.sp),
                  ),
                  child: Text(
                    '# $tag',
                    style: TextStyle(color: Colors.white, fontSize: 14.sp),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputField(
    String placeholder, {
    VoidCallback? onTap,
    bool showBorder = true,
    bool isTitle = false,
    bool center = false,
    String? value,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.sp),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: showBorder ? Colors.grey.shade200 : Colors.transparent,
            ),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: AppText(
                textAlign: center ? TextAlign.center : TextAlign.start,
                value?.isNotEmpty == true ? value! : placeholder,
                appTextStyle:
                    isTitle ? AppTextStyle.titleSmall : AppTextStyle.bodyText,
                color:
                    value?.isNotEmpty == true
                        ? Colors.black
                        : Colors.grey.shade400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<String?> uploadImageToIMGBB(File imageFile) async {
    final imgbbApiKey = AppConfig.imgbbApiKey;
    final imgbbUploadEndpoint = AppConfig.imgbbUploadEndpoint;

    try {
      final originalSize = await imageFile.length();
      print(
        'Original image size: ${(originalSize / 1024 / 1024).toStringAsFixed(2)} MB',
      );

      final dir = path.dirname(imageFile.path);
      final ext = path.extension(imageFile.path);
      final compressedFile = File('$dir/compressed$ext');

      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.path,
        compressedFile.path,
        quality: 85,
        minWidth: 720,
        minHeight: 720,
      );

      if (result == null) {
        _handleError('Image compression failed');
        return null;
      }

      final compressedSize = await result.length();
      print(
        'Compressed image size: ${(compressedSize / 1024 / 1024).toStringAsFixed(2)} MB',
      );
      print(
        'Compression ratio: ${((1 - compressedSize / originalSize) * 100).toStringAsFixed(1)}%',
      );

      final url = Uri.parse('$imgbbUploadEndpoint$imgbbApiKey');
      final request = http.MultipartRequest('POST', url)
        ..files.add(await http.MultipartFile.fromPath('image', result.path));

      final response = await request.send();
      await compressedFile.delete();

      if (response.statusCode != 200) return null;

      final responseData = await response.stream.bytesToString();
      final jsonResponse = jsonDecode(responseData);
      return jsonResponse['data']['url'];
    } catch (e) {
      _handleError('Service error: Image upload failed');
      return null;
    }
  }

  Future<void> analyzeImage() async {
    if (controller.selectedAsset == null) {
      showSnackBar(context, 'Please select an image first');
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _nutrition = null;
    });

    try {
      final imageFile = await controller.selectedAsset!.file;
      if (imageFile == null) {
        _handleError('Failed to get image file');
        return;
      }

      final imageUrl = await uploadImageToIMGBB(imageFile);
      if (imageUrl == null) {
        _handleError('Failed to upload image');
        return;
      }

      final apiEndpoint = AppConfig.aiServiceApiEndpoint;
      final response = await http
          .post(
            Uri.parse(apiEndpoint),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'image_path': imageUrl}),
          )
          .timeout(Duration(seconds: 90));

      if (response.statusCode != 200) {
        _handleError('Bad request: ${response.statusCode}');
        return;
      }

      await _parseAIResponse(response.body);
    } catch (e) {
      _handleError('Analysis failed: $e');
    } finally {
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  Future<void> _parseAIResponse(String response) async {
    final Map<String, dynamic> jsonResponse = jsonDecode(response);

    if (jsonResponse['error'] != null) {
      _handleError('Service error: Try again or choose a different image');
      return;
    }

    final String? result = jsonResponse['result'];
    if (result == null || result.isEmpty) {
      _handleError('Service error: invalid result');
      return;
    }

    final List<String> values = result.split(';').map((e) => e.trim()).toList();
    final List<String> keys = [
      'foodNames',
      'totalPortionSize',
      'energy',
      'protein',
      'fat',
      'carbohydrate',
      'fiber',
      'totalSugars',
      'calcium',
      'iron',
      'magnesium',
      'phosphorus',
      'potassium',
      'sodium',
      'zinc',
      'cholesterol',
    ];

    if (values.length < keys.length) {
      _handleError('Invalid response format');
      return;
    }

    Map<String, dynamic> nutritionData = {};
    for (int i = 0; i < keys.length; i++) {
      if (keys[i] == 'foodNames') {
        if ('localeName'.tr == 'zh') {
          _foodNames = await _translate(values[i]);
        } else {
          _foodNames = values[i];
        }
        // Update the food name controller
        setState(() {
          _nameController.text = _foodNames ?? '';
        });
        continue;
      }
      // Convert string values to double for nutrition data
      nutritionData[keys[i]] = double.tryParse(values[i]) ?? 0.0;
    }

    setState(() {
      _nutrition = Nutrition(
        totalPortionSize: nutritionData['totalPortionSize'] ?? 0.0,
        energy: nutritionData['energy'] ?? 0.0,
        protein: nutritionData['protein'] ?? 0.0,
        fat: nutritionData['fat'] ?? 0.0,
        carbohydrate: nutritionData['carbohydrate'] ?? 0.0,
        fiber: nutritionData['fiber'] ?? 0.0,
        totalSugars: nutritionData['totalSugars'] ?? 0.0,
        calcium: nutritionData['calcium'] ?? 0.0,
        iron: nutritionData['iron'] ?? 0.0,
        magnesium: nutritionData['magnesium'] ?? 0.0,
        phosphorus: nutritionData['phosphorus'] ?? 0.0,
        potassium: nutritionData['potassium'] ?? 0.0,
        sodium: nutritionData['sodium'] ?? 0.0,
        zinc: nutritionData['zinc'] ?? 0.0,
        cholesterol: nutritionData['cholesterol'] ?? 0.0,
      );
    });
  }

  Future<String> _translate(String value) async {
    try {
      final translator = GoogleTranslator();
      final translation = await translator.translate(value, to: 'zh-cn');

      return translation.text;
    } catch (e) {
      return '';
    }
  }

  void _handleError(String message) {
    setState(() {
      _isAnalyzing = false;
    });
    if (mounted) {
      showSnackBar(context, message);
    }
  }

  Widget _buildNutritionContent() {
    if (!_addNutritionContent) return const SizedBox.shrink();

    if (_isAnalyzing) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_nutrition == null) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No nutrition data available'),
        ),
      );
    }

    final nutritionDisplay = _nutrition!.toDisplayMap();

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            nutritionDisplay.entries.map((entry) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4.sp),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [AppText(entry.key), AppText(entry.value)],
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildAINutritionToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AppText(
          'Add AI Nutrition Content',
          appTextStyle: AppTextStyle.bodyText,
        ),
        Switch(
          value: _addNutritionContent,
          onChanged: (value) async {
            setState(() {
              _addNutritionContent = value;
            });
            // Only call API if toggling ON and no existing data
            if (value &&
                controller.selectedAsset != null &&
                _nutrition == null) {
              await analyzeImage();
            }
          },
          activeColor: context.theme.extraColors.energy,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      title: 'New Meal',
      appBarFontColor: context.theme.extraColors.energy,
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 32.sp, vertical: 6.sp),
              child: _buildImagePicker(),
            ),
          ),
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            sliver: SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInputField(
                    'Food Name',
                    showBorder: false,
                    center: true,
                    value: _nameController.text,
                    onTap: () async {
                      final name = await _showInputDialog(
                        'Food Name',
                        _nameController.text,
                      );
                      if (name != null) {
                        setState(() {
                          _nameController.text = name;
                        });
                      }
                    },
                  ),
                  SizedBox(height: 12.sp),
                  _buildRatingSelector(),
                  SizedBox(height: 12.sp),
                  Divider(height: 0, color: Colors.grey.shade200),
                  _buildInputField(
                    'Add a caption',
                    value: _caption,
                    onTap: () async {
                      final caption = await _showInputDialog(
                        'Caption',
                        _caption,
                      );
                      if (caption != null) {
                        setState(() {
                          _caption = caption;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
          _buildSelectedTags(),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp),
              child: Column(
                children: [
                  _buildAINutritionToggle(),
                  if (_addNutritionContent) _buildNutritionContent(),
                  Divider(height: 0, color: Colors.grey.shade200),
                  _buildLocation(),
                  _buildMap(),
                ],
              ),
            ),
          ),
          SliverFillRemaining(
            hasScrollBody: false,
            child: Padding(
              padding: EdgeInsets.only(
                top: 20.sp,
                left: 16.sp,
                right: 16.sp,
                bottom: 20.sp + context.safeAreaBottomPadding.bottom,
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      SizedBox(
                        width: 134.sp,
                        height: 40.sp,
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.theme.extraColors.energy,
                            foregroundColor: Colors.white,
                          ),
                          child: AppText('Share'),
                        ),
                      ),
                      SizedBox(width: 16.w),
                      SizedBox(
                        width: 134.sp,
                        height: 40.sp,
                        child: ElevatedButton(
                          onPressed: () {
                            // Handle add action
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF7B61FF),
                            foregroundColor: Colors.white,
                          ),
                          child: AppText('Private'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocation() {
    return Obx(() {
      if (controller.poiLoading.value) {
        return Center(child: CircularProgressIndicator());
      }
      if (controller.pois.isEmpty) return Text('No poi');
      return DropdownButtonFormField<Poi>(
        value: controller.selectedPoi.value,
        onChanged: (Poi? newValue) {
          if (newValue != null) {
            setState(() {
              controller.selectedPoi.value = newValue;
            });
          }
        },
        items:
            controller.pois.mapNotNull<DropdownMenuItem<Poi>>((Poi value) {
              final name = value.name;
              if (name == null) return null;
              return DropdownMenuItem<Poi>(value: value, child: Text(name));
            }).toList(),
      );
    });
    return _buildInputField(
      'Add restaurant location',
      value: _restaurantLocation,
      onTap: () async {
        final location = await _showInputDialog(
          'Restaurant',
          _restaurantLocation,
        );
        if (location != null) {
          setState(() {
            _restaurantLocation = location;
          });
        }
      },
    );
  }

  Widget _buildMap() {
    return Obx(() {
      final location = controller.location.value;
      if (location == null) return SizedBox.shrink();
      return SizedBox(
        height: 300.sp + context.safeAreaBottomPadding.bottom,
        child: IgnorePointer(
          child: FlutterMap(
            options: MapOptions(initialCenter: location, initialZoom: 18),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'dev.fleaflet.flutter_map.example',
              ),
              MarkerLayer(
                markers: [
                  Marker(
                    point: location,
                    width: 32.sp,
                    height: 32.sp,
                    child: Icon(
                      Icons.location_pin,
                      color: context.theme.extraColors.energy,
                      size: 32.sp,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
