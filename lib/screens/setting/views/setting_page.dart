import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/screens/setting/controllers/setting_controller.dart';
import 'package:ai_vanse/screens/setting/models/setting_menu_item.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  late final SettingController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          top: null,
          left: null,
          child: Assets.svgs.bgSetting.svg(),
        ),
        Positioned.fill(
          top: null,
          bottom: 50.sp,
          child: AppText(
            'setting_disclaimer'.tr,
            appTextStyle: AppTextStyle.bodyBold,
            textAlign: TextAlign.center,
            appTextColor: AppTextColor.disabled,
          ),
        ),
        Positioned.fill(
          top: null,
          bottom: 16.sp,
          child: AppText(
            '${'version'.tr} ${controller.version}',
            appTextStyle: AppTextStyle.bodyBold,
            textAlign: TextAlign.center,
            appTextColor: AppTextColor.disabled,
          ),
        ),
        _buildSettingMenuList(controller.menuList),
      ],
    );
  }

  Widget _buildSettingMenuList(List<SettingMenuItem> menuList) {
    return ListView.builder(
      itemCount: menuList.length,
      itemBuilder: (context, index) {
        final menuItem = menuList[index];

        return Column(
          children: [
            ListTile(
              onTap: menuItem.onTap ?? () {},
              leading: SvgPicture.asset(
                menuItem.icon,
                width: 32.sp,
                height: 32.sp,
              ),
              title: AppText(
                menuItem.title,
                appTextStyle: AppTextStyle.h4,
                textAlign: TextAlign.left,
              ),
              subtitle:
                  menuItem.subtitle != null
                      ? AppText(
                        menuItem.subtitle!,
                        appTextStyle: AppTextStyle.bodyText,
                        textAlign: TextAlign.left,
                      )
                      : null,
            ),
            Divider(
              indent: 16.sp,
              endIndent: 16.sp,
              color: context.theme.extraColors.divider,
              height: 1,
            ),
          ],
        );
      },
    );
  }
}
