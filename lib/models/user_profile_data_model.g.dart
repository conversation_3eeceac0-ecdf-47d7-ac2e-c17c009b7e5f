// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  id: (json['id'] as num?)?.toInt(),
  name: json['name'] as String,
  email: json['email'] as String,
  age: (json['age'] as num).toInt(),
  weight: (json['weight'] as num).toDouble(),
  height: (json['height'] as num).toDouble(),
  calorieLimit: (json['calorieLimit'] as num).toDouble(),
  fatLimit: (json['fatLimit'] as num).toDouble(),
  carbLimit: (json['carbLimit'] as num).toDouble(),
  proteinLimit: (json['proteinLimit'] as num).toDouble(),
  fiberLimit: (json['fiberLimit'] as num).toDouble(),
  waterIntakeGoal: (json['waterIntakeGoal'] as num).toDouble(),
  sugarIntakeGoal: (json['sugarIntakeGoal'] as num).toDouble(),
  saltIntakeGoal: (json['saltIntakeGoal'] as num).toDouble(),
  foodPreference: (json['foodPreference'] as num).toInt(),
  noAllergies: json['noAllergies'] as bool,
  glutenIntolerant: json['glutenIntolerant'] as bool,
  wheatIntolerant: json['wheatIntolerant'] as bool,
  lactoseIntolerant: json['lactoseIntolerant'] as bool,
  milkAllergy: json['milkAllergy'] as bool,
  eggAllergy: json['eggAllergy'] as bool,
  shellfishAllergy: json['shellfishAllergy'] as bool,
  fishAllergy: json['fishAllergy'] as bool,
  nutsAllergy: json['nutsAllergy'] as bool,
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'age': instance.age,
      'weight': instance.weight,
      'height': instance.height,
      'calorieLimit': instance.calorieLimit,
      'fatLimit': instance.fatLimit,
      'carbLimit': instance.carbLimit,
      'proteinLimit': instance.proteinLimit,
      'fiberLimit': instance.fiberLimit,
      'waterIntakeGoal': instance.waterIntakeGoal,
      'sugarIntakeGoal': instance.sugarIntakeGoal,
      'saltIntakeGoal': instance.saltIntakeGoal,
      'foodPreference': instance.foodPreference,
      'noAllergies': instance.noAllergies,
      'glutenIntolerant': instance.glutenIntolerant,
      'wheatIntolerant': instance.wheatIntolerant,
      'lactoseIntolerant': instance.lactoseIntolerant,
      'milkAllergy': instance.milkAllergy,
      'eggAllergy': instance.eggAllergy,
      'shellfishAllergy': instance.shellfishAllergy,
      'fishAllergy': instance.fishAllergy,
      'nutsAllergy': instance.nutsAllergy,
    };
