import 'dart:io';

import 'package:ai_vanse/models/overpass.dart';
import 'package:ai_vanse/repositories/meal_repository.dart';
import 'package:exif/exif.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import 'package:photo_manager/photo_manager.dart';

class CreateMealController extends GetxController {
  late final MealRepository _mealRepository = Get.find();
  AssetEntity? selectedAsset;
  XFile? imageFile;

  final Rxn<latlong2.LatLng> location = Rxn();
  final RxList<Poi> pois = RxList();
  final RxBool poiLoading = RxBool(false);
  final Rxn<Poi> selectedPoi = Rxn();

  @override
  void onInit() {
    super.onInit();
    selectedAsset = Get.arguments['asset'];

    imageFile = Get.arguments['image'];
    updateLocationFromAsset();
    updateLocationFromXFile();
  }

  Future<void> updateLocationFromAsset() async {
    final File? file = await selectedAsset?.originFile;
    if (file == null) return;
    await updateLocationFromFile(file);
  }

  Future<void> updateLocationFromXFile() async {
    var path = imageFile?.path;
    if (path == null) return;
    final File file = File(path);
    await updateLocationFromFile(file);
  }

  Future<void> updateLocationFromFile(File file) async {
    final tags = await readExifFromFile(file);
    final latitudeValue =
        (tags['GPS GPSLatitude']?.values as IfdRatios).ratios
            .map<double>(
              (item) =>
                  (item.numerator.toDouble() / item.denominator.toDouble()),
            )
            .toList();
    final latitudeSignal = tags['GPS GPSLatitudeRef']?.printable;

    final longitudeValue =
        (tags['GPS GPSLongitude']?.values as IfdRatios).ratios
            .map<double>(
              (item) =>
                  (item.numerator.toDouble() / item.denominator.toDouble()),
            )
            .toList();
    final longitudeSignal = tags['GPS GPSLongitudeRef']?.printable;

    double latitude =
        latitudeValue[0] + (latitudeValue[1] / 60) + (latitudeValue[2] / 3600);

    double longitude =
        longitudeValue[0] +
        (longitudeValue[1] / 60) +
        (longitudeValue[2] / 3600);

    if (latitudeSignal == 'S') latitude = -latitude;
    if (longitudeSignal == 'W') longitude = -longitude;

    print('latitude $latitude: longitude $longitude');
    if (latitude.isNaN || longitude.isNaN) return;

    location.value = latlong2.LatLng(latitude, longitude);
    searchNearby(latitude, longitude);
  }

  Future<void> searchNearby(double latitude, double longitude) async {
    poiLoading.value = true;
    try {
      pois.value = await _mealRepository.getNearbyRestaurants(
        latitude: latitude,
        longitude: longitude,
        radiusInMeters: 150,
      );
      selectedPoi.value = pois.firstWhereOrNull((poi) => poi.name != null);
    } catch (e, stacktrace) {
      debugPrintStack(stackTrace: stacktrace, label: e.toString());
    } finally {
      poiLoading.value = false;
    }
  }
}
