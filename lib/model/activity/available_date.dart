import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'available_date.freezed.dart';
part 'available_date.g.dart';

AvailableDate availableDateFromJson(String str) =>
    AvailableDate.fromJson(json.decode(str));

String availableDateToJson(AvailableDate data) => json.encode(data.toJson());

@freezed
abstract class AvailableDate with _$AvailableDate {
  const factory AvailableDate({List<DateTime>? availableDate}) = _AvailableDate;

  factory AvailableDate.fromJson(Map<String, dynamic> json) =>
      _$AvailableDateFromJson(json);
}
