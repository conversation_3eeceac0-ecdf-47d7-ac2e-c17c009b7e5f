import 'package:ai_vanse/api/dio/dio_client.dart';
import 'package:ai_vanse/model/activity/available_date.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'available_date_api.g.dart';

@RestApi(baseUrl: 'api/graphavailabledate')
abstract class AvailableDateApi {
  factory AvailableDateApi() =>
      _AvailableDateApi(DioClient.getInstance(authenticated: true));

  @GET('/patient/{patientId}')
  Future<HttpResponse<AvailableDate>> getAvailableDateByPatientId({
    @Path('patientId') required int patientId,
  });
}
