import 'dart:convert';

import 'package:ai_vanse/constants/config.dart';
import 'package:http/http.dart' as http;

class Translated {
  late final String text;
  late final bool success;

  Translated({required this.text, required this.success});

  factory Translated.fromJson(Map<String, dynamic> json) {
    return Translated(
      text: json['translated_text'] ?? '',
      success: json.containsKey('translated_text'),
    );
  }
}

class TranslateService {
  String? endpoint = AppConfig.translateServiceApiEndpoint;

  Future<bool> vitalCheck() async {
    if (endpoint == null) return false;

    try {
      final url = Uri.parse(endpoint!);
      final response = await http.get(url);
      if (response.statusCode != 200) return false;

      final jsonResponse = jsonDecode(response.body);
      return jsonResponse['status'] == 'ok';
    } catch (e) {
      return false;
    }
  }

  Future<Translated> translate(String text, String lang) async {
    if (endpoint == null) {
      return Translated(text: '', success: false);
    }

    final url = Uri.parse(endpoint!);
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'text': text, 'lang': lang}),
    );

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return Translated.fromJson(jsonResponse);
    } else {
      return Translated(text: '', success: false);
    }
  }
}
