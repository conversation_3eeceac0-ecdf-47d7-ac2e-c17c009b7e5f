import 'package:ai_vanse/config.dart';
import 'package:ai_vanse/constants/env/env.dart';
import 'package:flutter/material.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() {
  mainApp(Env());
}

//
// class AiVanseApp extends StatefulWidget {
//   const AiVanseApp({super.key});
//
//   @override
//   State<AiVanseApp> createState() => _AiVanseAppState();
// }
//
// class _AiVanseAppState extends State<AiVanseApp> {
//   Locale _locale = Locale('zh');
//   late StreamSubscription _loginSubscription;
//   late StreamSubscription _logoutSubscription;
//   late StreamSubscription _languageSubscription;
//   bool _isLoggedIn = false;
//   bool _isInitializing = true;
//
//   void _fetchLocale() {
//     StorageService storage = StorageService();
//
//     String languageCode = storage.lang ?? Platform.localeName;
//
//     _locale = Locale(languageCode);
//     Intl.defaultLocale = languageCode;
//   }
//
//   @override
//   void initState() {
//     super.initState();
//     _fetchLocale();
//     _checkLoginStatus();
//
//     _loginSubscription = eventBus.on<LoginSuccessEvent>().listen((event) {
//       if (event.success) {
//         if (mounted) {
//           setState(() {
//             _isLoggedIn = true;
//           });
//           navigatorKey.currentState?.pushReplacement(
//             MaterialPageRoute(builder: (context) => Home()),
//           );
//         }
//       }
//     });
//
//     _languageSubscription = eventBus.on<LocaleChangedTriggerEmit>().listen((
//       event,
//     ) {
//       changeLanguage(event.lang);
//     });
//   }
//
//   Future<void> _checkLoginStatus() async {
//     // add delay a bit to display loading screen nicely
//     await Future.delayed(Duration(seconds: 1));
//
//     bool isLoggedIn = await AuthService().isLoggedIn();
//
//     if (mounted) {
//       setState(() {
//         _isLoggedIn = isLoggedIn;
//         _isInitializing = false;
//       });
//     }
//   }
//
//   @override
//   void dispose() {
//     _loginSubscription.cancel();
//     _logoutSubscription.cancel();
//     _languageSubscription.cancel();
//     super.dispose();
//   }
//
//   void changeLanguage(String lang) {
//     setState(() {
//       _locale = Locale(lang);
//
//       Intl.defaultLocale = lang;
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       navigatorKey: navigatorKey,
//       debugShowCheckedModeBanner: false,
//       defaultTransition: Transition.cupertino,
//       opaqueRoute: Get.isOpaqueRouteDefault,
//       popGesture: Get.isPopGestureEnable,
//       getPages: pages,
//       theme: lightTheme,
//       locale: _locale,
//       supportedLocales: L10n.all,
//       localizationsDelegates: [
//         GlobalMaterialLocalizations.delegate,
//         GlobalWidgetsLocalizations.delegate,
//         GlobalCupertinoLocalizations.delegate,
//         AppLocalizations.delegate,
//       ],
//     );
//   }
//
//   Widget _buildLoadingScreen() {
//     return Scaffold(
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Builder(
//               builder: (context) {
//                 return Text(
//                   AppLocalizations.of(context)?.appName ?? 'AiVanse',
//                   style: TextStyle(
//                     fontSize: 32,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.teal,
//                   ),
//                 );
//               },
//             ),
//             SizedBox(height: 24),
//             CircularProgressIndicator(
//               valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
