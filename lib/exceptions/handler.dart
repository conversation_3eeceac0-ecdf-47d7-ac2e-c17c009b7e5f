import 'package:ai_vanse/exceptions/popup_exception.dart';
import 'package:ai_vanse/utils/common.dart';
import 'package:ai_vanse/widgets/app_alert_dialog.dart';
import 'package:get/get.dart';

class ExceptionHandler {
  static const List<String> _errorPopup = [];

  static errorMapping({String? title, String? code}) {
    if (_errorPopup.contains(code)) {
      throw PopupException(title: title, code: code);
    }

    // Default error
    throw PopupException(title: title, code: code);
  }

  static handler(Object error, StackTrace stack) {
    Common.hideLoading();

    if (error is PopupException) {
      AppAlertDialog.show(
        error.title,
        error.message,
        type: AppAlertDialogType.error,
      );
    } else {
      // if (!kDebugMode) {
      //   FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      // }

      // Default error
      AppAlertDialog.show(
        'error_general_title'.tr,
        'error_general_message'.tr,
        type: AppAlertDialogType.error,
      );
    }
  }
}
