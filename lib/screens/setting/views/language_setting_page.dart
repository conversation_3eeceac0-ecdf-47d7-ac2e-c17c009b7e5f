import 'package:ai_vanse/l10n/translate.dart';
import 'package:ai_vanse/screens/setting/controllers/language_setting_controller.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class LanguageSettingPage extends StatefulWidget {
  const LanguageSettingPage({super.key});

  @override
  State<LanguageSettingPage> createState() => _LanguageSettingPageState();
}

class _LanguageSettingPageState extends State<LanguageSettingPage> {
  late final LanguageSettingController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      title: 'language'.tr,
      body: Padding(
        padding: EdgeInsets.all(8.0.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AppText('language'.tr),
            Obx(() {
              return DropdownButton<AppTranslationLanguage>(
                value: controller.selectedLanguage.value,
                items:
                    AppTranslationLanguage.values.map((language) {
                      return DropdownMenuItem<AppTranslationLanguage>(
                        value: language,
                        child: AppText(language.tr),
                      );
                    }).toList(),
                onChanged: controller.changeLanguage,
              );
            }),
          ],
        ),
      ),
    );
  }
}
