import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:ai_vanse/screens/home/<USER>/food_post.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

class FoodChoiceDetailPage extends StatefulWidget {
  const FoodChoiceDetailPage({super.key});

  @override
  State<FoodChoiceDetailPage> createState() => _FoodChoiceDetailPageState();
}

class _FoodChoiceDetailPageState extends State<FoodChoiceDetailPage> {
  final FoodPost foodPost = Get.arguments as FoodPost;

  String getInitials(String name) {
    if (name.isEmpty) return '';
    final names = name.split(' ');
    return names[0][0].toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      extendBodyBehindAppBar: false,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Get.back(),
        ),
        title: AppText(
          'FOOD CHOICE',
          appTextStyle: AppTextStyle.bodyBold,
          appTextColor: AppTextColor.energy,
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 16.sp),
            child: CircleAvatar(
              radius: 20.sp,
              backgroundColor: Colors.green,
              child: Text(
                getInitials(foodPost.userName),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18.sp,
                ),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            CachedNetworkImage(
              imageUrl: foodPost.imageUrl,
              height: 300.sp,
              fit: BoxFit.cover,
            ),
            Padding(
              padding: EdgeInsets.all(16.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      ...foodPost.tags.map(
                        (tag) => Padding(
                          padding: EdgeInsets.only(right: 8.sp),
                          child: _buildTag(tag),
                        ),
                      ),
                      Spacer(),
                      _buildRating(foodPost.rating),
                    ],
                  ),
                  SizedBox(height: 16.sp),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 25.sp,
                        backgroundColor: Colors.green,
                        child: Text(
                          getInitials(foodPost.userName),
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 20.sp,
                          ),
                        ),
                      ),
                      SizedBox(width: 12.sp),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppText(
                            foodPost.userName,
                            appTextStyle: AppTextStyle.h4,
                          ),
                          AppText(
                            'Verified Nutritionist',
                            appTextStyle: AppTextStyle.bodyText,
                            appTextColor: AppTextColor.tertiary,
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 24.sp),
                  AppText(foodPost.foodName, appTextStyle: AppTextStyle.h3),
                  SizedBox(height: 16.sp),
                  _buildNutritionSection(),
                ],
              ),
            ),
            _buildMap(),
          ],
        ),
      ),
    );
  }

  Widget _buildTag(String text) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 6.sp),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20.sp),
      ),
      child: AppText(
        text,
        appTextStyle: AppTextStyle.bodyText,
        appTextColor: AppTextColor.energy,
      ),
    );
  }

  Widget _buildRating(int count) {
    return Row(
      children: [
        AppText(
          'Rating: ',
          appTextStyle: AppTextStyle.bodyText,
          appTextColor: AppTextColor.tertiary,
        ),
        ...List.generate(
          count,
          (index) => Padding(
            padding: EdgeInsets.only(left: 4.sp),
            child: Image.asset(
              Assets.images.rating.path,
              width: 16.sp,
              height: 16.sp,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNutritionSection() {
    final nutritionMap = foodPost.nutrition.toDisplayMap();
    return Column(
      children: [
        ...nutritionMap.entries.map(
          (entry) => _buildNutritionItem(entry.key, entry.value),
        ),
      ],
    );
  }

  Widget _buildNutritionItem(String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.sp),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: AppText(label, appTextStyle: AppTextStyle.bodyText),
          ),
          Expanded(
            child: AppText(
              value,
              appTextStyle: AppTextStyle.bodyText,
              appTextColor: AppTextColor.tertiary,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return SizedBox(
      height: 300.sp + context.safeAreaBottomPadding.bottom,
      child: FlutterMap(
        options: MapOptions(
          initialCenter: LatLng(13.7, 100), // Center the map over London
          initialZoom: 15,
        ),
        children: [
          TileLayer(
            urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
            userAgentPackageName: 'dev.fleaflet.flutter_map.example',
          ),
          MarkerLayer(
            markers: [
              Marker(
                point: LatLng(13.7, 100),
                width: 80,
                height: 80,
                child: ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(12.r)),
                  child: CachedNetworkImage(
                    imageUrl: foodPost.imageUrl,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
