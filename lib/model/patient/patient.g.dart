// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'patient.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Patient _$PatientFromJson(Map<String, dynamic> json) => _Patient(
  id: (json['id'] as num?)?.toInt(),
  firstname: json['firstname'] as String?,
  lastname: json['lastname'] as String?,
  firstnameEn: json['firstnameEn'] as String?,
  lastnameEn: json['lastnameEn'] as String?,
  hn: json['hn'] as String?,
  age: (json['age'] as num?)?.toInt(),
  dob: json['dob'] == null ? null : DateTime.parse(json['dob'] as String),
  gender:
      json['gender'] == null ? null : Gender.fromJson(json['gender'] as String),
  status: json['status'] as String?,
  bloodGroup: json['bloodGroup'] as String?,
  address: json['address'] as String?,
  phone: json['phone'] as String?,
  homeAddressId: json['homeAddressId'] as String?,
  createdDate:
      json['createdDate'] == null
          ? null
          : DateTime.parse(json['createdDate'] as String),
  modifiedDate:
      json['modifiedDate'] == null
          ? null
          : DateTime.parse(json['modifiedDate'] as String),
  deletedDate:
      json['deletedDate'] == null
          ? null
          : DateTime.parse(json['deletedDate'] as String),
  weight: (json['weight'] as num?)?.toDouble(),
  height: (json['height'] as num?)?.toInt(),
  note: json['note'] as String?,
  imgProfileUrl: json['imgProfileUrl'] as String?,
);

Map<String, dynamic> _$PatientToJson(_Patient instance) => <String, dynamic>{
  'id': instance.id,
  'firstname': instance.firstname,
  'lastname': instance.lastname,
  'firstnameEn': instance.firstnameEn,
  'lastnameEn': instance.lastnameEn,
  'hn': instance.hn,
  'age': instance.age,
  'dob': instance.dob?.toIso8601String(),
  'gender': _$GenderEnumMap[instance.gender],
  'status': instance.status,
  'bloodGroup': instance.bloodGroup,
  'address': instance.address,
  'phone': instance.phone,
  'homeAddressId': instance.homeAddressId,
  'createdDate': instance.createdDate?.toIso8601String(),
  'modifiedDate': instance.modifiedDate?.toIso8601String(),
  'deletedDate': instance.deletedDate?.toIso8601String(),
  'weight': instance.weight,
  'height': instance.height,
  'note': instance.note,
  'imgProfileUrl': instance.imgProfileUrl,
};

const _$GenderEnumMap = {
  Gender.male: 'male',
  Gender.female: 'female',
  Gender.other: 'other',
};
