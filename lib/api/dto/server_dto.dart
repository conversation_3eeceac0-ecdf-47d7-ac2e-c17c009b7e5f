import 'package:freezed_annotation/freezed_annotation.dart';

part 'server_dto.freezed.dart';
part 'server_dto.g.dart';

@freezed
abstract class ServerDto with _$ServerDto {
  @JsonSerializable(explicitToJson: true)
  const factory ServerDto({
    required int id,
    required String name,
    required String apiUrl,
    required String notificationUrl,
    required String code,
  }) = _ServerDto;

  factory ServerDto.fromJson(dynamic json) => _$ServerDtoFromJson(json);
}
