// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserDto _$UserDtoFromJson(Map<String, dynamic> json) => _UserDto(
  id: (json['id'] as num).toInt(),
  userName: json['userName'] as String,
  email: json['email'] as String?,
  fullname: json['fullname'] as String?,
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  tel: json['tel'] as String?,
  subgroupPermission: json['subgroupPermission'] as String?,
  tenantPermission: json['tenantPermission'] as String,
  roleId: (json['roleId'] as num).toInt(),
  roles: UserRoleDto.fromJson(json['roles']),
);

Map<String, dynamic> _$UserDtoToJson(_UserDto instance) => <String, dynamic>{
  'id': instance.id,
  'userName': instance.userName,
  'email': instance.email,
  'fullname': instance.fullname,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'tel': instance.tel,
  'subgroupPermission': instance.subgroupPermission,
  'tenantPermission': instance.tenantPermission,
  'roleId': instance.roleId,
  'roles': instance.roles.toJson(),
};

_UserRoleDto _$UserRoleDtoFromJson(Map<String, dynamic> json) => _UserRoleDto(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  description: json['description'] as String?,
  forDeveloper: json['forDeveloper'] as bool?,
  createdDate: DateTime.parse(json['createdDate'] as String),
  modifiedDate:
      json['modifiedDate'] == null
          ? null
          : DateTime.parse(json['modifiedDate'] as String),
  deletedDate:
      json['deletedDate'] == null
          ? null
          : DateTime.parse(json['deletedDate'] as String),
);

Map<String, dynamic> _$UserRoleDtoToJson(_UserRoleDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'forDeveloper': instance.forDeveloper,
      'createdDate': instance.createdDate.toIso8601String(),
      'modifiedDate': instance.modifiedDate?.toIso8601String(),
      'deletedDate': instance.deletedDate?.toIso8601String(),
    };

Map<String, dynamic> _$UserRequestDtoToJson(_UserRequestDto instance) =>
    <String, dynamic>{
      'firstname': instance.firstname,
      'lastname': instance.lastname,
      'email': instance.email,
      'tel': instance.tel,
      'homeAddress': instance.homeAddress,
    };

Map<String, dynamic> _$HomeAddressToJson(_HomeAddress instance) =>
    <String, dynamic>{
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'province': instance.province,
      'zipcode': instance.zipcode,
    };
