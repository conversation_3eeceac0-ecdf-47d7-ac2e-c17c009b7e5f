import 'package:json_annotation/json_annotation.dart';

part 'user_profile_data_model.g.dart';

@JsonSerializable()
class UserProfile {
  final int? id;
  final String name;
  final String email;
  final int age;
  final double weight;
  final double height;
  final double calorieLimit;
  final double fatLimit;
  final double carbLimit;
  final double proteinLimit;
  final double fiberLimit;
  final double waterIntakeGoal;
  final double sugarIntakeGoal;
  final double saltIntakeGoal;
  final int foodPreference;
  final bool noAllergies;
  final bool glutenIntolerant;
  final bool wheatIntolerant;
  final bool lactoseIntolerant;
  final bool milkAllergy;
  final bool eggAllergy;
  final bool shellfishAllergy;
  final bool fishAllergy;
  final bool nutsAllergy;

  UserProfile({
    this.id,
    required this.name,
    required this.email,
    required this.age,
    required this.weight,
    required this.height,
    required this.calorieLimit,
    required this.fatLimit,
    required this.carbLimit,
    required this.proteinLimit,
    required this.fiberLimit,
    required this.waterIntakeGoal,
    required this.sugarIntakeGoal,
    required this.saltIntakeGoal,
    required this.foodPreference,
    required this.noAllergies,
    required this.glutenIntolerant,
    required this.wheatIntolerant,
    required this.lactoseIntolerant,
    required this.milkAllergy,
    required this.eggAllergy,
    required this.shellfishAllergy,
    required this.fishAllergy,
    required this.nutsAllergy,
  });

  UserProfile.defaultProfile()
    : id = 0,
      name = 'User',
      email = '<EMAIL>',
      age = 30,
      weight = 75.0,
      height = 180.0,
      calorieLimit = 2500,
      fatLimit = 80,
      carbLimit = 350,
      proteinLimit = 60,
      fiberLimit = 35,
      waterIntakeGoal = 2500,
      sugarIntakeGoal = 60,
      saltIntakeGoal = 7,
      foodPreference = 0,
      // 0 = no, 1 = Vegetarian, 2 = Vegan, 3 = Pescetarian
      noAllergies = true,
      glutenIntolerant = false,
      wheatIntolerant = false,
      lactoseIntolerant = false,
      milkAllergy = false,
      eggAllergy = false,
      shellfishAllergy = false,
      fishAllergy = false,
      nutsAllergy = false;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'age': age,
      'weight': weight,
      'height': height,
      'calorie_limit': calorieLimit,
      'fat_limit': fatLimit,
      'carb_limit': carbLimit,
      'protein_limit': proteinLimit,
      'fiber_limit': fiberLimit,
      'water_intake_goal': waterIntakeGoal,
      'sugar_intake_goal': sugarIntakeGoal,
      'salt_intake_goal': saltIntakeGoal,
      'food_preference': foodPreference,
      'no_allergies': noAllergies ? 1 : 0,
      'gluten_intolerant': glutenIntolerant ? 1 : 0,
      'wheat_intolerant': wheatIntolerant ? 1 : 0,
      'lactose_intolerant': lactoseIntolerant ? 1 : 0,
      'milk_allergy': milkAllergy ? 1 : 0,
      'egg_allergy': eggAllergy ? 1 : 0,
      'shellfish_allergy': shellfishAllergy ? 1 : 0,
      'fish_allergy': fishAllergy ? 1 : 0,
      'nuts_allergy': nutsAllergy ? 1 : 0,
    };
  }

  // Factory constructor to create UserProfile from a map
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      id: map['id'] as int?,
      name: map['name'] as String,
      email: map['email'] as String,
      age: map['age'] as int,
      weight: map['weight'] as double,
      height: map['height'] as double,
      calorieLimit: map['calorie_limit'] as double,
      fatLimit: map['fat_limit'] as double,
      carbLimit: map['carb_limit'] as double,
      proteinLimit: map['protein_limit'] as double,
      fiberLimit: map['fiber_limit'] as double,
      waterIntakeGoal: map['water_intake_goal'] as double,
      sugarIntakeGoal: map['sugar_intake_goal'] as double,
      saltIntakeGoal: map['salt_intake_goal'] as double,
      foodPreference: map['food_preference'] as int,
      noAllergies: map['no_allergies'] == 1,
      glutenIntolerant: map['gluten_intolerant'] == 1,
      wheatIntolerant: map['wheat_intolerant'] == 1,
      lactoseIntolerant: map['lactose_intolerant'] == 1,
      milkAllergy: map['milk_allergy'] == 1,
      eggAllergy: map['egg_allergy'] == 1,
      shellfishAllergy: map['shellfish_allergy'] == 1,
      fishAllergy: map['fish_allergy'] == 1,
      nutsAllergy: map['nuts_allergy'] == 1,
    );
  }
}

String get createUserProfileTableString =>
    'CREATE TABLE profiles(id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, email TEXT, age INTEGER, weight REAL, height REAL, calorie_limit REAL, fat_limit REAL, carb_limit REAL, protein_limit REAL, fiber_limit REAL, water_intake_goal REAL, sugar_intake_goal REAL, salt_intake_goal REAL, food_preference INTEGER, no_allergies INTEGER, gluten_intolerant INTEGER, wheat_intolerant INTEGER, lactose_intolerant INTEGER, milk_allergy INTEGER, egg_allergy INTEGER, shellfish_allergy INTEGER, fish_allergy INTEGER, nuts_allergy INTEGER)';
