import 'dart:async';

import 'package:ai_vanse/utilities/extra.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class DeviceTile extends StatefulWidget {
  final BluetoothDevice device;
  final VoidCallback onTap;

  const DeviceTile({super.key, required this.device, required this.onTap});

  @override
  State<DeviceTile> createState() => _AvailableDeviceTileState();
}

class _AvailableDeviceTileState extends State<DeviceTile> {
  BluetoothConnectionState _connectionState =
      BluetoothConnectionState.disconnected;
  bool _isConnecting = false;

  // bool _isDisconnecting = false;
  String connectionStatus = 'Disconnected';

  late StreamSubscription<BluetoothConnectionState>
  _connectionStateSubscription;
  late StreamSubscription<bool> _isConnectingSubscription;
  late StreamSubscription<bool> _isDisconnectingSubscription;

  @override
  void initState() {
    super.initState();

    _connectionStateSubscription = widget.device.connectionState.listen((
      state,
    ) {
      _connectionState = state;
      connectionStatus =
          _connectionState == BluetoothConnectionState.connected
              ? 'Connected'
              : 'Disconnected';
      if (mounted) {
        setState(() {});
      }
    });

    _isConnectingSubscription = widget.device.isConnecting.listen((state) {
      _isConnecting = state;
      if (state) {
        connectionStatus = 'Connecting';
      }
      if (mounted) {
        setState(() {});
      }
    });

    _isDisconnectingSubscription = widget.device.isDisconnecting.listen((
      state,
    ) {
      // _isDisconnecting = state;
      if (state) {
        connectionStatus = 'Disconnecting';
      }
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    if (_isConnecting) {
      widget.device.disconnectAndUpdateStream().catchError((e) {});
    }
    _connectionStateSubscription.cancel();
    _isConnectingSubscription.cancel();
    _isDisconnectingSubscription.cancel();
    super.dispose();
  }

  Widget buildTile(BuildContext context) {
    String title;
    if (widget.device.platformName.isNotEmpty) {
      title = widget.device.platformName;
    } else {
      title = widget.device.remoteId.toString();
    }
    return ListTile(
      dense: true,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      leading: const Icon(Icons.bluetooth),
      title: Text(
        title,
        style: TextStyle(
          color:
              _connectionState == BluetoothConnectionState.connected
                  ? Colors.white
                  : Colors.black,
          fontSize: Theme.of(context).textTheme.bodyMedium!.fontSize,
        ),
      ),
      // subtitle:
      // Text(connectionStatus, style: Theme.of(context).textTheme.bodySmall),
      trailing: Icon(Icons.arrow_forward_ios_rounded),
      onTap: () => widget.onTap(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color:
            _connectionState == BluetoothConnectionState.connected
                ? Colors.blue[500]
                : Color.fromARGB(40, 180, 180, 180),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: buildTile(context),
    );
  }
}
