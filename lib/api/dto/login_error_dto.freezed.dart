// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_error_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LoginErrorDto {

@JsonKey(name: '__type') String? get type; String? get message;
/// Create a copy of LoginErrorDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LoginErrorDtoCopyWith<LoginErrorDto> get copyWith => _$LoginErrorDtoCopyWithImpl<LoginErrorDto>(this as LoginErrorDto, _$identity);

  /// Serializes this LoginErrorDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoginErrorDto&&(identical(other.type, type) || other.type == type)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,message);

@override
String toString() {
  return 'LoginErrorDto(type: $type, message: $message)';
}


}

/// @nodoc
abstract mixin class $LoginErrorDtoCopyWith<$Res>  {
  factory $LoginErrorDtoCopyWith(LoginErrorDto value, $Res Function(LoginErrorDto) _then) = _$LoginErrorDtoCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: '__type') String? type, String? message
});




}
/// @nodoc
class _$LoginErrorDtoCopyWithImpl<$Res>
    implements $LoginErrorDtoCopyWith<$Res> {
  _$LoginErrorDtoCopyWithImpl(this._self, this._then);

  final LoginErrorDto _self;
  final $Res Function(LoginErrorDto) _then;

/// Create a copy of LoginErrorDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = freezed,Object? message = freezed,}) {
  return _then(_self.copyWith(
type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc

@JsonSerializable(fieldRename: FieldRename.none, explicitToJson: true)
class _LoginErrorDto implements LoginErrorDto {
  const _LoginErrorDto({@JsonKey(name: '__type') this.type, this.message});
  factory _LoginErrorDto.fromJson(Map<String, dynamic> json) => _$LoginErrorDtoFromJson(json);

@override@JsonKey(name: '__type') final  String? type;
@override final  String? message;

/// Create a copy of LoginErrorDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LoginErrorDtoCopyWith<_LoginErrorDto> get copyWith => __$LoginErrorDtoCopyWithImpl<_LoginErrorDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LoginErrorDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LoginErrorDto&&(identical(other.type, type) || other.type == type)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,message);

@override
String toString() {
  return 'LoginErrorDto(type: $type, message: $message)';
}


}

/// @nodoc
abstract mixin class _$LoginErrorDtoCopyWith<$Res> implements $LoginErrorDtoCopyWith<$Res> {
  factory _$LoginErrorDtoCopyWith(_LoginErrorDto value, $Res Function(_LoginErrorDto) _then) = __$LoginErrorDtoCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: '__type') String? type, String? message
});




}
/// @nodoc
class __$LoginErrorDtoCopyWithImpl<$Res>
    implements _$LoginErrorDtoCopyWith<$Res> {
  __$LoginErrorDtoCopyWithImpl(this._self, this._then);

  final _LoginErrorDto _self;
  final $Res Function(_LoginErrorDto) _then;

/// Create a copy of LoginErrorDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = freezed,Object? message = freezed,}) {
  return _then(_LoginErrorDto(
type: freezed == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String?,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
