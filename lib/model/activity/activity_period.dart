import 'package:get/get.dart';

enum ActivityPeriod {
  week,
  month,
  year;

  String get tr => switch (this) {
    ActivityPeriod.week => 'statistic_seven_day'.tr,
    ActivityPeriod.month => 'statistic_four_week'.tr,
    ActivityPeriod.year => 'statistic_one_year'.tr,
  };

  String get trAverage => switch (this) {
    ActivityPeriod.week ||
    ActivityPeriod.month => 'statistic_average_per_day'.tr,
    ActivityPeriod.year => 'statistic_average_per_month'.tr,
  };
}
