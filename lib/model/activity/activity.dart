import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'activity.freezed.dart';
part 'activity.g.dart';

Activity activityFromJson(String str) => Activity.fromJson(json.decode(str));

String activityToJson(Activity data) => json.encode(data.toJson());

@freezed
abstract class Activity with _$Activity {
  const factory Activity({
    Energy? energy,
    ActivityDuration? duration,
    int? steps,
    int? distance,
    DateTime? createdDate,
  }) = _Activity;

  factory Activity.fromJson(Map<String, dynamic> json) =>
      _$ActivityFromJson(json);
}

@freezed
abstract class ActivityDuration with _$ActivityDuration {
  const factory ActivityDuration({
    int? lying,
    int? lyingOnBack,
    int? lyingOnStomach,
    int? lyingOnLeft,
    int? lyingOnRight,
    int? sitting,
    int? standing,
    int? walking,
  }) = _ActivityDuration;

  factory ActivityDuration.fromJson(Map<String, dynamic> json) =>
      _$ActivityDurationFromJson(json);
}

@freezed
abstract class Energy with _$Energy {
  const factory Energy({
    int? lying,
    int? sitting,
    int? standing,
    int? walking,
    int? total,
  }) = _Energy;

  factory Energy.fromJson(Map<String, dynamic> json) => _$EnergyFromJson(json);
}
