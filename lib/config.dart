import 'dart:async';
import 'dart:io';

import 'package:ai_vanse/app.dart';
import 'package:ai_vanse/binding.dart';
import 'package:ai_vanse/constants/config.dart';
import 'package:ai_vanse/constants/env/env.dart';
import 'package:ai_vanse/screens/splash/views/splash_page.dart';
import 'package:ai_vanse/services/identifier_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

Future<void> _setupUI() async {
  await ScreenUtil.ensureScreenSize();

  if (Platform.isAndroid) {
    final bool isAndroidQAndAbove =
        Get.find<IdentifierService>().isAndroidQAndAbove;

    if (isAndroidQAndAbove) {
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    } else {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
  }
}

void mainApp(Env env) {
  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      runApp(const ColoredBox(color: Colors.white, child: SplashWidget()));

      // TODO: Multiple env
      AppConfig.init(env);

      await Future.wait([
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]),
        ScreenUtil.ensureScreenSize(),
        GetStorage.init(),
        registerService(),
      ]);
      await _setupUI();

      runApp(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          fontSizeResolver: FontSizeResolvers.radius,
          builder: (context, child) => const MainApp(),
        ),
      );
    },
    (Object error, StackTrace stack) {
      debugPrintStack(stackTrace: stack, label: error.toString());
      // handle errors here
    },
  );
}
