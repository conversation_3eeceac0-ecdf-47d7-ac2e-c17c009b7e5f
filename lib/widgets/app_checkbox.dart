import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppCheckBox extends StatelessWidget {
  const AppCheckBox({
    super.key,
    required this.value,
    required this.onChanged,
    this.label,
    this.labelTextStyle,
  });

  final void Function(bool) onChanged;

  final bool value;
  final String? label;
  final AppTextStyle? labelTextStyle;

  @override
  Widget build(BuildContext context) {
    final chk = Transform.scale(
      scale: 0.9,
      child: SizedBox(
        width: 18.sp,
        height: 18.sp,
        child: Checkbox(
          value: value,
          onChanged: (bool? value) {
            onChanged(value ?? false);
          },
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.sp),
          ),
          side: BorderSide(color: Colors.grey.shade300, width: 1.sp),
        ),
      ),
    );

    if (label?.isNotEmpty == true) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          chk,
          SizedBox(width: 4.sp),
          GestureDetector(
            onTap: () => onChanged(!value),
            child: AppText(
              label!,
              appTextStyle: labelTextStyle ?? AppTextStyle.bodyText,
            ),
          ),
        ],
      );
    }

    return chk;
  }
}
