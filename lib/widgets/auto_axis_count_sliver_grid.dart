import 'dart:math';

import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';

class AutoAxisCountSliverGrid extends StatelessWidget {
  const AutoAxisCountSliverGrid({
    super.key,
    required this.itemCrossAxisMinSize,
    required this.itemBuilder,
    this.minCrossAxisCount,
    this.maxCrossAxisCount,
    required this.itemCount,
    this.itemMainAxisSize,
    this.padding,
    this.mainAxisSpacing = 0.0,
    this.crossAxisSpacing = 0.0,
    this.childAspectRatio,
  }) : assert(minCrossAxisCount == null || minCrossAxisCount > 0),
       assert(maxCrossAxisCount == null || maxCrossAxisCount > 0),
       assert(itemCrossAxisMinSize > 0);

  final IndexedWidgetBuilder itemBuilder;
  final int? minCrossAxisCount;
  final int? maxCrossAxisCount;
  final double itemCrossAxisMinSize;
  final double? itemMainAxisSize;
  final int itemCount;
  final EdgeInsets? padding;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double? childAspectRatio;

  @override
  Widget build(BuildContext context) {
    final EdgeInsets padding = this.padding ?? context.safeAreaWithoutTop;

    return SliverPadding(
      sliver: SliverLayoutBuilder(builder: buildGridView),
      padding: padding,
    );
  }

  Widget buildGridView(BuildContext context, SliverConstraints constraints) {
    final int crossAxisCount = _getCrossAxisCount(constraints);
    final aspectRatio = _getAspectRatio(context, constraints, crossAxisCount);

    return SliverGrid.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: aspectRatio,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
    );
  }

  int _getCrossAxisCount(SliverConstraints boxConstraints) {
    final double crossAxisSize = _getCrossAxisSize(boxConstraints);
    final int crossAxisCount = (crossAxisSize / itemCrossAxisMinSize).floor();
    if (minCrossAxisCount == null && minCrossAxisCount == null) {
      return crossAxisCount;
    }

    if (minCrossAxisCount != null) {
      return max(minCrossAxisCount!, crossAxisCount);
    }

    if (maxCrossAxisCount != null) {
      return min(maxCrossAxisCount!, crossAxisCount);
    }

    return crossAxisCount.clamp(minCrossAxisCount!, maxCrossAxisCount!);
  }

  double _getAspectRatio(
    BuildContext context,
    SliverConstraints boxConstraints,
    int crossAxisCount,
  ) {
    if (itemMainAxisSize == null) {
      return childAspectRatio ?? 1.0;
    }
    final double itemCrossAxisSize =
        _getCrossAxisSize(boxConstraints) / crossAxisCount;
    return itemCrossAxisSize / itemMainAxisSize!;
  }

  double _getCrossAxisSize(SliverConstraints boxConstraints) =>
      boxConstraints.crossAxisExtent;
}
