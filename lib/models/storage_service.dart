import 'package:shared_preferences/shared_preferences.dart';

const String languageKey = 'language';

class StorageService {
  late final SharedPreferencesWithCache localStorage;

  StorageService._();

  static final StorageService _instance = StorageService._();

  static StorageService get instance => _instance;

  factory StorageService() => _instance;

  Future<void> init() async {
    localStorage = await SharedPreferencesWithCache.create(
      cacheOptions: const SharedPreferencesWithCacheOptions(),
    );
  }

  set lang(String? lang) {
    if (lang == null) {
      localStorage.remove(languageKey);
      return;
    }
    localStorage.setString(languageKey, lang);
  }

  String? get lang => localStorage.getString(languageKey);
}
