import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class MediaServices {
  MediaServices._privateConstructor();

  static final MediaServices _instance = MediaServices._privateConstructor();

  factory MediaServices() {
    return _instance;
  }

  bool cameraPermissionGranted = false;
  bool photoPermissionsGranted = false;

  bool get permissionsGranted =>
      cameraPermissionGranted && photoPermissionsGranted;

  // Request permissions for camera and photos
  Future requestPermissions() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final permissionStatus =
          await (androidInfo.version.sdkInt <= 32
              ? Permission.storage.request()
              : Permission.photos.request());
      photoPermissionsGranted =
          permissionStatus.isGranted || permissionStatus.isLimited;
    } else if (Platform.isIOS) {
      final iosPermission = await Permission.photos.request();
      photoPermissionsGranted = iosPermission.isGranted;
      if (iosPermission.isPermanentlyDenied) {
        openAppSettings();
      }
      // photoPermissionsGranted = await Permission.photos.request().isGranted;
      // print(iosPermission);
    }

    cameraPermissionGranted = await Permission.camera.request().isGranted;
    // print(cameraPermissionGranted);
  }
}
