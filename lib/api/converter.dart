import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

class UTCDateTimeConverter implements JsonConverter<DateTime, String> {
  const UTCDateTimeConverter();

  @override
  DateTime fromJson(String json) => DateTime.parse(json);

  @override
  String toJson(DateTime object) =>
      object.copyWith(microsecond: 0).toUtc().toIso8601String();
}

class EpochDateTimeConverter implements JsonConverter<DateTime, int> {
  const EpochDateTimeConverter();

  @override
  DateTime fromJson(int json) => DateTime.fromMillisecondsSinceEpoch(json);

  @override
  int toJson(DateTime object) => object.millisecondsSinceEpoch;
}

class EpochSecondDateTimeConverter implements JsonConverter<DateTime, int> {
  const EpochSecondDateTimeConverter();

  @override
  DateTime fromJson(int json) =>
      DateTime.fromMillisecondsSinceEpoch(json * 1000);

  @override
  int toJson(DateTime object) => (object.millisecondsSinceEpoch / 1000).round();
}

class DateOnlyConverter implements JsonConverter<DateTime, String> {
  const DateOnlyConverter();

  static const String pattern = 'yyyy-MM-dd';

  @override
  DateTime fromJson(String json) => DateFormat(pattern).parse(json);

  @override
  String toJson(DateTime date) => DateFormat(pattern).format(date);
}

class MonthOnlyConverter implements JsonConverter<DateTime, String> {
  const MonthOnlyConverter();

  static const String pattern = 'MM-yyyy';

  @override
  DateTime fromJson(String json) => DateFormat(pattern).parse(json);

  @override
  String toJson(DateTime date) => DateFormat(pattern).format(date);
}

class IntStringConverter implements JsonConverter<int, dynamic> {
  final bool toJsonString;

  const IntStringConverter({this.toJsonString = false});

  @override
  int fromJson(dynamic json) {
    if (json == null) return 0;
    if (json is int) return json;
    return int.tryParse(json) ?? 0;
  }

  @override
  dynamic toJson(int value) {
    return toJsonString ? value.toString : value;
  }
}
