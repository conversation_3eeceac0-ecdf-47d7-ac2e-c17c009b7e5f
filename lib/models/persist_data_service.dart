import 'dart:async';

import 'package:ai_vanse/models/user_diet_data_model.dart';
import 'package:ai_vanse/models/user_profile_data_model.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class PersistDataService {
  PersistDataService._privateConstructor();

  static final PersistDataService _instance =
      PersistDataService._privateConstructor();

  factory PersistDataService() {
    return _instance;
  }

  late Database _database;

  Future<void> initialize() async {
    _database = await openDatabase(
      join(await getDatabasesPath(), 'user_diet_database.db'),
      onConfigure: (db) {},
      onCreate: (db, version) async {
        await db.execute(createDietTableString);
        await db.execute(createUserProfileTableString);
        debugPrint('finished all db table init');
        return;
      },
      version: 1,
    );
  }

  Future<int> insertDiet(Map<String, Object?> row) async {
    return await _database.insert('diets', row).catchError((err) {
      debugPrint(err);
      return 0;
    });
  }

  Future<List<Map<String, Object?>>> queryAllDiet() async {
    return await _database.query('diets', orderBy: 'timestamp DESC');
  }

  // Query a specific diet day, input date must be in 'yyyy-MM-dd' format
  Future<List<Map<String, Object?>>> queryDietDay(String date) async {
    DateTime today = DateFormat('yyyy-MM-dd').parse(date);
    DateTime tomorrow = today.add(const Duration(days: 1));
    final List<Map<String, Object?>> maps = await _database.query(
      'diets',
      where: 'timestamp BETWEEN ? AND ?',
      whereArgs: [
        today.millisecondsSinceEpoch,
        tomorrow.millisecondsSinceEpoch - 1,
      ],
    );
    return maps;
  }

  Future<Map<String, Object?>> queryDiet(int timestamp) async {
    final List<Map<String, Object?>> maps = await _database.query(
      'diets',
      where: 'timestamp = ?',
      whereArgs: [timestamp],
    );
    return maps.isEmpty ? {} : maps[0];
  }

  Future<int> updateDiet(Map<String, Object?> row) async {
    return await _database
        .update(
          'diets',
          row,
          where: 'timestamp = ?',
          whereArgs: [row['timestamp']],
        )
        .catchError((err) {
          debugPrint(err);
          return 0;
        });
  }

  Future<int> deleteDiet(int timestamp) async {
    return await _database.delete(
      'diets',
      where: 'timestamp = ?',
      whereArgs: [timestamp],
    );
  }

  // User profile methods
  Future<int> insertUserProfile(Map<String, Object?> userProfile) async {
    return await _database.insert('profiles', userProfile).catchError((err) {
      debugPrint(err);
      return 0;
    });
  }

  Future<List<Map<String, Object?>>> queryAllUserProfiles() async {
    return await _database.query('profiles', orderBy: 'id ASC');
  }

  Future<Map<String, Object?>> queryUserProfile(int id) async {
    final List<Map<String, Object?>> maps = await _database.query(
      'profiles',
      where: 'id = ?',
      whereArgs: [id],
    );
    return maps.isEmpty ? {} : maps[0];
  }

  Future<int> updateUserProfile(Map<String, Object?> userProfile) async {
    return await _database
        .update(
          'profiles',
          userProfile,
          where: 'id = ?',
          whereArgs: [userProfile['id']],
        )
        .catchError((err) {
          debugPrint(err);
          return 0;
        });
  }

  Future<int> deleteUserProfile(int id) async {
    return await _database.delete('profiles', where: 'id = ?', whereArgs: [id]);
  }
}
