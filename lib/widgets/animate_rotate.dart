import 'package:flutter/widgets.dart';

class InfiniteAnimation extends StatefulWidget {
  final Widget child;
  final int durationInSeconds;

  const InfiniteAnimation({
    super.key,
    required this.child,
    this.durationInSeconds = 2,
  });

  @override
  InfiniteAnimationState createState() => InfiniteAnimationState();
}

class InfiniteAnimationState extends State<InfiniteAnimation>
    with SingleTickerProviderStateMixin {
  late final AnimationController animationController = AnimationController(
    vsync: this,
    duration: Duration(seconds: widget.durationInSeconds),
  );
  late final Animation<double> animation = Tween<double>(
    begin: 0,
    end: 12.5664, // 2Radians (360 degrees)
  ).animate(animationController);

  @override
  void initState() {
    super.initState();

    animationController.forward();
    animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        animationController.repeat();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController,
      builder:
          (context, child) =>
              Transform.rotate(angle: animation.value, child: widget.child),
    );
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }
}
