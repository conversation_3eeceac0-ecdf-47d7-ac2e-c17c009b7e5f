import 'package:freezed_annotation/freezed_annotation.dart';

part 'renew_token_dto.freezed.dart';
part 'renew_token_dto.g.dart';

@freezed
abstract class RenewTokenDto with _$RenewTokenDto {
  const factory RenewTokenDto({
    int? id,
    String? user,
    String? role,
    String? token,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'IdToken') String? idToken,
  }) = _RenewTokenDto;

  factory RenewTokenDto.fromJson(Map<String, dynamic> json) =>
      _$RenewTokenDtoFromJson(json);
}
