// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'patient.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Patient {

 int? get id; String? get firstname; String? get lastname; String? get firstnameEn; String? get lastnameEn; String? get hn; int? get age; DateTime? get dob; Gender? get gender; String? get status; String? get bloodGroup; String? get address; String? get phone; String? get homeAddressId; DateTime? get createdDate; DateTime? get modifiedDate; DateTime? get deletedDate; double? get weight; int? get height; String? get note; String? get imgProfileUrl;
/// Create a copy of Patient
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PatientCopyWith<Patient> get copyWith => _$PatientCopyWithImpl<Patient>(this as Patient, _$identity);

  /// Serializes this Patient to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Patient&&(identical(other.id, id) || other.id == id)&&(identical(other.firstname, firstname) || other.firstname == firstname)&&(identical(other.lastname, lastname) || other.lastname == lastname)&&(identical(other.firstnameEn, firstnameEn) || other.firstnameEn == firstnameEn)&&(identical(other.lastnameEn, lastnameEn) || other.lastnameEn == lastnameEn)&&(identical(other.hn, hn) || other.hn == hn)&&(identical(other.age, age) || other.age == age)&&(identical(other.dob, dob) || other.dob == dob)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.status, status) || other.status == status)&&(identical(other.bloodGroup, bloodGroup) || other.bloodGroup == bloodGroup)&&(identical(other.address, address) || other.address == address)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.homeAddressId, homeAddressId) || other.homeAddressId == homeAddressId)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate)&&(identical(other.modifiedDate, modifiedDate) || other.modifiedDate == modifiedDate)&&(identical(other.deletedDate, deletedDate) || other.deletedDate == deletedDate)&&(identical(other.weight, weight) || other.weight == weight)&&(identical(other.height, height) || other.height == height)&&(identical(other.note, note) || other.note == note)&&(identical(other.imgProfileUrl, imgProfileUrl) || other.imgProfileUrl == imgProfileUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,firstname,lastname,firstnameEn,lastnameEn,hn,age,dob,gender,status,bloodGroup,address,phone,homeAddressId,createdDate,modifiedDate,deletedDate,weight,height,note,imgProfileUrl]);

@override
String toString() {
  return 'Patient(id: $id, firstname: $firstname, lastname: $lastname, firstnameEn: $firstnameEn, lastnameEn: $lastnameEn, hn: $hn, age: $age, dob: $dob, gender: $gender, status: $status, bloodGroup: $bloodGroup, address: $address, phone: $phone, homeAddressId: $homeAddressId, createdDate: $createdDate, modifiedDate: $modifiedDate, deletedDate: $deletedDate, weight: $weight, height: $height, note: $note, imgProfileUrl: $imgProfileUrl)';
}


}

/// @nodoc
abstract mixin class $PatientCopyWith<$Res>  {
  factory $PatientCopyWith(Patient value, $Res Function(Patient) _then) = _$PatientCopyWithImpl;
@useResult
$Res call({
 int? id, String? firstname, String? lastname, String? firstnameEn, String? lastnameEn, String? hn, int? age, DateTime? dob, Gender? gender, String? status, String? bloodGroup, String? address, String? phone, String? homeAddressId, DateTime? createdDate, DateTime? modifiedDate, DateTime? deletedDate, double? weight, int? height, String? note, String? imgProfileUrl
});




}
/// @nodoc
class _$PatientCopyWithImpl<$Res>
    implements $PatientCopyWith<$Res> {
  _$PatientCopyWithImpl(this._self, this._then);

  final Patient _self;
  final $Res Function(Patient) _then;

/// Create a copy of Patient
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? firstname = freezed,Object? lastname = freezed,Object? firstnameEn = freezed,Object? lastnameEn = freezed,Object? hn = freezed,Object? age = freezed,Object? dob = freezed,Object? gender = freezed,Object? status = freezed,Object? bloodGroup = freezed,Object? address = freezed,Object? phone = freezed,Object? homeAddressId = freezed,Object? createdDate = freezed,Object? modifiedDate = freezed,Object? deletedDate = freezed,Object? weight = freezed,Object? height = freezed,Object? note = freezed,Object? imgProfileUrl = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,firstname: freezed == firstname ? _self.firstname : firstname // ignore: cast_nullable_to_non_nullable
as String?,lastname: freezed == lastname ? _self.lastname : lastname // ignore: cast_nullable_to_non_nullable
as String?,firstnameEn: freezed == firstnameEn ? _self.firstnameEn : firstnameEn // ignore: cast_nullable_to_non_nullable
as String?,lastnameEn: freezed == lastnameEn ? _self.lastnameEn : lastnameEn // ignore: cast_nullable_to_non_nullable
as String?,hn: freezed == hn ? _self.hn : hn // ignore: cast_nullable_to_non_nullable
as String?,age: freezed == age ? _self.age : age // ignore: cast_nullable_to_non_nullable
as int?,dob: freezed == dob ? _self.dob : dob // ignore: cast_nullable_to_non_nullable
as DateTime?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,bloodGroup: freezed == bloodGroup ? _self.bloodGroup : bloodGroup // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,homeAddressId: freezed == homeAddressId ? _self.homeAddressId : homeAddressId // ignore: cast_nullable_to_non_nullable
as String?,createdDate: freezed == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime?,modifiedDate: freezed == modifiedDate ? _self.modifiedDate : modifiedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedDate: freezed == deletedDate ? _self.deletedDate : deletedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,weight: freezed == weight ? _self.weight : weight // ignore: cast_nullable_to_non_nullable
as double?,height: freezed == height ? _self.height : height // ignore: cast_nullable_to_non_nullable
as int?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,imgProfileUrl: freezed == imgProfileUrl ? _self.imgProfileUrl : imgProfileUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Patient extends Patient {
  const _Patient({this.id, this.firstname, this.lastname, this.firstnameEn, this.lastnameEn, this.hn, this.age, this.dob, this.gender, this.status, this.bloodGroup, this.address, this.phone, this.homeAddressId, this.createdDate, this.modifiedDate, this.deletedDate, this.weight, this.height, this.note, this.imgProfileUrl}): super._();
  factory _Patient.fromJson(Map<String, dynamic> json) => _$PatientFromJson(json);

@override final  int? id;
@override final  String? firstname;
@override final  String? lastname;
@override final  String? firstnameEn;
@override final  String? lastnameEn;
@override final  String? hn;
@override final  int? age;
@override final  DateTime? dob;
@override final  Gender? gender;
@override final  String? status;
@override final  String? bloodGroup;
@override final  String? address;
@override final  String? phone;
@override final  String? homeAddressId;
@override final  DateTime? createdDate;
@override final  DateTime? modifiedDate;
@override final  DateTime? deletedDate;
@override final  double? weight;
@override final  int? height;
@override final  String? note;
@override final  String? imgProfileUrl;

/// Create a copy of Patient
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PatientCopyWith<_Patient> get copyWith => __$PatientCopyWithImpl<_Patient>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PatientToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Patient&&(identical(other.id, id) || other.id == id)&&(identical(other.firstname, firstname) || other.firstname == firstname)&&(identical(other.lastname, lastname) || other.lastname == lastname)&&(identical(other.firstnameEn, firstnameEn) || other.firstnameEn == firstnameEn)&&(identical(other.lastnameEn, lastnameEn) || other.lastnameEn == lastnameEn)&&(identical(other.hn, hn) || other.hn == hn)&&(identical(other.age, age) || other.age == age)&&(identical(other.dob, dob) || other.dob == dob)&&(identical(other.gender, gender) || other.gender == gender)&&(identical(other.status, status) || other.status == status)&&(identical(other.bloodGroup, bloodGroup) || other.bloodGroup == bloodGroup)&&(identical(other.address, address) || other.address == address)&&(identical(other.phone, phone) || other.phone == phone)&&(identical(other.homeAddressId, homeAddressId) || other.homeAddressId == homeAddressId)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate)&&(identical(other.modifiedDate, modifiedDate) || other.modifiedDate == modifiedDate)&&(identical(other.deletedDate, deletedDate) || other.deletedDate == deletedDate)&&(identical(other.weight, weight) || other.weight == weight)&&(identical(other.height, height) || other.height == height)&&(identical(other.note, note) || other.note == note)&&(identical(other.imgProfileUrl, imgProfileUrl) || other.imgProfileUrl == imgProfileUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,firstname,lastname,firstnameEn,lastnameEn,hn,age,dob,gender,status,bloodGroup,address,phone,homeAddressId,createdDate,modifiedDate,deletedDate,weight,height,note,imgProfileUrl]);

@override
String toString() {
  return 'Patient(id: $id, firstname: $firstname, lastname: $lastname, firstnameEn: $firstnameEn, lastnameEn: $lastnameEn, hn: $hn, age: $age, dob: $dob, gender: $gender, status: $status, bloodGroup: $bloodGroup, address: $address, phone: $phone, homeAddressId: $homeAddressId, createdDate: $createdDate, modifiedDate: $modifiedDate, deletedDate: $deletedDate, weight: $weight, height: $height, note: $note, imgProfileUrl: $imgProfileUrl)';
}


}

/// @nodoc
abstract mixin class _$PatientCopyWith<$Res> implements $PatientCopyWith<$Res> {
  factory _$PatientCopyWith(_Patient value, $Res Function(_Patient) _then) = __$PatientCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? firstname, String? lastname, String? firstnameEn, String? lastnameEn, String? hn, int? age, DateTime? dob, Gender? gender, String? status, String? bloodGroup, String? address, String? phone, String? homeAddressId, DateTime? createdDate, DateTime? modifiedDate, DateTime? deletedDate, double? weight, int? height, String? note, String? imgProfileUrl
});




}
/// @nodoc
class __$PatientCopyWithImpl<$Res>
    implements _$PatientCopyWith<$Res> {
  __$PatientCopyWithImpl(this._self, this._then);

  final _Patient _self;
  final $Res Function(_Patient) _then;

/// Create a copy of Patient
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? firstname = freezed,Object? lastname = freezed,Object? firstnameEn = freezed,Object? lastnameEn = freezed,Object? hn = freezed,Object? age = freezed,Object? dob = freezed,Object? gender = freezed,Object? status = freezed,Object? bloodGroup = freezed,Object? address = freezed,Object? phone = freezed,Object? homeAddressId = freezed,Object? createdDate = freezed,Object? modifiedDate = freezed,Object? deletedDate = freezed,Object? weight = freezed,Object? height = freezed,Object? note = freezed,Object? imgProfileUrl = freezed,}) {
  return _then(_Patient(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,firstname: freezed == firstname ? _self.firstname : firstname // ignore: cast_nullable_to_non_nullable
as String?,lastname: freezed == lastname ? _self.lastname : lastname // ignore: cast_nullable_to_non_nullable
as String?,firstnameEn: freezed == firstnameEn ? _self.firstnameEn : firstnameEn // ignore: cast_nullable_to_non_nullable
as String?,lastnameEn: freezed == lastnameEn ? _self.lastnameEn : lastnameEn // ignore: cast_nullable_to_non_nullable
as String?,hn: freezed == hn ? _self.hn : hn // ignore: cast_nullable_to_non_nullable
as String?,age: freezed == age ? _self.age : age // ignore: cast_nullable_to_non_nullable
as int?,dob: freezed == dob ? _self.dob : dob // ignore: cast_nullable_to_non_nullable
as DateTime?,gender: freezed == gender ? _self.gender : gender // ignore: cast_nullable_to_non_nullable
as Gender?,status: freezed == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as String?,bloodGroup: freezed == bloodGroup ? _self.bloodGroup : bloodGroup // ignore: cast_nullable_to_non_nullable
as String?,address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,phone: freezed == phone ? _self.phone : phone // ignore: cast_nullable_to_non_nullable
as String?,homeAddressId: freezed == homeAddressId ? _self.homeAddressId : homeAddressId // ignore: cast_nullable_to_non_nullable
as String?,createdDate: freezed == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime?,modifiedDate: freezed == modifiedDate ? _self.modifiedDate : modifiedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedDate: freezed == deletedDate ? _self.deletedDate : deletedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,weight: freezed == weight ? _self.weight : weight // ignore: cast_nullable_to_non_nullable
as double?,height: freezed == height ? _self.height : height // ignore: cast_nullable_to_non_nullable
as int?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,imgProfileUrl: freezed == imgProfileUrl ? _self.imgProfileUrl : imgProfileUrl // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
