import 'package:ai_vanse/screens/camera/controller/capture_controller.dart';
import 'package:ai_vanse/utilities/snackbar.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:camerawesome/pigeon.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CaptureScreen extends StatefulWidget {
  const CaptureScreen({super.key});

  @override
  State<CaptureScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CaptureScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  late final CaptureController controller = Get.find();

  static const List<IconData> flashIcons = [
    Icons.flash_off,
    Icons.flash_on,
    Icons.flash_auto,
  ];
  int selectedFlashModeIndex = 0;

  // Zoom, focus and exposure
  final int _pointers = 0;
  double _baseScale = 1.0;
  double _currentScale = 1.0;
  final double _minAvailableZoom = 1.0;
  final double _maxAvailableZoom = 1.0;

  // double _minAvailableExposureOffset = 0.0;
  // double _maxAvailableExposureOffset = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}

  void showInSnackBar(String message) {
    showSnackBar(context, message);
  }

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      title: 'Capture',
      body: _buildCameraPreviewWidget(context),
    );
  }

  Widget _buildCameraPreviewWidget(BuildContext context) {
    return CameraAwesomeBuilder.awesome(
      saveConfig: SaveConfig.photo(
        exifPreferences: ExifPreferences(saveGPSLocation: true),
      ),
      sensorConfig: SensorConfig.single(
        aspectRatio: CameraAspectRatios.ratio_1_1,
        sensor: Sensor.position(SensorPosition.back),
        zoom: 0.0,
      ),

      onMediaCaptureEvent: (MediaCapture event) {
        print(event.status);
      },
      middleContentBuilder: (state) => SizedBox.shrink(),
      previewFit: CameraPreviewFit.contain,

      bottomActionsBuilder:
          (state) => SizedBox(
            height: 100.sp,
            child: Center(child: _buildCameraCaptureButton(state)),
          ),
    );
  }

  Widget _buildCameraCaptureButton(CameraState state) {
    return state.when(
      onPreparingCamera:
          (state) => const Center(child: CircularProgressIndicator()),
      onPhotoMode: (PhotoCameraState cameraState) {
        return GestureDetector(
          onTap: () async {
            final CaptureRequest capture = await cameraState.takePhoto();
            capture.when(single: controller.onCapturePressed);
          },
          child: Stack(
            children: [
              Container(
                height: 40.sp,
                width: 40.sp,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(
                height: 40.sp,
                width: 40.sp,
                child: Center(
                  child: Container(
                    height: 39.sp,
                    width: 39.sp,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
