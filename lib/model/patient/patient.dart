import 'package:ai_vanse/model/patient/gender.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'patient.freezed.dart';
part 'patient.g.dart';

@freezed
abstract class Patient with _$Patient {
  const Patient._();

  const factory Patient({
    int? id,
    String? firstname,
    String? lastname,
    String? firstnameEn,
    String? lastnameEn,
    String? hn,
    int? age,
    DateTime? dob,
    Gender? gender,
    String? status,
    String? bloodGroup,
    String? address,
    String? phone,
    String? homeAddressId,
    DateTime? createdDate,
    DateTime? modifiedDate,
    DateTime? deletedDate,
    double? weight,
    int? height,
    String? note,
    String? imgProfileUrl,
  }) = _Patient;

  factory Patient.fromJson(dynamic json) => _$PatientFromJson(json);

  double? get bmr {
    if (gender != null && weight != null && height != null && age != null) {
      if (gender == Gender.male) {
        return (10 * weight!) + (6.25 * height!) - (5 * age!) + 5;
      } else if (gender == Gender.female) {
        return (10 * weight!) + (6.25 * height!) - (5 * age!) - 161;
      }
    }
    return null;
  }

  double? get bmi {
    if (weight != null && height != null) {
      final double heightInMeters = height! / 100;
      return weight! / (heightInMeters * heightInMeters);
    } else {
      return null;
    }
  }

  double? get goal {
    if (bmr != 0 || bmr != null) {
      return bmr! * 1.55;
    }
    return null;
  }
}
