import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TagDialog extends StatefulWidget {
  final List<String> defaultTags;
  final List<String> selectedTags;
  final Function(List<String>) onTagsSelected;

  const TagDialog({
    Key? key,
    required this.defaultTags,
    required this.selectedTags,
    required this.onTagsSelected,
  }) : super(key: key);

  @override
  State<TagDialog> createState() => _TagDialogState();
}

class _TagDialogState extends State<TagDialog> {
  late List<String> tempSelectedTags;
  final TextEditingController newTagController = TextEditingController();

  @override
  void initState() {
    super.initState();
    tempSelectedTags = List.from(widget.selectedTags);
  }

  Widget _buildTagChip(
    String tag, {
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20.sp),
        ),
        child: Text(
          '# $tag',
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontSize: 14.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildAddTagInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 8.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Text(
            '#',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black,
            ),
          ),
          SizedBox(width: 8.sp),
          Expanded(
            child: TextField(
              controller: newTagController,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: 'Add new tag...',
                contentPadding: EdgeInsets.zero,
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  setState(() {
                    if (!widget.defaultTags.contains(value)) {
                      widget.defaultTags.add(value);
                      tempSelectedTags.add(value);
                    }
                  });
                  newTagController.clear();
                }
              },
            ),
          ),
          IconButton(
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(),
            icon: Icon(Icons.add, color: Colors.blue),
            onPressed: () {
              if (newTagController.text.isNotEmpty) {
                setState(() {
                  if (!widget.defaultTags.contains(newTagController.text)) {
                    widget.defaultTags.add(newTagController.text);
                    tempSelectedTags.add(newTagController.text);
                  }
                });
                newTagController.clear();
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Material(
        color: Colors.transparent,
        child: Container(
          height: 245.sp,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(10.sp)),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'Back',
                          style: TextStyle(color: Colors.blue, fontSize: 16),
                        ),
                      ),
                      Text(
                        'Add a Tag',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          widget.onTagsSelected(tempSelectedTags);
                          Navigator.pop(context);
                        },
                        child: Text(
                          'OK',
                          style: TextStyle(color: Colors.blue, fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                ),
                // Tags List
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(16.sp),
                    child: Wrap(
                      spacing: 8.sp,
                      runSpacing: 12.sp,
                      children: widget.defaultTags.map((tag) {
                        return _buildTagChip(
                          tag,
                          isSelected: tempSelectedTags.contains(tag),
                          onTap: () {
                            setState(() {
                              if (tempSelectedTags.contains(tag)) {
                                tempSelectedTags.remove(tag);
                              } else {
                                tempSelectedTags.add(tag);
                              }
                            });
                          },
                        );
                      }).toList(),
                    ),
                  ),
                ),
                // Add Tag Input
                _buildAddTagInput(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    newTagController.dispose();
    super.dispose();
  }
}
