import 'package:ai_vanse/model/patient/patient.dart';
import 'package:ai_vanse/repositories/user_repository.dart';
import 'package:ai_vanse/services/dialog_service.dart';
import 'package:get/get.dart';

class UserInfoController extends GetxController {
  late final UserRepository _userRepository = Get.find();
  late final DialogService _dialogService = Get.find();

  final _patientInfo = Rxn<Patient>();
  final _bmrValue = RxDouble(0);
  final _bmiValue = RxDouble(0);
  final _displayGender = RxString('');

  Patient? get patientInfo => _patientInfo.value;

  String get fullname => '${patientInfo?.firstname} ${patientInfo?.lastname}';

  double? get weight => patientInfo?.weight;

  int? get height => patientInfo?.height;

  int? get age => patientInfo?.age;

  double? get bmrValue => _bmrValue.value;

  double? get bmiValue => _bmiValue.value;

  DateTime? get dob => patientInfo?.dob;

  String get displayGender => _displayGender.value;

  @override
  void onReady() {
    super.onReady();
    _fetchPatient();
  }

  void _setDisplayGender() {
    _displayGender.value = patientInfo?.gender?.tr ?? '-';
  }

  Future<void> _fetchPatient() async {
    try {
      final patient = _userRepository.patient;

      _patientInfo.value = patient;
      _bmiValue.value = patient.bmi ?? 0;
      _bmrValue.value = patient.bmr ?? 0;
      _setDisplayGender();
    } catch (e) {
      printError(info: 'Error fetching patient: $e');
      _bmrValue.value = 0;
    }
  }

  void showBmrInfo() {}

  void showBmiInfo() {}
}
