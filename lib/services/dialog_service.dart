import 'package:ai_vanse/extensions/control_flow_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/widgets/app_button.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

enum OkCancelResult { ok, cancel }

class DialogService extends GetxService {
  Widget adaptiveAction({
    required VoidCallback onPressed,
    required Widget child,
    bool isDestructiveAction = false,
  }) {
    switch (Get.theme.platform) {
      case TargetPlatform.android:
      case TargetPlatform.fuchsia:
      case TargetPlatform.linux:
      case TargetPlatform.windows:
        return TextButton(onPressed: onPressed, child: child);
      case TargetPlatform.iOS:
      case TargetPlatform.macOS:
        return CupertinoDialogAction(
          onPressed: onPressed,
          isDestructiveAction: isDestructiveAction,
          child: child,
        );
    }
  }

  Future<OkCancelResult?> showLimitedPermissionDialog(
    String limitedPermissionMessage,
  ) async {
    return showMessageDialog(
      title: 'no_permission_title'.tr,
      message: limitedPermissionMessage,
      okLabel: 'open_settings'.tr,
      cancelLabel: 'cancel'.tr,
    );
  }

  Future<OkCancelResult?> showNoPermissionDialog(
    String noPermissionMessage, {
    String? title,
  }) {
    return showMessageDialog(
      title: title ?? 'no_permission_title'.tr,
      message: noPermissionMessage,
      okLabel: 'open_settings'.tr,
      cancelLabel: 'cancel'.tr,
    );
  }

  Future<OkCancelResult?> showMessageDialog({
    String? title,
    Widget? titleWidget,
    String? message,
    Widget? messageWidget,
    String? okLabel,
    String? cancelLabel,
    bool barrierDismissible = true,
  }) {
    return Get.dialog(
      barrierDismissible: barrierDismissible,
      AlertDialog.adaptive(
        title: titleWidget ?? title?.let(Text.new),
        titleTextStyle: AppTextStyle.h6.textStyle.copyWith(
          color: Get.theme.extraColors.defaultTextColor,
        ),
        content: messageWidget ?? message?.let(Text.new),
        contentTextStyle: AppTextStyle.bodySmall.textStyle.copyWith(
          color: Get.theme.extraColors.defaultTextColor,
        ),
        actions: <Widget>[
          if (cancelLabel != null)
            adaptiveAction(
              onPressed: () => Get.back(result: OkCancelResult.cancel),
              isDestructiveAction: true,
              child: Text(
                cancelLabel,
                style: AppTextStyle.titleSmall.textStyle,
              ),
            ),
          if (okLabel != null)
            adaptiveAction(
              onPressed: () => Get.back(result: OkCancelResult.ok),
              child: Text(okLabel, style: AppTextStyle.titleSmall.textStyle),
            ),
        ],
      ),
    );
  }

  Future<String> showCupertinoPickerDialog({
    required BuildContext context,
    required List<String> items,
    required int initialIndex,
    double heightFactor = 0.4,
  }) async {
    int selectedIndex = initialIndex;
    final screenHeight = MediaQuery.of(context).size.height;
    final pickerHeight = screenHeight * heightFactor;

    final result = await showCupertinoModalPopup<String>(
      context: context,
      builder:
          (_) => StatefulBuilder(
            builder:
                (context, setState) => Container(
                  padding: EdgeInsets.all(26.sp),
                  height: pickerHeight,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 36.sp,
                          scrollController: FixedExtentScrollController(
                            initialItem: initialIndex,
                          ),
                          onSelectedItemChanged: (index) {
                            selectedIndex = index;
                          },
                          children:
                              items
                                  .map((item) => Center(child: Text(item)))
                                  .toList(),
                        ),
                      ),
                      SizedBox(height: 10.sp),
                      SizedBox(
                        height: 42.sp,
                        width: 107.sp,
                        child: FittedBox(
                          child: Center(
                            child: AppButton(
                              label: 'common_cta_confirm'.tr,
                              onPressed: () {
                                Navigator.of(context).pop(items[selectedIndex]);
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );

    return result ?? items[selectedIndex];
  }
}
