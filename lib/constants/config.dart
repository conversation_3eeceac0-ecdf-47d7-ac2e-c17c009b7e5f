import 'package:ai_vanse/constants/env/env.dart';

class AppConfig {
  static void init(Env env) {
    _env = env;
  }

  static late Env _env;

  static String get imgbbApiKey => _env.imgbbApiKey;

  static String get imgbbUploadEndpoint => _env.imgbbUploadEndpoint;

  static String get aiServiceApiEndpoint => _env.aiServiceApiEndpoint;

  static String get translateServiceApiEndpoint =>
      _env.translateServiceApiEndpoint;

  static String get authServiceApiEndpoint => _env.authServiceApiEndpoint;
}
