// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'available_date.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AvailableDate {

 List<DateTime>? get availableDate;
/// Create a copy of AvailableDate
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AvailableDateCopyWith<AvailableDate> get copyWith => _$AvailableDateCopyWithImpl<AvailableDate>(this as AvailableDate, _$identity);

  /// Serializes this AvailableDate to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AvailableDate&&const DeepCollectionEquality().equals(other.availableDate, availableDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(availableDate));

@override
String toString() {
  return 'AvailableDate(availableDate: $availableDate)';
}


}

/// @nodoc
abstract mixin class $AvailableDateCopyWith<$Res>  {
  factory $AvailableDateCopyWith(AvailableDate value, $Res Function(AvailableDate) _then) = _$AvailableDateCopyWithImpl;
@useResult
$Res call({
 List<DateTime>? availableDate
});




}
/// @nodoc
class _$AvailableDateCopyWithImpl<$Res>
    implements $AvailableDateCopyWith<$Res> {
  _$AvailableDateCopyWithImpl(this._self, this._then);

  final AvailableDate _self;
  final $Res Function(AvailableDate) _then;

/// Create a copy of AvailableDate
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? availableDate = freezed,}) {
  return _then(_self.copyWith(
availableDate: freezed == availableDate ? _self.availableDate : availableDate // ignore: cast_nullable_to_non_nullable
as List<DateTime>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AvailableDate implements AvailableDate {
  const _AvailableDate({final  List<DateTime>? availableDate}): _availableDate = availableDate;
  factory _AvailableDate.fromJson(Map<String, dynamic> json) => _$AvailableDateFromJson(json);

 final  List<DateTime>? _availableDate;
@override List<DateTime>? get availableDate {
  final value = _availableDate;
  if (value == null) return null;
  if (_availableDate is EqualUnmodifiableListView) return _availableDate;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}


/// Create a copy of AvailableDate
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AvailableDateCopyWith<_AvailableDate> get copyWith => __$AvailableDateCopyWithImpl<_AvailableDate>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AvailableDateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AvailableDate&&const DeepCollectionEquality().equals(other._availableDate, _availableDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_availableDate));

@override
String toString() {
  return 'AvailableDate(availableDate: $availableDate)';
}


}

/// @nodoc
abstract mixin class _$AvailableDateCopyWith<$Res> implements $AvailableDateCopyWith<$Res> {
  factory _$AvailableDateCopyWith(_AvailableDate value, $Res Function(_AvailableDate) _then) = __$AvailableDateCopyWithImpl;
@override @useResult
$Res call({
 List<DateTime>? availableDate
});




}
/// @nodoc
class __$AvailableDateCopyWithImpl<$Res>
    implements _$AvailableDateCopyWith<$Res> {
  __$AvailableDateCopyWithImpl(this._self, this._then);

  final _AvailableDate _self;
  final $Res Function(_AvailableDate) _then;

/// Create a copy of AvailableDate
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? availableDate = freezed,}) {
  return _then(_AvailableDate(
availableDate: freezed == availableDate ? _self._availableDate : availableDate // ignore: cast_nullable_to_non_nullable
as List<DateTime>?,
  ));
}


}

// dart format on
