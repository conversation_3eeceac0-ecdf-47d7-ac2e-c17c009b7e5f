import 'package:ai_vanse/api/dio/dio_client.dart';
import 'package:ai_vanse/api/dto/login_request_dto.dart';
import 'package:ai_vanse/api/dto/login_response_dto.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'login_api.g.dart';

@RestApi()
abstract class LoginApi {
  factory LoginApi() => _LoginApi(DioClient.getInstance(authenticated: false));

  @POST('/v3/api/auth/login')
  Future<HttpResponse<LoginResponseDto>> loginV3(
    @Body() LoginRequestDto request,
  );
}
