// import 'package:ai_vanse/models/persist_data_service.dart';
// import 'package:ai_vanse/models/user_profile_data_model.dart';
// import 'package:ai_vanse/models/user_profile_service.dart';
// import 'package:ai_vanse/utilities/event_bus.dart';
// import 'package:ai_vanse/utilities/events.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
//
// class PersonalInfoScreen extends StatefulWidget {
//   const PersonalInfoScreen({super.key});
//
//   @override
//   State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
// }
//
// class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
//   // Variables to hold user input
//   String name = '';
//   String email = '';
//   int age = 25;
//   double weight = 70.0; // in kg
//   double height = 175.0; // in cm
//   bool hasChanges = false;
//
//   final _formKey = GlobalKey<FormState>(); // Form key for validation
//
//   @override
//   void initState() {
//     super.initState();
//     _loadPersonalInfo();
//   }
//
//   void _loadPersonalInfo() {
//     var personalInfo = UserProfileService().getPersonalInfo();
//     setState(() {
//       name = personalInfo['name'];
//       email = personalInfo['email'];
//       age = personalInfo['age'];
//       weight = personalInfo['weight'];
//       height = personalInfo['height'];
//     });
//   }
//
//   void onNameChanged(String value) {
//     setState(() {
//       name = value;
//       hasChanges = true;
//     });
//   }
//
//   void onEmailChanged(String value) {
//     setState(() {
//       email = value;
//       hasChanges = true;
//     });
//   }
//
//   void onAgeChanged(String value) {
//     setState(() {
//       hasChanges = true;
//       var a = int.tryParse(value);
//       if (a != null) {
//         age = a;
//       }
//     });
//   }
//
//   void onWeightChanged(String value) {
//     setState(() {
//       hasChanges = true;
//       var w = double.tryParse(value);
//       if (w != null) {
//         weight = w;
//       }
//     });
//   }
//
//   void onHeightChanged(String value) {
//     setState(() {
//       hasChanges = true;
//       var h = double.tryParse(value);
//       if (h != null) {
//         height = h;
//       }
//     });
//   }
//
//   Future<void> _testUserProfileDb() async {
//     var dummyUser = UserProfile(
//       name: 'Tony',
//       email: '<EMAIL>',
//       age: 25,
//       weight: 70.0,
//       height: 175.0,
//       calorieLimit: 2000,
//       fatLimit: 70,
//       carbLimit: 300,
//       proteinLimit: 50,
//       fiberLimit: 30,
//       waterIntakeGoal: 2000,
//       sugarIntakeGoal: 50,
//       saltIntakeGoal: 6,
//       foodPreference: 0,
//       // 0 = no, 1 = Vegetarian, 2 = Vegan, 3 = Pescetarian
//       noAllergies: false,
//       glutenIntolerant: false,
//       wheatIntolerant: false,
//       lactoseIntolerant: false,
//       milkAllergy: false,
//       eggAllergy: false,
//       shellfishAllergy: false,
//       fishAllergy: false,
//       nutsAllergy: false,
//     );
//
//     debugPrint('-------- Testing 1 (Insert) --------');
//     int id = await PersistDataService().insertUserProfile(dummyUser.toMap());
//     debugPrint('Inserted a user entry with id: $id');
//
//     debugPrint('-------- Testing 2 (Duplicate Insert) --------');
//     int id2 = await PersistDataService().insertUserProfile(dummyUser.toMap());
//     debugPrint('Inserted a user entry with id: $id2');
//
//     debugPrint('-------- Testing 3 (query with non-existing id) --------');
//     var targetUser = await PersistDataService().queryUserProfile(id + 5);
//     debugPrint('target user: $targetUser');
//
//     debugPrint('-------- Testing 4 (query with existing id) --------');
//     targetUser = await PersistDataService().queryUserProfile(id);
//     debugPrint('target user: $targetUser');
//
//     debugPrint('-------- Testing 5 (update existing entry) --------');
//     var userCpy = Map.of(targetUser);
//     userCpy['food_preference'] = 1;
//     int updateCount = await PersistDataService().updateUserProfile(userCpy);
//     debugPrint('Updated $updateCount entries');
//     var updatedUser = await PersistDataService().queryUserProfile(id);
//     debugPrint('updated user: $updatedUser');
//
//     debugPrint('-------- Testing 6 (update non-existing entry) --------');
//     userCpy['id'] = (id + 3).toString();
//     updateCount = await PersistDataService().updateUserProfile(userCpy);
//     debugPrint('Updated $updateCount entries');
//
//     debugPrint('-------- Testing 7 (delete non-existing entry) --------');
//     int deleteCount = await PersistDataService().deleteUserProfile(id + 9);
//     debugPrint('Deleted $deleteCount entries');
//
//     debugPrint('-------- Testing 8 (delete existing entry) --------');
//     deleteCount = await PersistDataService().deleteUserProfile(id);
//     debugPrint('Deleted $deleteCount entries');
//
//     debugPrint(
//       '-------- Testing 9 (retrieve & cast to userProfile object) --------',
//     );
//     id = await PersistDataService().insertUserProfile(dummyUser.toMap());
//     targetUser = await PersistDataService().queryUserProfile(id);
//     var userObj = UserProfile.fromMap(targetUser);
//     debugPrint('User object: ${userObj.toMap()}');
//     await PersistDataService().deleteUserProfile(id);
//   }
//
//   void _savePersonalInfoChanges() {
//     UserProfileService().handlePersonalInfoChanged(
//       name,
//       email,
//       age,
//       weight,
//       height,
//     );
//     setState(() {
//       hasChanges = false;
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         FocusScope.of(context).unfocus();
//       },
//       child: Scaffold(
//         appBar: AppBar(title: Text(AppLocalizations.of(context)!.personalInfo)),
//         body: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Form(
//             key: _formKey,
//             child: ListView(
//               children: [
//                 buildProfileField(
//                   AppLocalizations.of(context)!.name,
//                   name,
//                   onNameChanged,
//                 ),
//                 buildProfileField(
//                   AppLocalizations.of(context)!.email,
//                   email,
//                   onEmailChanged,
//                 ),
//                 buildNumericField(
//                   AppLocalizations.of(context)!.age,
//                   age.toString(),
//                   onAgeChanged,
//                 ),
//                 buildNumericField(
//                   AppLocalizations.of(context)!.weight,
//                   weight.toString(),
//                   onWeightChanged,
//                   isDouble: true,
//                 ),
//                 buildNumericField(
//                   AppLocalizations.of(context)!.height,
//                   height.toString(),
//                   onHeightChanged,
//                   isDouble: true,
//                 ),
//                 Center(
//                   child: ElevatedButton(
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: Colors.teal,
//                       padding: EdgeInsets.symmetric(
//                         horizontal: 32,
//                         vertical: 16,
//                       ),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(30),
//                       ),
//                     ),
//                     onPressed:
//                         hasChanges && _formKey.currentState!.validate()
//                             ? () {
//                               {
//                                 _savePersonalInfoChanges();
//                                 ScaffoldMessenger.of(context).showSnackBar(
//                                   SnackBar(
//                                     content: Text(
//                                       AppLocalizations.of(
//                                         context,
//                                       )!.profileUpdated,
//                                     ),
//                                   ),
//                                 );
//                                 eventBus.fire(
//                                   ProfileInfoBtnTriggerEmit(hasChanges),
//                                 );
//                               }
//                             }
//                             : null,
//                     child: Text(
//                       AppLocalizations.of(context)!.saveChange,
//                       style: TextStyle(fontSize: 18),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget buildProfileField(
//     String label,
//     String value,
//     Function(String) onChanged,
//   ) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 8.0),
//       child: TextFormField(
//         initialValue: value,
//         enableSuggestions: false,
//         decoration: InputDecoration(
//           labelText: label,
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(10),
//             borderSide: BorderSide(color: Colors.teal),
//           ),
//           focusedBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(10),
//             borderSide: BorderSide(color: Colors.teal, width: 2),
//           ),
//           filled: true,
//           fillColor: Colors.teal.shade50,
//         ),
//         onChanged: onChanged,
//         validator: (value) {
//           if (value == null || value.isEmpty) {
//             return 'Please enter your $label';
//           }
//           return null;
//         },
//       ),
//     );
//   }
//
//   Widget buildNumericField(
//     String label,
//     String value,
//     Function(String) onChanged, {
//     bool isDouble = false,
//   }) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 8.0),
//       child: TextFormField(
//         initialValue: value,
//         enableSuggestions: false,
//         decoration: InputDecoration(
//           labelText: label,
//           border: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(10),
//             borderSide: BorderSide(color: Colors.teal),
//           ),
//           focusedBorder: OutlineInputBorder(
//             borderRadius: BorderRadius.circular(10),
//             borderSide: BorderSide(color: Colors.teal, width: 2),
//           ),
//           filled: true,
//           fillColor: Colors.teal.shade50,
//         ),
//         onChanged: onChanged,
//         inputFormatters: [
//           FilteringTextInputFormatter.allow(
//             isDouble ? RegExp('[0-9.]') : RegExp('[0-9]'),
//           ),
//         ],
//         validator: (value) {
//           if (value == null ||
//               value.isEmpty ||
//               (isDouble ? double.tryParse(value) : int.tryParse(value)) ==
//                   null) {
//             return 'Please enter your $label';
//           }
//           return null;
//         },
//       ),
//     );
//   }
// }
