import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/screens/home/<USER>/home_controller.dart';
import 'package:ai_vanse/screens/home/<USER>/food_post.dart';
import 'package:ai_vanse/screens/home/<USER>/food_story_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);

  String getInitials(String name) {
    if (name.isEmpty) return '';
    final names = name.split(' ');
    return names[0][0].toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _buildTopBar(),
            _buildSearchBar(),
            _buildNavigationTabs(),
            Expanded(
              child: Obx(
                () =>
                    controller.selectedMainTab.value == 0
                        ? Column(
                          children: [
                            SizedBox(height: 20.sp),
                            _buildSubTabs(),
                            Expanded(child: _buildFoodGrid()),
                          ],
                        )
                        : const FoodStoryView(),
              ),
            ),
          ],
        ),
      ),
      // bottomNavigationBar: _buildBottomNavBar(),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(child: SizedBox()),
          Expanded(
            child: Center(
              child: Text(
                'HOME',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: CircleAvatar(
                radius: 20,
                backgroundColor: Colors.green,
                child: Text(
                  getInitials(controller.userName.value),
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(30),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.grey[200],
        ),
      ),
    );
  }

  Widget _buildNavigationTabs() {
    return Obx(
      () => Column(
        children: [
          Row(
            children: [_buildTab('Food Choice', 0), _buildTab('Food Story', 1)],
          ),
          Container(
            height: 2,
            color: Colors.green,
            margin: EdgeInsets.only(
              left:
                  MediaQuery.of(Get.context!).size.width *
                  (controller.selectedMainTab.value == 0 ? 0 : 0.5),
              right:
                  MediaQuery.of(Get.context!).size.width *
                  (controller.selectedMainTab.value == 0 ? 0.5 : 0),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    return Expanded(
      child: GestureDetector(
        onTap: () => controller.selectedMainTab.value = index,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          color: Colors.transparent,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color:
                  controller.selectedMainTab.value == index
                      ? Colors.green
                      : Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubTabs() {
    return Obx(
      () => SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            _buildSubTab('For You', 0),
            _buildSubTab('Nearby', 1),
            _buildSubTab('Friends', 2),
            _buildSubTab('Featured', 3),
          ],
        ),
      ),
    );
  }

  Widget _buildSubTab(String title, int index) {
    final isSelected = controller.selectedSubTab.value == index;
    return GestureDetector(
      onTap: () => controller.selectedSubTab.value = index,
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: isSelected ? Colors.green : Colors.grey),
        ),
        child: Text(
          title,
          style: TextStyle(color: isSelected ? Colors.white : Colors.grey),
        ),
      ),
    );
  }

  Widget _buildFoodGrid() {
    return Obx(
      () => GridView.builder(
        padding: EdgeInsets.all(8.sp),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 176 / 202,
          crossAxisSpacing: 8.sp,
          mainAxisSpacing: 8.sp,
        ),
        itemCount: controller.foodPosts.length,
        itemBuilder: (context, index) {
          final post = controller.foodPosts[index];
          return _buildFoodCard(post);
        },
      ),
    );
  }

  Widget _buildFoodCard(FoodPost post) {
    return GestureDetector(
      onTap: () => Get.toNamed(AppRoutes.foodChoiceDetail, arguments: post),
      child: Card(
        elevation: 4,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(12.r),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1.2,
                    child: Image.network(post.imageUrl, fit: BoxFit.cover),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(8.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: List.generate(
                          post.rating,
                          (index) => Padding(
                            padding: EdgeInsets.only(right: 2.sp),
                            child: Image.asset(
                              Assets.images.rating.path,
                              width: 16.sp,
                              height: 16.sp,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 4.sp),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Calories: ${post.nutrition.energy.toInt()}',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 12.sp,
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Dist.${post.distance} km',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 10.sp,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              top: 8.sp,
              left: 8.sp,
              right: 8.sp,
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 16.sp,
                    backgroundColor: Colors.green,
                    child: Text(
                      getInitials(post.userName),
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.sp),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.userName,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14.sp,
                            shadows: [
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 3,
                                color: Colors.black.withOpacity(0.5),
                              ),
                            ],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          post.description,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                            shadows: [
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: 3,
                                color: Colors.black.withOpacity(0.5),
                              ),
                            ],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
