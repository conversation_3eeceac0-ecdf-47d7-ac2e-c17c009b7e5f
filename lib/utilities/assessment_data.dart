import 'dart:convert';
import 'dart:math';

import 'package:shared_preferences/shared_preferences.dart';

class AssessmentData {
  DateTime? dateTime;
  List<List<int>>? balanceAssessmentData;
  List<List<int>>? strideAssessmentData;

  AssessmentData() {
    dateTime = DateTime.now();
    balanceAssessmentData = [];
    strideAssessmentData = [];
  }

  AssessmentData.withData({
    this.dateTime,
    this.balanceAssessmentData,
    this.strideAssessmentData,
  });

  void addBalanceAssessmentData(List<int> data) {
    balanceAssessmentData?.add(data);
  }

  void addStrideAssessmentData(List<int> data) {
    strideAssessmentData?.add(data);
  }

  void setBalanceAssessmentData(List<List<int>> data) {
    balanceAssessmentData = data;
  }

  void setStrideAssessmentData(List<List<int>> data) {
    strideAssessmentData = data;
  }

  void setDateTime(DateTime dt) {
    dateTime = dt;
  }

  DateTime? getDateTime() {
    return dateTime;
  }

  List<List<int>> getBalanceAssessmentData() {
    return balanceAssessmentData ?? [];
  }

  List<List<int>> getStrideAssessmentData() {
    return strideAssessmentData ?? [];
  }

  Map<String, dynamic> toJson() {
    return {
      'dateTime': (dateTime ?? DateTime.now()).toIso8601String(),
      'balanceAssessmentData': balanceAssessmentData ?? [],
      'strideAssessmentData': strideAssessmentData ?? [],
    };
  }

  factory AssessmentData.fromJson(Map<String, dynamic> json) {
    return AssessmentData.withData(
      dateTime: DateTime.parse(json['dateTime']),
      balanceAssessmentData:
          (json['balanceAssessmentData'] as List)
              .map((e) => (e as List).map((i) => i as int).toList())
              .toList(),
      strideAssessmentData:
          (json['strideAssessmentData'] as List)
              .map((e) => (e as List).map((i) => i as int).toList())
              .toList(),
    );
  }
}

Future<void> saveAssessmentData(AssessmentData data) async {
  final prefs = await SharedPreferences.getInstance();
  final jsonString = jsonEncode(data.toJson());
  final identifier = 'assessment_${DateTime.now().millisecondsSinceEpoch}';
  await prefs.setString(identifier, jsonString);
}

Future<List<Map<String, dynamic>>> loadAllAssessmentData() async {
  final prefs = await SharedPreferences.getInstance();
  final keys = prefs.getKeys();
  final assessmentKeys =
      keys.where((key) => key.startsWith('assessment_')).toList();
  final List<Map<String, dynamic>> allData = [];

  for (String key in assessmentKeys) {
    final jsonString = prefs.getString(key);
    if (jsonString != null) {
      final data = jsonDecode(jsonString);
      allData.add(data);
    }
  }

  allData.sort((a, b) {
    final aDateTime = DateTime.parse(a['dateTime']);
    final bDateTime = DateTime.parse(b['dateTime']);
    return bDateTime.compareTo(aDateTime);
  });

  return allData;
}

List<Map<String, dynamic>> testLoadAllAssessmentData() {
  final math = Random();

  return [
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
    {
      'dateTime': DateTime.now().toIso8601String(),
      'balanceAssessmentData': List<List<int>>.generate(
        30,
        (_) => List<int>.generate(
          9,
          (_) => math.nextInt(16383) * (math.nextBool() ? 1 : -1),
        ),
      ),
      'strideAssessmentData': [],
    },
  ];
}
