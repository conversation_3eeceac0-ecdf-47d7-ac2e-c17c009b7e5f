// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_error_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LoginErrorDto _$LoginErrorDtoFromJson(Map<String, dynamic> json) =>
    _LoginErrorDto(
      type: json['__type'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$LoginErrorDtoToJson(_LoginErrorDto instance) =>
    <String, dynamic>{'__type': instance.type, 'message': instance.message};
