class Error {
  int? errorCode;
  String? errorMessage;

  Error(this.errorCode, this.errorMessage);

  @override
  String toString() {
    return 'Error[$errorCode]: $errorMessage';
  }
}

class Errors {
  static Error scanConnectedDeviceError = Error(
    4,
    'Error finding connected devices',
  );
  static Error connectionError = Error(5, 'Error connecting to device');
  static Error disconnectionError = Error(6, 'Error disconnecting from device');
  static Error scanAvailableDeviceError = Error(
    7,
    'Error finding available devices',
  );
  static Error startScanningError = Error(8, 'Error starting device scan');
  static Error discoverServicesError = Error(9, 'Error discovering services');
  static Error serviceNotFoundError = Error(10, 'Service not found');
  static Error characteristicNotFoundError = Error(
    11,
    'Characteristic not found',
  );

  /// Error message for when a connection is canceled by the user.
  static const String connectionCanceled =
      'ignore connections canceled by the user';
}
