// ignore_for_file: unintended_html_in_doc_comment

import 'package:ai_vanse/extensions/colors_extra_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

enum AppTextStyle {
  /// Weight Bold <br />
  /// Size 56.0 <br />
  /// Line Height 89.0 <br />
  h1(fontWeight: FontWeight.w700, fontSize: 56.0, lineHeight: 89.0),

  /// Weight Bold <br />
  /// Size 36.0 <br />
  /// Line Height 57.0 <br />
  h2(fontWeight: FontWeight.w700, fontSize: 36.0, lineHeight: 57.0),

  /// Weight Bold <br />
  /// Size 30.0 <br />
  /// Line Height 47.0 <br />
  h3(fontWeight: FontWeight.w700, fontSize: 30.0, lineHeight: 47.0),

  /// Weight 600 <br />
  /// Size 24.0 <br />
  /// Line Height 38.0 <br />
  h4(fontWeight: FontWeight.w600, fontSize: 24.0, lineHeight: 38.0),

  /// Weight 500 <br />
  /// Size 28.0 <br />
  /// Line Height 38.0 <br />
  h5(fontWeight: FontWeight.w500, fontSize: 28.0, lineHeight: 38.0),

  /// Weight 500 <br />
  /// Size 20.0 <br />
  /// Line Height 30.0 <br />
  h6(fontWeight: FontWeight.w500, fontSize: 20.0, lineHeight: 30.0),

  /// Weight 700 <br />
  /// Size 18.0 <br />
  /// Line Height 28.0 <br />
  titleSmall(fontWeight: FontWeight.w700, fontSize: 18.0, lineHeight: 28.0),

  /// Weight 500 <br />
  /// Size 34.0 <br />
  /// Line Height 54.0 <br />
  highlight(fontWeight: FontWeight.w500, fontSize: 34.0, lineHeight: 54.0),

  /// Weight Bold <br />
  /// Size 18.0 <br />
  /// Line Height 23.0 <br />
  bodyBold(
    fontWeight: FontWeight.w700,
    fontSize: 18.0,
    lineHeight: 23.0,
    fontFamily: 'Sarabun',
  ),

  /// Weight Bold <br />
  /// Size 18.0 <br />
  /// Line Height 23.0 <br />
  /// Font Family Sarabun <br />
  bodyText(
    fontWeight: FontWeight.w400,
    fontSize: 18.0,
    lineHeight: 23.0,
    fontFamily: 'Sarabun',
  ),

  /// Weight Bold <br />
  /// Size 16.0 <br />
  /// Line Height 20.0 <br />
  /// Font Family Sarabun <br />
  bodySmall(
    fontWeight: FontWeight.w400,
    fontSize: 16.0,
    lineHeight: 20.0,
    fontFamily: 'Sarabun',
  ),

  /// Weight Body Large 500 <br />
  /// Size 28.0 <br />
  /// Line Height 23.0 <br />
  /// Font Family Sarabun <br />
  bodyLargeW500(
    fontWeight: FontWeight.w500,
    fontSize: 28.0,
    lineHeight: 32.0,
    fontFamily: 'Sarabun',
  ),
  numberScale(
    fontWeight: FontWeight.w400,
    fontSize: 12.0,
    lineHeight: 16.0,
    fontFamily: 'Sarabun',
  );

  const AppTextStyle({
    this.fontWeight,
    this.fontSize,
    this.lineHeight,
    this.fontFamily,
  });

  final FontWeight? fontWeight;
  final double? fontSize;
  final double? lineHeight;
  final String? fontFamily;

  TextStyle get textStyle {
    final style = TextStyle(
      fontWeight: fontWeight,
      fontSize: fontSize,
      height: _height,
    );
    if (fontFamily != null) {
      return GoogleFonts.getFont(fontFamily!, textStyle: style);
    }
    return style;
  }

  double? get _height {
    if (lineHeight == null || fontSize == null) return null;
    return lineHeight!.sp / fontSize!.sp;
  }
}

enum AppTextColor {
  ///ColorHEx ==
  primary,
  primaryContainer,

  ///ColorHEx ==
  onPrimary,

  ///ColorHEx ==
  onSecondary,

  ///ColorHEx ==
  disabled,

  ///ColorHEx ==
  error,
  base,
  tertiary,
  onTertiary,
  unselected,
  energy,
  target;

  Color getColor(BuildContext context) {
    final ThemeData theme = context.theme;
    final colorScheme = theme.colorScheme;
    final extraColors = theme.extraColors;
    return switch (this) {
      AppTextColor.primary => colorScheme.primary,
      AppTextColor.onPrimary => colorScheme.onPrimary,
      AppTextColor.primaryContainer => colorScheme.primaryContainer,
      AppTextColor.onSecondary => colorScheme.onSecondary,
      AppTextColor.disabled => extraColors.slate.c200,
      AppTextColor.error => colorScheme.error,
      AppTextColor.base => colorScheme.onPrimary,
      AppTextColor.tertiary => colorScheme.tertiary,
      AppTextColor.onTertiary => colorScheme.onTertiary,
      AppTextColor.unselected => extraColors.unselected,
      AppTextColor.energy => extraColors.energy,
      AppTextColor.target => extraColors.target,
    };
  }
}

class AppText extends StatelessWidget {
  final String text;
  final AppTextStyle appTextStyle;
  final AppTextColor? appTextColor;
  final Color? color;
  final TextOverflow? textOverflow;
  final TextAlign? textAlign;
  final TextDecoration? textDecoration;
  final int? maxLines;
  final bool italic;
  final double? textLetterSpacing;
  final bool autoSize;
  final bool underline;
  final AppTextColor? underlineColor;
  final Color? decorationColor;

  const AppText(
    this.text, {
    super.key,
    this.appTextStyle = AppTextStyle.bodyText,
    this.appTextColor,
    this.color,
    this.textOverflow,
    this.textAlign,
    this.textDecoration,
    this.maxLines,
    this.italic = false,
    this.textLetterSpacing,
    this.autoSize = false,
    this.underline = false,
    this.underlineColor,
    this.decorationColor,
  });

  Color? _getTextColor(BuildContext context) {
    if (color != null || appTextColor == null) {
      return color;
    }
    return (appTextColor ?? AppTextColor.base).getColor(context);
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle applyTextStyle = appTextStyle.textStyle.copyWith(
      fontSize: appTextStyle.fontSize?.sp,
    );

    final fontStyle = applyTextStyle.copyWith(
      overflow: textOverflow,
      decoration: underline ? TextDecoration.underline : textDecoration,
      decorationColor:
          underline
              ? (underlineColor?.getColor(context) ??
                  context.theme.primaryColor)
              : decorationColor,
      fontStyle: italic ? FontStyle.italic : null,
      color: _getTextColor(context),
      letterSpacing: textLetterSpacing,
    );

    if (autoSize) {
      return AutoSizeText(
        text,
        textAlign: textAlign,
        maxLines: maxLines,
        style: fontStyle,
        maxFontSize: (fontStyle.fontSize ?? 14.sp).toPrecision(0),
        stepGranularity: 0.1,
        minFontSize: 6.sp.toPrecision(0),
      );
    }

    return Text(
      text,
      textAlign: textAlign,
      maxLines: maxLines,
      style: fontStyle,
    );
  }
}
