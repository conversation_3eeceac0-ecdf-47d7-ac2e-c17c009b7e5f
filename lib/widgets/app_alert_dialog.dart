import 'package:ai_vanse/extensions/colors_extra_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:ai_vanse/widgets/app_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

enum AppAlertDialogResult { closed, confirmed }

enum AppAlertDialogType {
  information,
  confirmation,
  warning,
  error,
  success,
  custom,
}

class AppAlertDialog extends StatelessWidget {
  final AppAlertDialogType type;
  final String title;
  final String message;
  final String? confirmText;
  final String? cancelText;
  final IconData? customIcon;
  final Color? customColor;

  const AppAlertDialog({
    super.key,
    this.type = AppAlertDialogType.information,
    required this.title,
    required this.message,
    this.confirmText,
    this.cancelText,
    this.customIcon,
    this.customColor,
  });

  static Future<AppAlertDialogResult?> show(
    String title,
    String message, {
    String? confirmText,
    String? cancelText,
    AppAlertDialogType type = AppAlertDialogType.information,
    IconData? customIcon,
    Color? customColor,
  }) {
    return Get.dialog<AppAlertDialogResult>(
      AppAlertDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        type: type,
        customIcon: customIcon,
        customColor: customColor,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final extraColors = theme.extraColors;
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.sp)),
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8.sp),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.all(16.sp),
              child: Column(
                children: [
                  Icon(_buildIcon(), size: 60.sp, color: _getIconColor(theme)),
                  SizedBox(height: 16.sp),
                  AppText(
                    title,
                    appTextStyle: AppTextStyle.h2,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.sp),
                  AppText(
                    message,
                    appTextStyle: AppTextStyle.bodyText,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.sp),
                ],
              ),
            ),
            Divider(height: 1, thickness: 1, color: extraColors.slate.c50),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: 50.sp,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(8.sp),
          bottomRight: Radius.circular(8.sp),
        ),
      ),
      child:
          type == AppAlertDialogType.confirmation
              ? Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(child: _buildCloseButton(theme)),
                  SizedBox(
                    height: 40.0,
                    child: VerticalDivider(
                      width: 1.sp,
                      thickness: 1,
                      color: theme.extraColors.slate.c50,
                    ),
                  ),
                  Expanded(child: _buildConfirmButton(theme)),
                ],
              )
              : Center(child: _buildConfirmButton(theme)),
    );
  }

  Widget _buildConfirmButton(ThemeData theme) {
    return SizedBox(
      height: 50.sp,
      child: AppTextButton(
        label: confirmText ?? 'common_cta_ok'.tr,
        textAlign: TextAlign.center,
        appTextStyle: AppTextStyle.bodyText,
        textColor: theme.colorScheme.primary,
        onPressed: () => Get.back(result: AppAlertDialogResult.confirmed),
      ),
    );
  }

  Widget _buildCloseButton(ThemeData theme) {
    return SizedBox(
      height: 50.sp,
      child: AppTextButton(
        label: cancelText ?? 'common_cta_close'.tr,
        textAlign: TextAlign.center,
        appTextStyle: AppTextStyle.bodyText,
        textColor: theme.colorScheme.primary,
        onPressed: () => Get.back(result: AppAlertDialogResult.closed),
      ),
    );
  }

  IconData _buildIcon() {
    if (type == AppAlertDialogType.custom && customIcon != null) {
      return customIcon!;
    }

    return switch (type) {
      AppAlertDialogType.information => Icons.notifications_outlined,
      AppAlertDialogType.confirmation => Icons.help_outline,
      AppAlertDialogType.warning => Icons.warning_amber_rounded,
      AppAlertDialogType.error => Icons.error_outline_outlined,
      AppAlertDialogType.success => Icons.check_circle_outlined,
      _ => Icons.notifications_outlined,
    };
  }

  Color _getIconColor(ThemeData theme) {
    if (type == AppAlertDialogType.custom && customColor != null) {
      return customColor!;
    }

    return switch (type) {
      AppAlertDialogType.information => theme.primaryColor,
      AppAlertDialogType.confirmation => theme.primaryColor,
      AppAlertDialogType.warning => theme.primaryColor,
      AppAlertDialogType.error => theme.extraColors.red.c600,
      AppAlertDialogType.success => theme.primaryColor,
      _ => theme.primaryColor,
    };
  }
}
