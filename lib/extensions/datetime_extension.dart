import 'package:ai_vanse/extensions/locale_extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

extension DateTimeNullableExtension on DateTime? {
  String format([String pattern = 'yyyyMMddHmmss']) {
    if (this == null) {
      return '';
    }

    return this!.format(pattern);
  }
}

extension DateTimeExtension on DateTime {
  String format([String pattern = 'yyyyMMddHHmmss']) {
    final formatter = DateFormat(pattern, Get.locale?.toLanguageTag());
    final strDate = formatter.format(this);

    return Get.locale!.isBuddhist
        ? strDate.replaceAll(year.toString(), (year + 543).toString())
        : strDate;
  }

  String defaultDisplay({bool time = false}) {
    String formatDate = 'dd/MM/yyyy';
    if (time) {
      formatDate += ' HH:mm';
    }

    return format(formatDate);
  }

  DateTime addTime(TimeOfDay? time) {
    if (time == null) {
      return this;
    }
    return DateTime(year, month, day, time.hour, time.minute);
  }

  TimeOfDay toTime() {
    return TimeOfDay.fromDateTime(this);
  }

  String get relativeTime {
    final now = DateTime.now();
    final Duration diff = now.difference(this);
    final inDays = diff.inDays;
    if (inDays < 1) {
      return format('HH:mm');
    } else if (inDays < 7) {
      return 'days_ago'.trParams({'day': inDays.toString()});
    } else {
      return defaultDisplay(time: false);
    }
  }

  String formatToWeekDay() {
    return 'common_weekDay_$weekday'.tr;
  }
}
