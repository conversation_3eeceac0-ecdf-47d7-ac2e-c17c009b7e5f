import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/screens/post/controllers/gallery_controller.dart';
import 'package:ai_vanse/utilities/snackbar.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:ai_vanse/widgets/control_buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';

class GalleryPickerScreen extends StatefulWidget {
  const GalleryPickerScreen({super.key});

  @override
  State<GalleryPickerScreen> createState() => _GalleryPickerScreenState();
}

class _GalleryPickerScreenState extends State<GalleryPickerScreen> {
  late final GalleryController controller = Get.find();

  final int _sliverGridPageSize = 20;
  int _totalAssets = 0;
  int _page = 0;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreToLoad = true;
  AssetPathEntity? _selectedAlbum;
  List<AssetEntity>? _assets;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _initialize() async {
    controller.requestPermission();
    await _loadAlbums();
    await _loadAssets();
  }

  Future _loadAlbums() async {
    setState(() {
      _isLoading = true;
    });
    final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
      type: RequestType.image,
      onlyAll: true,
    );
    if (!mounted) {
      return;
    }

    if (albums.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      showSnackBar(context, 'No albums found.');
      return;
    }

    setState(() {
      _selectedAlbum = albums.first;
    });
  }

  Future _loadAssets() async {
    if (_selectedAlbum == null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }
    _totalAssets = await _selectedAlbum!.assetCountAsync;
    final List<AssetEntity> assets = await _selectedAlbum!.getAssetListPaged(
      page: 0,
      size: _sliverGridPageSize,
    );

    controller.asset.value = assets.firstOrNull;

    if (!mounted) {
      return;
    }

    setState(() {
      _assets = assets;
      _isLoading = false;
      _hasMoreToLoad = _assets!.length < _totalAssets;
    });
  }

  Future<void> _loadMoreAsset() async {
    final List<AssetEntity> entities = await _selectedAlbum!.getAssetListPaged(
      page: _page + 1,
      size: _sliverGridPageSize,
    );
    if (!mounted) {
      return;
    }
    setState(() {
      _assets!.addAll(entities);
      _page++;
      _hasMoreToLoad = _assets!.length < _totalAssets;
      _isLoadingMore = false;
    });
  }

  Widget _buildAlbumPreview() {
    return _assets == null
        ? Container()
        : _isLoading
        ? const Center(child: CircularProgressIndicator.adaptive())
        : _buildGrid();
  }

  Widget _buildGrid() {
    return GridView.custom(
      scrollDirection: Axis.vertical,
      padding: context.safeAreaBottomPadding + EdgeInsets.only(top: 60.sp),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 4.sp,
        mainAxisSpacing: 4.sp,
        childAspectRatio: 1,
      ),
      childrenDelegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          if (index == 0) {
            return _buildCameraButton();
          }
          if (index == _assets!.length - 9 &&
              !_isLoadingMore &&
              _hasMoreToLoad) {
            _loadMoreAsset();
          }
          final AssetEntity entity = _assets![index - 1];
          return assetWidget(entity);
        },
        childCount: _assets!.length + 1,
        findChildIndexCallback: (Key key) {
          if (key is ValueKey<int>) {
            return key.value;
          }
          return null;
        },
      ),
    );
  }

  Widget _buildCameraButton() => GestureDetector(
    onTap: () {
      controller.captureImage();
    },
    child: Container(
      color: Color(0xFFD9D9D9),
      child: Center(child: Assets.svgs.icoCamera.svg()),
    ),
  );

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      title: 'Select Image',
      appBarFontColor: context.theme.extraColors.energy,
      actions: [_buildNext()],
      body: Column(
        children: [
          AspectRatio(aspectRatio: 1, child: _buildPreview()),
          Expanded(child: _buildAlbumPreview()),
        ],
      ),
    );
  }

  Widget _buildNext() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.createMeal,
          arguments: {'asset': controller.asset.value},
        );
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(width: 8.5.sp),
          AppText(
            'common_cta_next'.tr,
            appTextStyle: AppTextStyle.bodyText,
            appTextColor:
                controller.asset.value != null
                    ? AppTextColor.tertiary
                    : AppTextColor.disabled,
            autoSize: true,
          ),
          Icon(
            Icons.chevron_right,
            color:
                controller.asset.value != null
                    ? context.theme.colorScheme.tertiary
                    : context.theme.colorScheme.onSurface.withOpacity(0.38),
          ),
          SizedBox(width: 5.sp),
        ],
      ),
    );
  }

  Widget assetWidget(AssetEntity asset) {
    return Obx(
      () => GestureDetector(
        onTap: () => controller.asset.value = asset,
        child: Stack(
          children: [
            Positioned.fill(
              child: AssetEntityImage(
                asset,
                isOriginal: false,
                filterQuality: FilterQuality.medium,
                fit: BoxFit.cover,
                thumbnailSize: ThumbnailSize.square(
                  (View.of(context).physicalSize.width / 4).ceil(),
                ),
                errorBuilder:
                    (context, error, stackTrace) => Center(
                      child: Icon(Icons.error_outline, color: Colors.red),
                    ),
              ),
            ),
            if (controller.asset.value == asset)
              Positioned(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.blue, width: 2.sp),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreview() {
    return Obx(() {
      if (controller.asset.value == null) return Container();
      return AssetEntityImage(
        controller.asset.value!,
        isOriginal: false,
        filterQuality: FilterQuality.high,
        fit: BoxFit.cover,
        thumbnailSize: ThumbnailSize.square(
          View.of(context).physicalSize.width.ceil(),
        ),
        errorBuilder:
            (context, error, stackTrace) =>
                Center(child: Icon(Icons.error_outline, color: Colors.red)),
      );
    });
  }
}

class AssetPreview extends StatelessWidget {
  final AssetEntity asset;
  final void Function(AssetEntity) onAssetConfirmed;
  final void Function() onPopBack;

  const AssetPreview({
    super.key,
    required this.asset,
    required this.onAssetConfirmed,
    required this.onPopBack,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: AssetEntityImage(
                asset,
                isOriginal: false,
                filterQuality: FilterQuality.high,
                fit: BoxFit.contain,
                thumbnailSize: const ThumbnailSize.square(2000),
              ),
            ),
            Align(alignment: Alignment.topLeft, child: cancelButton(onPopBack)),
            Align(
              alignment: Alignment.topRight,
              child: confirmButton(() {
                onPopBack();
                onAssetConfirmed(asset);
              }),
            ),
          ],
        ),
      ),
    );
  }
}
