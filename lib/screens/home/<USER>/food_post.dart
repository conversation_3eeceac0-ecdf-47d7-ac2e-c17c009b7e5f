class Nutrition {
  final double totalPortionSize;
  final double energy;
  final double protein;
  final double fat;
  final double carbohydrate;
  final double fiber;
  final double totalSugars;
  final double calcium;
  final double iron;
  final double magnesium;
  final double phosphorus;
  final double potassium;
  final double sodium;
  final double zinc;
  final double cholesterol;

  Nutrition({
    required this.totalPortionSize,
    required this.energy,
    required this.protein,
    required this.fat,
    required this.carbohydrate,
    required this.fiber,
    required this.totalSugars,
    required this.calcium,
    required this.iron,
    required this.magnesium,
    required this.phosphorus,
    required this.potassium,
    required this.sodium,
    required this.zinc,
    required this.cholesterol,
  });

  factory Nutrition.fromJson(Map<String, dynamic> json) {
    return Nutrition(
      totalPortionSize: (json['totalPortionSize'] as num).toDouble(),
      energy: (json['energy'] as num).toDouble(),
      protein: (json['protein'] as num).toDouble(),
      fat: (json['fat'] as num).toDouble(),
      carbohydrate: (json['carbohydrate'] as num).toDouble(),
      fiber: (json['fiber'] as num).toDouble(),
      totalSugars: (json['totalSugars'] as num).toDouble(),
      calcium: (json['calcium'] as num).toDouble(),
      iron: (json['iron'] as num).toDouble(),
      magnesium: (json['magnesium'] as num).toDouble(),
      phosphorus: (json['phosphorus'] as num).toDouble(),
      potassium: (json['potassium'] as num).toDouble(),
      sodium: (json['sodium'] as num).toDouble(),
      zinc: (json['zinc'] as num).toDouble(),
      cholesterol: (json['cholesterol'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalPortionSize': totalPortionSize,
      'energy': energy,
      'protein': protein,
      'fat': fat,
      'carbohydrate': carbohydrate,
      'fiber': fiber,
      'totalSugars': totalSugars,
      'calcium': calcium,
      'iron': iron,
      'magnesium': magnesium,
      'phosphorus': phosphorus,
      'potassium': potassium,
      'sodium': sodium,
      'zinc': zinc,
      'cholesterol': cholesterol,
    };
  }

  Map<String, String> toDisplayMap() {
    return {
      'Total Portion Size': '${totalPortionSize}g',
      'Energy': '${energy}kcal',
      'Protein': '${protein}g',
      'Fat': '${fat}g',
      'Carbohydrate': '${carbohydrate}g',
      'Fiber': '${fiber}g',
      'Total Sugars': '${totalSugars}g',
      'Calcium': '${calcium}mg',
      'Iron': '${iron}mg',
      'Magnesium': '${magnesium}mg',
      'Phosphorus': '${phosphorus}mg',
      'Potassium': '${potassium}mg',
      'Sodium': '${sodium}mg',
      'Zinc': '${zinc}mg',
      'Cholesterol': '${cholesterol}mg',
    };
  }
}

class FoodPost {
  final String id;
  final String userName;
  final String userProfileImage;
  final String imageUrl;
  final String description;
  final String foodName;
  final double distance;
  final List<String> tags;
  final int rating;
  final Nutrition nutrition;

  FoodPost({
    required this.id,
    required this.userName,
    required this.userProfileImage,
    required this.imageUrl,
    required this.description,
    required this.foodName,
    required this.distance,
    required this.tags,
    required this.rating,
    required this.nutrition,
  });

  factory FoodPost.fromJson(Map<String, dynamic> json) {
    return FoodPost(
      id: json['id'] as String,
      userName: json['userName'] as String,
      userProfileImage: json['userProfileImage'] as String,
      imageUrl: json['imageUrl'] as String,
      description: json['description'] as String,
      foodName: json['foodName'] as String,
      distance: (json['distance'] as num).toDouble(),
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      rating: json['rating'] as int? ?? 0,
      nutrition: Nutrition.fromJson(json['nutrition'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName,
      'userProfileImage': userProfileImage,
      'imageUrl': imageUrl,
      'description': description,
      'foodName': foodName,
      'distance': distance,
      'tags': tags,
      'rating': rating,
      'nutrition': nutrition.toJson(),
    };
  }
}
