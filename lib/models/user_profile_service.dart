import 'package:ai_vanse/models/persist_data_service.dart';
import 'package:ai_vanse/models/user_profile_data_model.dart';

class UserProfileService {
  UserProfileService._privateConstructor();

  static final UserProfileService _instance =
      UserProfileService._privateConstructor();

  factory UserProfileService() {
    return _instance;
  }

  Map<String, dynamic> _userProfile = UserProfile.defaultProfile().toMap();

  Future initialize() async {
    PersistDataService().queryAllUserProfiles().then((allUsers) {
      if (allUsers.isEmpty) {
        PersistDataService().insertUserProfile(_userProfile);
        return;
      }
      _userProfile = UserProfile.fromMap(allUsers.first).toMap();
    });
  }

  Future saveUserProfile() async {
    await PersistDataService().updateUserProfile(_userProfile);
  }

  void handlePersonalInfoChanged(
    String name,
    String email,
    int age,
    double weight,
    double height,
  ) {
    _userProfile['name'] = name;
    _userProfile['email'] = email;
    _userProfile['age'] = age;
    _userProfile['weight'] = weight;
    _userProfile['height'] = height;
    saveUserProfile();
  }

  void handleNutritionalGoalChanged(
    double calorieLimit,
    double fatLimit,
    double carbLimit,
    double proteinLimit,
    double fiberLimit,
    double waterIntakeGoal,
    double sugarIntakeGoal,
    double saltIntakeGoal,
  ) {
    _userProfile['calorie_limit'] = calorieLimit;
    _userProfile['fat_limit'] = fatLimit;
    _userProfile['carb_limit'] = carbLimit;
    _userProfile['protein_limit'] = proteinLimit;
    _userProfile['fiber_limit'] = fiberLimit;
    _userProfile['water_intake_goal'] = waterIntakeGoal;
    _userProfile['sugar_intake_goal'] = sugarIntakeGoal;
    _userProfile['salt_intake_goal'] = saltIntakeGoal;
    saveUserProfile();
  }

  void handleFoodPreferenceChanged(
    int foodPreference,
    bool noAllergies,
    bool glutenIntolerant,
    bool wheatIntolerant,
    bool lactoseIntolerant,
    bool milkAllergy,
    bool eggAllergy,
    bool shellfishAllergy,
    bool fishAllergy,
    bool nutsAllergy,
  ) {
    _userProfile['food_preference'] = foodPreference;
    _userProfile['no_allergies'] = noAllergies;
    _userProfile['gluten_intolerant'] = glutenIntolerant;
    _userProfile['wheat_intolerant'] = wheatIntolerant;
    _userProfile['lactose_intolerant'] = lactoseIntolerant;
    _userProfile['milk_allergy'] = milkAllergy;
    _userProfile['egg_allergy'] = eggAllergy;
    _userProfile['shellfish_allergy'] = shellfishAllergy;
    _userProfile['fish_allergy'] = fishAllergy;
    _userProfile['nuts_allergy'] = nutsAllergy;
    saveUserProfile();
  }

  void handleSingleChange(String key, dynamic value) {
    _userProfile[key] = value;
    saveUserProfile();
  }

  Map<String, dynamic> getUserProfile() {
    return _userProfile;
  }

  dynamic get(String key) {
    return _userProfile[key];
  }

  Map<String, dynamic> getPersonalInfo() {
    return {
      'name': _userProfile['name'],
      'email': _userProfile['email'],
      'age': _userProfile['age'],
      'weight': _userProfile['weight'],
      'height': _userProfile['height'],
    };
  }

  Map<String, dynamic> getNutritionalGoals() {
    return {
      'calorie_limit': _userProfile['calorie_limit'],
      'fat_limit': _userProfile['fat_limit'],
      'carb_limit': _userProfile['carb_limit'],
      'protein_limit': _userProfile['protein_limit'],
      'fiber_limit': _userProfile['fiber_limit'],
      'water_intake_goal': _userProfile['water_intake_goal'],
      'sugar_intake_goal': _userProfile['sugar_intake_goal'],
      'salt_intake_goal': _userProfile['salt_intake_goal'],
    };
  }
}
