import 'package:ai_vanse/screens/dietary/view/dietary_assestment_page.dart';
import 'package:ai_vanse/screens/home/<USER>/home_controller.dart';
import 'package:ai_vanse/screens/home/<USER>/home_summary_page.dart';
import 'package:ai_vanse/screens/home/<USER>/home_view.dart';
import 'package:ai_vanse/screens/home/<USER>/widgets/navigation_menu.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      extendBody: true,
      extendBodyBehindAppBar: true,
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: controller.pageController,
        children: [
          const HomeView(),
          const HomeSummaryPage(),
          const DietaryAssessmentScreen(),
          Center(child: AppText('Health Page')),
          Center(child: AppText('Profile Page')),
        ],
      ),
      bottomNavigationBar: const NavigationMenu(),
    );
  }
}
