import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_dto.freezed.dart';
part 'user_dto.g.dart';

@freezed
abstract class UserDto with _$UserDto {
  @JsonSerializable(explicitToJson: true)
  const factory UserDto({
    required int id,
    required String userName,
    String? email,
    String? fullname,
    required String firstName,
    required String lastName,
    String? tel,
    String? subgroupPermission,
    required String tenantPermission,
    required int roleId,
    required UserRoleDto roles,
  }) = _UserDto;

  factory UserDto.fromJson(dynamic json) => _$UserDtoFromJson(json);
}

@freezed
abstract class UserRoleDto with _$UserRoleDto {
  @JsonSerializable(explicitToJson: true)
  const factory UserRoleDto({
    required int id,
    required String name,
    String? description,
    bool? forDeveloper,
    required DateTime createdDate,
    DateTime? modifiedDate,
    DateTime? deletedDate,
  }) = _UserRoleDto;

  factory UserRoleDto.fromJson(dynamic json) => _$UserRoleDtoFromJson(json);
}

@Freezed(toJson: true)
abstract class UserRequestDto with _$UserRequestDto {
  const factory UserRequestDto({
    String? firstname,
    String? lastname,
    String? email,
    String? tel,
    HomeAddress? homeAddress,
  }) = _UserRequestDto;
}

@Freezed(toJson: true)
abstract class HomeAddress with _$HomeAddress {
  const factory HomeAddress({
    String? address,
    String? city,
    String? state,
    String? province,
    String? zipcode,
  }) = _HomeAddress;
}
