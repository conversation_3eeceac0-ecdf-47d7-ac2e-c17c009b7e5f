import 'package:ai_vanse/extensions/string_extension.dart';
import 'package:intl/intl.dart';

extension NumExtension on num {
  String toCurrencyFormat({bool showDecimal = false}) {
    final formater = NumberFormat('#,##0${showDecimal ? '.00' : ''}');
    return formater.format(this);
  }

  String toCurrencyWithAutoDecimal() {
    return toCurrencyFormat(showDecimal: this % 1 != 0);
  }

  String toHourMinute() {
    final Duration duration = Duration(seconds: ceil());
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes.remainder(60);
    return '$hours:${minutes.toString().padLeft(2, '0')}';
  }
}

extension IntExtension on int {
  String toRepeatString(String string) {
    final buffer = StringBuffer()..writeAll(List.generate(this, (_) => string));
    return buffer.toString();
  }
}

extension DoubleExtension on double {
  double roundFixed([int fractionDigits = 2]) {
    return toStringAsFixed(fractionDigits).toDouble(defaultValue: 0.0) ?? 0.0;
  }
}

extension NullDoubleExtension on double? {
  double roundFixed([int fractionDigits = 2]) {
    if (this == null || this == 0.0) return 0.0;

    return this!.roundFixed(fractionDigits);
  }
}
