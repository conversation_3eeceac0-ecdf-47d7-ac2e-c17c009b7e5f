/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/icon.png
  AssetGenImage get icon => const AssetGenImage('assets/images/icon.png');

  /// File path: assets/images/icon_no_bg.png
  AssetGenImage get iconNoBg =>
      const AssetGenImage('assets/images/icon_no_bg.png');

  /// File path: assets/images/icon_no_image.png
  AssetGenImage get iconNoImage =>
      const AssetGenImage('assets/images/icon_no_image.png');

  /// File path: assets/images/rating.png
  AssetGenImage get rating => const AssetGenImage('assets/images/rating.png');

  /// List of all assets
  List<AssetGenImage> get values => [icon, iconNoBg, iconNoImage, rating];
}

class $AssetsSvgsGen {
  const $AssetsSvgsGen();

  /// File path: assets/svgs/add_button.svg
  SvgGenImage get addButton => const SvgGenImage('assets/svgs/add_button.svg');

  /// File path: assets/svgs/bg_setting.svg
  SvgGenImage get bgSetting => const SvgGenImage('assets/svgs/bg_setting.svg');

  /// File path: assets/svgs/bg_sleep.svg
  SvgGenImage get bgSleep => const SvgGenImage('assets/svgs/bg_sleep.svg');

  /// File path: assets/svgs/carlander_active.svg
  SvgGenImage get carlanderActive =>
      const SvgGenImage('assets/svgs/carlander_active.svg');

  /// File path: assets/svgs/carlander_inactive.svg
  SvgGenImage get carlanderInactive =>
      const SvgGenImage('assets/svgs/carlander_inactive.svg');

  /// File path: assets/svgs/cutlery_active.svg
  SvgGenImage get cutleryActive =>
      const SvgGenImage('assets/svgs/cutlery_active.svg');

  /// File path: assets/svgs/cutlery_inactive.svg
  SvgGenImage get cutleryInactive =>
      const SvgGenImage('assets/svgs/cutlery_inactive.svg');

  /// File path: assets/svgs/eye_active.svg
  SvgGenImage get eyeActive => const SvgGenImage('assets/svgs/eye_active.svg');

  /// File path: assets/svgs/eye_inactive.svg
  SvgGenImage get eyeInactive =>
      const SvgGenImage('assets/svgs/eye_inactive.svg');

  /// File path: assets/svgs/heart_rate_active.svg
  SvgGenImage get heartRateActive =>
      const SvgGenImage('assets/svgs/heart_rate_active.svg');

  /// File path: assets/svgs/heart_rate_inactive.svg
  SvgGenImage get heartRateInactive =>
      const SvgGenImage('assets/svgs/heart_rate_inactive.svg');

  /// File path: assets/svgs/ico_activity_active.svg
  SvgGenImage get icoActivityActive =>
      const SvgGenImage('assets/svgs/ico_activity_active.svg');

  /// File path: assets/svgs/ico_activity_inactive.svg
  SvgGenImage get icoActivityInactive =>
      const SvgGenImage('assets/svgs/ico_activity_inactive.svg');

  /// File path: assets/svgs/ico_air_node.svg
  SvgGenImage get icoAirNode =>
      const SvgGenImage('assets/svgs/ico_air_node.svg');

  /// File path: assets/svgs/ico_arrow_left.svg
  SvgGenImage get icoArrowLeft =>
      const SvgGenImage('assets/svgs/ico_arrow_left.svg');

  /// File path: assets/svgs/ico_battery_charging.svg
  SvgGenImage get icoBatteryCharging =>
      const SvgGenImage('assets/svgs/ico_battery_charging.svg');

  /// File path: assets/svgs/ico_calender.svg
  SvgGenImage get icoCalender =>
      const SvgGenImage('assets/svgs/ico_calender.svg');

  /// File path: assets/svgs/ico_graph.svg
  SvgGenImage get icoGraph => const SvgGenImage('assets/svgs/ico_graph.svg');

  /// File path: assets/svgs/ico_heart.svg
  SvgGenImage get icoHeart => const SvgGenImage('assets/svgs/ico_heart.svg');

  /// File path: assets/svgs/ico_laying_active.svg
  SvgGenImage get icoLayingActive =>
      const SvgGenImage('assets/svgs/ico_laying_active.svg');

  /// File path: assets/svgs/ico_laying_inactive.svg
  SvgGenImage get icoLayingInactive =>
      const SvgGenImage('assets/svgs/ico_laying_inactive.svg');

  /// File path: assets/svgs/ico_meal.svg
  SvgGenImage get icoMeal => const SvgGenImage('assets/svgs/ico_meal.svg');

  /// File path: assets/svgs/ico_myAct_active.svg
  SvgGenImage get icoMyActActive =>
      const SvgGenImage('assets/svgs/ico_myAct_active.svg');

  /// File path: assets/svgs/ico_myAct_inactive.svg
  SvgGenImage get icoMyActInactive =>
      const SvgGenImage('assets/svgs/ico_myAct_inactive.svg');

  /// File path: assets/svgs/ico_rounded_air_node.svg
  SvgGenImage get icoRoundedAirNode =>
      const SvgGenImage('assets/svgs/ico_rounded_air_node.svg');

  /// File path: assets/svgs/ico_rounded_checkbox.svg
  SvgGenImage get icoRoundedCheckbox =>
      const SvgGenImage('assets/svgs/ico_rounded_checkbox.svg');

  /// File path: assets/svgs/ico_rounded_data.svg
  SvgGenImage get icoRoundedData =>
      const SvgGenImage('assets/svgs/ico_rounded_data.svg');

  /// File path: assets/svgs/ico_rounded_graph.svg
  SvgGenImage get icoRoundedGraph =>
      const SvgGenImage('assets/svgs/ico_rounded_graph.svg');

  /// File path: assets/svgs/ico_rounded_logout.svg
  SvgGenImage get icoRoundedLogout =>
      const SvgGenImage('assets/svgs/ico_rounded_logout.svg');

  /// File path: assets/svgs/ico_rounded_my_act.svg
  SvgGenImage get icoRoundedMyAct =>
      const SvgGenImage('assets/svgs/ico_rounded_my_act.svg');

  /// File path: assets/svgs/ico_rounded_sleep.svg
  SvgGenImage get icoRoundedSleep =>
      const SvgGenImage('assets/svgs/ico_rounded_sleep.svg');

  /// File path: assets/svgs/ico_rounded_smartwatch.svg
  SvgGenImage get icoRoundedSmartwatch =>
      const SvgGenImage('assets/svgs/ico_rounded_smartwatch.svg');

  /// File path: assets/svgs/ico_setting.svg
  SvgGenImage get icoSetting =>
      const SvgGenImage('assets/svgs/ico_setting.svg');

  /// File path: assets/svgs/ico_setting_active.svg
  SvgGenImage get icoSettingActive =>
      const SvgGenImage('assets/svgs/ico_setting_active.svg');

  /// File path: assets/svgs/ico_setting_inactive.svg
  SvgGenImage get icoSettingInactive =>
      const SvgGenImage('assets/svgs/ico_setting_inactive.svg');

  /// File path: assets/svgs/ico_sitting_active.svg
  SvgGenImage get icoSittingActive =>
      const SvgGenImage('assets/svgs/ico_sitting_active.svg');

  /// File path: assets/svgs/ico_sitting_inactive.svg
  SvgGenImage get icoSittingInactive =>
      const SvgGenImage('assets/svgs/ico_sitting_inactive.svg');

  /// File path: assets/svgs/ico_sleep_active.svg
  SvgGenImage get icoSleepActive =>
      const SvgGenImage('assets/svgs/ico_sleep_active.svg');

  /// File path: assets/svgs/ico_sleep_inactive.svg
  SvgGenImage get icoSleepInactive =>
      const SvgGenImage('assets/svgs/ico_sleep_inactive.svg');

  /// File path: assets/svgs/ico_smartwatch.svg
  SvgGenImage get icoSmartwatch =>
      const SvgGenImage('assets/svgs/ico_smartwatch.svg');

  /// File path: assets/svgs/ico_sos_button.svg
  SvgGenImage get icoSosButton =>
      const SvgGenImage('assets/svgs/ico_sos_button.svg');

  /// File path: assets/svgs/ico_standing_active.svg
  SvgGenImage get icoStandingActive =>
      const SvgGenImage('assets/svgs/ico_standing_active.svg');

  /// File path: assets/svgs/ico_standing_inactive.svg
  SvgGenImage get icoStandingInactive =>
      const SvgGenImage('assets/svgs/ico_standing_inactive.svg');

  /// File path: assets/svgs/ico_user_active.svg
  SvgGenImage get icoUserActive =>
      const SvgGenImage('assets/svgs/ico_user_active.svg');

  /// File path: assets/svgs/ico_user_inactive.svg
  SvgGenImage get icoUserInactive =>
      const SvgGenImage('assets/svgs/ico_user_inactive.svg');

  /// File path: assets/svgs/lock_active.svg
  SvgGenImage get lockActive =>
      const SvgGenImage('assets/svgs/lock_active.svg');

  /// File path: assets/svgs/lock_inactive.svg
  SvgGenImage get lockInactive =>
      const SvgGenImage('assets/svgs/lock_inactive.svg');

  /// File path: assets/svgs/logo.svg
  SvgGenImage get logo => const SvgGenImage('assets/svgs/logo.svg');

  /// File path: assets/svgs/user_active.svg
  SvgGenImage get userActive =>
      const SvgGenImage('assets/svgs/user_active.svg');

  /// File path: assets/svgs/user_inactive.svg
  SvgGenImage get userInactive =>
      const SvgGenImage('assets/svgs/user_inactive.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    addButton,
    bgSetting,
    bgSleep,
    carlanderActive,
    carlanderInactive,
    cutleryActive,
    cutleryInactive,
    eyeActive,
    eyeInactive,
    heartRateActive,
    heartRateInactive,
    icoActivityActive,
    icoActivityInactive,
    icoAirNode,
    icoArrowLeft,
    icoBatteryCharging,
    icoCalender,
    icoGraph,
    icoHeart,
    icoLayingActive,
    icoLayingInactive,
    icoMeal,
    icoMyActActive,
    icoMyActInactive,
    icoRoundedAirNode,
    icoRoundedCheckbox,
    icoRoundedData,
    icoRoundedGraph,
    icoRoundedLogout,
    icoRoundedMyAct,
    icoRoundedSleep,
    icoRoundedSmartwatch,
    icoSetting,
    icoSettingActive,
    icoSettingInactive,
    icoSittingActive,
    icoSittingInactive,
    icoSleepActive,
    icoSleepInactive,
    icoSmartwatch,
    icoSosButton,
    icoStandingActive,
    icoStandingInactive,
    icoUserActive,
    icoUserInactive,
    lockActive,
    lockInactive,
    logo,
    userActive,
    userInactive,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsSvgsGen svgs = $AssetsSvgsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
