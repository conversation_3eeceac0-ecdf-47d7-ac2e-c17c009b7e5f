import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_response_dto.freezed.dart';
part 'login_response_dto.g.dart';

@freezed
abstract class LoginResponseDto with _$LoginResponseDto {
  @JsonSerializable(
    fieldRename: FieldRename.pascal,
    explicitToJson: true,
    createToJson: true,
  )
  const factory LoginResponseDto({
    required String accessToken,
    required String idToken,
    required String refreshToken,
    required String tokenType,
    required int expiresIn,
  }) = _LoginResponseDto;

  factory LoginResponseDto.fromJson(dynamic json) =>
      _$LoginResponseDtoFromJson(json);
}
