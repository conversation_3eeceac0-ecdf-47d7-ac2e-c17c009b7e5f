import 'package:ai_vanse/l10n/translate.dart';
import 'package:get/get.dart';

class LanguageSettingController extends GetxController {
  final Rx<AppTranslationLanguage> selectedLanguage =
      AppTranslation.currentLanguage.obs;

  void changeLanguage(AppTranslationLanguage? language) {
    if (language == null) return;
    selectedLanguage.value = language;
    AppTranslation.changeLocale(language);
  }
}
