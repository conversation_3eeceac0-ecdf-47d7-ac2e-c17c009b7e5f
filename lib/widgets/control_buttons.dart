import 'package:flutter/material.dart';

Widget cancelButton(VoidCallback? onPressed) => Padding(
  padding: const EdgeInsets.all(10.0),
  child: Container(
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: Colors.black.withOpacity(0.5),
    ),
    child: IconButton(
      icon: Icon(Icons.close, color: Colors.white),
      onPressed: onPressed,
    ),
  ),
);

Widget confirmButton(VoidCallback? onPressed) => Padding(
  padding: const EdgeInsets.all(10.0),
  child: Container(
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: Colors.black.withOpacity(0.5),
    ),
    child: IconButton(
      icon: Icon(Icons.check, color: Colors.white),
      onPressed: onPressed,
    ),
  ),
);
