import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/screens/home/<USER>/food_post.dart';
import 'package:ai_vanse/screens/home/<USER>/menu_item.dart';
import 'package:ai_vanse/screens/home/<USER>/story_post.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HomeController extends GetxController {
  final pageController = PageController();
  final selectedMainTab = 0.obs;
  final selectedSubTab = 0.obs;
  final selectedBottomTab = 0.obs;
  final userName = '<PERSON>'.obs;

  final foodPosts = <FoodPost>[].obs;
  final storyPosts = <StoryPost>[].obs;

  final menus = <MenuItem>[
    MenuItem(
      icon: Assets.svgs.cutleryInactive.path,
      iconActive: Assets.svgs.cutleryActive.path,
      label: 'home_title'.tr,
    ),
    MenuItem(
      icon: Assets.svgs.carlanderInactive.path,
      iconActive: Assets.svgs.carlanderActive.path,
      label: 'sleep_title'.tr,
    ),
    MenuItem(
      icon: Assets.svgs.addButton.path,
      iconActive: Assets.svgs.addButton.path,
      label: 'user_data_title'.tr,
      iconSize: 43.sp,
    ),
    MenuItem(
      icon: Assets.svgs.heartRateInactive.path,
      iconActive: Assets.svgs.heartRateActive.path,
      label: 'setting_title'.tr,
    ),
    MenuItem(
      icon: Assets.svgs.userInactive.path,
      iconActive: Assets.svgs.userActive.path,
      label: 'setting_title'.tr,
    ),
  ];

  final _menuIndex = 0.obs;

  int get menuIndex => _menuIndex.value;

  set menuIndex(int index) {
    if (index >= 0 && index < menus.length) {
      pageController.jumpToPage(index);
      _menuIndex.value = index;
    }
  }

  @override
  void onInit() {
    super.onInit();
    _loadInitialData();
  }

  void _loadInitialData() {
    foodPosts.addAll([
      FoodPost(
        id: '1',
        userName: 'Ellen Green',
        userProfileImage: 'https://placekitten.com/50/50',
        imageUrl: 'https://images.unsplash.com/photo-**********-ba9599a7e63c',
        description: 'Weightless',
        foodName: 'Healthy Salad Bowl',
        distance: 1.0,
        tags: ['#WeightLess'],
        rating: 3,
        nutrition: Nutrition(
          totalPortionSize: 350.0,
          energy: 350.0,
          calories: 350.0,
          protein: 12.5,
          fat: 15.0,
          carbohydrate: 45.0,
          fiber: 8.5,
          totalSugars: 6.0,
          calcium: 120.0,
          iron: 2.5,
          magnesium: 80.0,
          phosphorus: 150.0,
          potassium: 450.0,
          sodium: 380.0,
          zinc: 1.8,
          cholesterol: 0.0,
        ),
      ),
      FoodPost(
        id: '2',
        userName: 'Jan Dangerfield',
        userProfileImage: 'https://placekitten.com/51/51',
        imageUrl:
            'https://images.unsplash.com/photo-1504674900247-0877df9cc836',
        description: '#Nutrition',
        foodName: 'Grilled Salmon with Vegetables',
        distance: 1.1,
        tags: ['#Nutrition'],
        rating: 1,
        nutrition: Nutrition(
          totalPortionSize: 400.0,
          energy: 450.0,
          calories: 450.0,
          protein: 35.0,
          fat: 22.0,
          carbohydrate: 28.0,
          fiber: 6.0,
          totalSugars: 4.0,
          calcium: 180.0,
          iron: 3.2,
          magnesium: 95.0,
          phosphorus: 320.0,
          potassium: 780.0,
          sodium: 420.0,
          zinc: 2.4,
          cholesterol: 85.0,
        ),
      ),
      FoodPost(
        id: '3',
        userName: 'Daniel Cook',
        userProfileImage: 'https://placekitten.com/52/52',
        imageUrl: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1',
        description: '#BodyBuild',
        foodName: 'High Protein Bowl',
        distance: 1.1,
        tags: ['#BodyBuild'],
        rating: 2,
        nutrition: Nutrition(
          totalPortionSize: 500.0,
          energy: 650.0,
          calories: 650.0,
          protein: 45.0,
          fat: 25.0,
          carbohydrate: 65.0,
          fiber: 7.0,
          totalSugars: 5.0,
          calcium: 250.0,
          iron: 4.5,
          magnesium: 120.0,
          phosphorus: 380.0,
          potassium: 850.0,
          sodium: 520.0,
          zinc: 3.2,
          cholesterol: 95.0,
        ),
      ),
      // Add more mock posts as needed
    ]);

    // Mock data for story posts
    storyPosts.addAll([
      StoryPost(
        userName: 'Dr. Ellen Clark',
        userImage: 'https://placekitten.com/200/200',
        timeAgo: '4 min ago',
        tags: ['#Veggie', '#Fiber', '#WeightLoss'],
        mainImage: 'https://images.unsplash.com/photo-**********-ba9599a7e63c',
        additionalImages: [
          'https://images.unsplash.com/photo-1543362906-acfc16c67564',
          'https://images.unsplash.com/photo-1512621776951-a57141f2eefd',
          'https://images.unsplash.com/photo-1498837167922-ddd27525d352',
          'https://images.unsplash.com/photo-1504674900247-0877df9cc836',
          'https://images.unsplash.com/photo-1493770348161-369560ae357d',
        ],
        title: 'Gaia Veggie',
      ),
      StoryPost(
        userName: 'Daniel Cook',
        userImage: 'https://placekitten.com/201/201',
        timeAgo: '15 min ago',
        tags: ['#Protein', '#BodyBuild', '#Nutrition'],
        mainImage:
            'https://images.unsplash.com/photo-1432139555190-58524dae6a55',
        additionalImages: [
          'https://images.unsplash.com/photo-1542010589005-d1eacc3918f2',
        ],
        title: 'High Protein Meal',
      ),
    ]);
  }
}
