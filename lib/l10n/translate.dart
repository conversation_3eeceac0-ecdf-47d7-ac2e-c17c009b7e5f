import 'dart:ui';

import 'package:ai_vanse/l10n/locales/en.dart';
import 'package:ai_vanse/l10n/locales/ja.dart';
import 'package:ai_vanse/l10n/locales/th.dart';
import 'package:ai_vanse/l10n/locales/zh_cn.dart';
import 'package:ai_vanse/services/preference_service.dart';
import 'package:get/get.dart';

enum AppTranslationLanguage {
  thai('th_TH', Locale('th', 'TH')),
  english('en_US', Locale('en', 'US')),
  japanese('ja_JP', Locale('ja', 'JP')),
  chineseSimplified('zh_CN', Locale('zh', 'CN'));

  final String localeCode;
  final Locale locale;

  const AppTranslationLanguage(this.localeCode, this.locale);

  Map<String, String> get translate => switch (this) {
    AppTranslationLanguage.thai => LocaleTh.data,
    AppTranslationLanguage.english => LocaleEn.data,
    AppTranslationLanguage.japanese => LocaleJa.data,
    AppTranslationLanguage.chineseSimplified => LocaleZhCN.data,
  };

  String get tr => switch (this) {
    AppTranslationLanguage.thai => 'language_thai'.tr,
    AppTranslationLanguage.english => 'language_english'.tr,
    AppTranslationLanguage.japanese => 'language_japanese'.tr,
    AppTranslationLanguage.chineseSimplified =>
      'language_chinese_simplified'.tr,
  };
}

class AppTranslation extends Translations {
  static AppTranslationLanguage get defaultLanguage =>
      AppTranslationLanguage.english;
  static Locale defaultLocale =
      Get.deviceLocale ?? AppTranslationLanguage.english.locale;

  static String get languageCode =>
      Get.locale?.languageCode ?? defaultLocale.languageCode;

  static bool isCurrentLocale(AppTranslationLanguage isLanguageCode) {
    return languageCode == isLanguageCode.locale.languageCode;
  }

  static changeLocale(AppTranslationLanguage languageCode) {
    final prefService = Get.find<PreferenceService>();
    prefService.appLanguage = languageCode;
    Get.updateLocale(languageCode.locale);
  }

  static Locale getLocale(AppTranslationLanguage? languageCode) {
    return (languageCode ?? defaultLanguage).locale;
  }

  static AppTranslationLanguage get currentLanguage {
    return AppTranslationLanguage.values.firstWhere(
      (language) => language.locale.languageCode == languageCode,
      orElse: () => defaultLanguage,
    );
  }

  List<Locale> supportedLocales =
      AppTranslationLanguage.values.map((e) => e.locale).toList();

  Locale getSavedLocale() {
    final prefService = Get.find<PreferenceService>();
    final savedLanguage = prefService.appLanguage;
    return getLocale(savedLanguage);
  }

  @override
  Map<String, Map<String, String>> get keys {
    return {
      for (final AppTranslationLanguage language
          in AppTranslationLanguage.values)
        language.localeCode: language.translate,
    };
  }
}
