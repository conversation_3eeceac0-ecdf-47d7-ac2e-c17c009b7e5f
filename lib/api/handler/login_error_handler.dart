import 'package:ai_vanse/exceptions/dio_exception.dart';
import 'package:dio/dio.dart';
import 'package:get/get_connect/http/src/exceptions/exceptions.dart';

class LoginErrorHandler<T extends Exception> extends DioErrorHandler {
  @override
  Exception fromDioException(DioException exception, StackTrace stackTrace) {
    return switch (exception.response?.statusCode) {
      401 => const BadCredentialException(),
      403 => UnauthorizedException(),
      _ => exception,
    };
  }
}

class BadCredentialException implements Exception {
  const BadCredentialException();

  @override
  String toString() => 'BadCredentialException';
}

class SessionExpiredException implements Exception {
  const SessionExpiredException();

  @override
  String toString() => 'SessionExpiredException';
}
