import 'dart:convert';

import 'package:ai_vanse/widgets/app_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Common {
  static bool _isLoading = false;

  static bool isLoading() => _isLoading;

  static Future<void> showLoading() async {
    if (!_isLoading && !Get.isSnackbarOpen) {
      _isLoading = true;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.dialog(const AppLoading());
      });
    }
  }

  static void hideLoading() {
    if (_isLoading) {
      _isLoading = false;
      Get.back();
    }
  }

  static void hideKeyboard(BuildContext context, {microTask = false}) {
    final currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      if (microTask) {
        Future.microtask(() => FocusManager.instance.primaryFocus!.unfocus());
      } else {
        FocusManager.instance.primaryFocus!.unfocus();
      }
    }
  }

  static String findFirstChar(String name) {
    return name
        .replaceFirst('นาย', '')
        .replaceFirst('นางสาว', '')
        .replaceFirst('นาง', '')
        .trim()
        .split('')
        .where((c) => !['เ', 'แ', 'โ'].contains(c))
        .first
        .toUpperCase();
  }

  static String encodeToBase64(String input) {
    final List<int> bytes = utf8.encode(input);
    final String base64String = base64Encode(bytes);
    return base64String;
  }

  static String decodeFromBase64(String base64String) {
    final List<int> bytes = base64Decode(base64String);
    final String decodedString = utf8.decode(bytes);
    return decodedString;
  }

  static String convertSecondsToHoursMinutes(int totalSeconds) {
    final int hours = totalSeconds ~/ 3600;
    final int minutes = (totalSeconds % 3600) ~/ 60;

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }
}
