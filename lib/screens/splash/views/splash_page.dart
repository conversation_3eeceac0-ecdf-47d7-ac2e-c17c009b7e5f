import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/screens/splash/controller/splash_controller.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  late final SplashController controller = Get.find();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.init();
    });
  }

  @override
  Widget build(BuildContext context) {
    return const AppPageContainer(body: SplashWidget());
  }
}

class SplashWidget extends StatelessWidget {
  const SplashWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Hero(tag: 'logo', child: Assets.images.icon.image(width: 252)),
    );
  }
}
