import 'dart:async';

import 'package:ai_vanse/api/interceptors/http_interceptor.dart';
import 'package:ai_vanse/services/preference_service.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;

class AuthInterceptor extends HttpInterceptor {
  late final PreferenceService _preferenceService = getx.Get.find();

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // final token = await _preferenceService.getIdToken();
    final token = '';

    options.headers.addAll({'Authorization': 'Bearer $token'});

    super.onRequest(options, handler);
  }
}
