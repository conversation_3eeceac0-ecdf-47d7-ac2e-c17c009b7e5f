import 'package:freezed_annotation/freezed_annotation.dart';

part 'success_response_dto.freezed.dart';
part 'success_response_dto.g.dart';

@freezed
abstract class SuccessResponseDto with _$SuccessResponseDto {
  @JsonSerializable(explicitToJson: true)
  const factory SuccessResponseDto({String? success}) = _SuccessResponseDto;

  factory SuccessResponseDto.fromJson(dynamic json) =>
      _$SuccessResponseDtoFromJson(json);
}
