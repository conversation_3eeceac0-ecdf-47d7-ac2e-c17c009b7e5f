import 'package:ai_vanse/screen/camera_screen.dart';
import 'package:ai_vanse/utilities/snackbar.dart';
import 'package:ai_vanse/widgets/control_buttons.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';

class ImagePickerScreen extends StatefulWidget {
  final int maxCount;
  final RequestType requestType;

  const ImagePickerScreen({
    super.key,
    required this.maxCount,
    required this.requestType,
  });

  @override
  State<ImagePickerScreen> createState() => _ImagePickerScreenState();
}

class _ImagePickerScreenState extends State<ImagePickerScreen> {
  final int _sliverGridPageSize = 20;
  int _totalAssets = 0;
  int _page = 0;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreToLoad = true;
  AssetPathEntity? _selectedAlbum;
  List<AssetEntity>? _assets;
  AssetEntity? _selectedAsset;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _initialize() async {
    await _loadAlbums();
    await _loadAssets();
  }

  Future _loadAlbums() async {
    setState(() {
      _isLoading = true;
    });
    final List<AssetPathEntity> albums = await PhotoManager.getAssetPathList(
      type: widget.requestType,
      onlyAll: true,
    );
    if (!mounted) {
      return;
    }

    if (albums.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      showSnackBar(context, 'No albums found.');
      return;
    }

    setState(() {
      _selectedAlbum = albums.first;
    });
  }

  Future _loadAssets() async {
    if (_selectedAlbum == null) {
      setState(() {
        _isLoading = false;
      });
      return;
    }
    _totalAssets = await _selectedAlbum!.assetCountAsync;
    final List<AssetEntity> assets = await _selectedAlbum!.getAssetListPaged(
      page: 0,
      size: _sliverGridPageSize,
    );
    if (!mounted) {
      return;
    }
    setState(() {
      _assets = assets;
      _isLoading = false;
      _hasMoreToLoad = _assets!.length < _totalAssets;
    });
  }

  Future<void> _loadMoreAsset() async {
    final List<AssetEntity> entities = await _selectedAlbum!.getAssetListPaged(
      page: _page + 1,
      size: _sliverGridPageSize,
    );
    if (!mounted) {
      return;
    }
    setState(() {
      _assets!.addAll(entities);
      _page++;
      _hasMoreToLoad = _assets!.length < _totalAssets;
      _isLoadingMore = false;
    });
  }

  void _onBackPressed() {
    Navigator.pop(context);
  }

  void _onAssetConfirmed(AssetEntity asset) {
    Navigator.pop(context, asset);
  }

  void _onAssetSelected(AssetEntity asset) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AssetPreview(
              asset: asset,
              onAssetConfirmed: _onAssetConfirmed,
              onPopBack: _onBackPressed,
            ),
      ),
    );
  }

  void _onCaptureConfirmed(XFile capturedImage) async {
    Navigator.pop(context);
    final AssetEntity image = await PhotoManager.editor.saveImageWithPath(
      capturedImage.path,
      title: capturedImage.path,
    );
    if (mounted) {
      Navigator.pop(context, image);
    }
  }

  Widget _buildAlbumPreview(BuildContext context) {
    final assetGridHeight = MediaQuery.of(context).size.height * 0.15;
    return _assets == null
        ? Container()
        : Positioned(
          bottom: assetGridHeight,
          left: 0,
          right: 0,
          child: SizedBox(
            height: assetGridHeight,
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator.adaptive())
                    : GridView.custom(
                      scrollDirection: Axis.horizontal,
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 1,
                          ),
                      childrenDelegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          if (index == _assets!.length - 10 &&
                              !_isLoadingMore &&
                              _hasMoreToLoad) {
                            _loadMoreAsset();
                          }
                          final AssetEntity entity = _assets![index];
                          return assetWidget(entity);
                        },
                        childCount: _assets!.length,
                        findChildIndexCallback: (Key key) {
                          if (key is ValueKey<int>) {
                            return key.value;
                          }
                          return null;
                        },
                      ),
                    ),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: CameraScreen(onCaptureConfirmed: _onCaptureConfirmed),
            ),
            _buildAlbumPreview(context),
            Align(
              alignment: Alignment.topLeft,
              child: cancelButton(_onBackPressed),
            ),
          ],
        ),
      ),
    );
  }

  Widget assetWidget(AssetEntity asset) => Stack(
    children: [
      Positioned.fill(
        child: Container(
          padding: EdgeInsets.all(_selectedAsset == asset ? 10.0 : 5.0),
          decoration: BoxDecoration(
            border: Border.all(
              color: _selectedAsset == asset ? Colors.blue : Colors.transparent,
              width: 1.0,
            ),
          ),
          child: AssetEntityImage(
            asset,
            isOriginal: false,
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
            thumbnailSize: const ThumbnailSize.square(200),
            errorBuilder:
                (context, error, stackTrace) =>
                    Center(child: Icon(Icons.error_outline, color: Colors.red)),
          ),
        ),
      ),
      Positioned.fill(
        child: GestureDetector(onTap: () => _onAssetSelected(asset)),
      ),
    ],
  );
}

class AssetPreview extends StatelessWidget {
  final AssetEntity asset;
  final void Function(AssetEntity) onAssetConfirmed;
  final void Function() onPopBack;

  const AssetPreview({
    super.key,
    required this.asset,
    required this.onAssetConfirmed,
    required this.onPopBack,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: AssetEntityImage(
                asset,
                isOriginal: false,
                filterQuality: FilterQuality.high,
                fit: BoxFit.contain,
                thumbnailSize: const ThumbnailSize.square(2000),
              ),
            ),
            Align(alignment: Alignment.topLeft, child: cancelButton(onPopBack)),
            Align(
              alignment: Alignment.topRight,
              child: confirmButton(() {
                onPopBack();
                onAssetConfirmed(asset);
              }),
            ),
          ],
        ),
      ),
    );
  }
}
