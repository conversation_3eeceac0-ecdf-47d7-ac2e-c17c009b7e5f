import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewPageController extends GetxController {
  late final Uri uri;
  late final WebViewController webController;
  final RxBool isLoading = RxBool(true);

  @override
  void onInit() {
    super.onInit();
    uri = Get.arguments['uri'];

    webController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageStarted: (_) => isLoading.value = true,
              onHttpError: (_) => isLoading.value = false,
              onWebResourceError: (_) => isLoading.value = false,
              onPageFinished: (_) => isLoading.value = false,
            ),
          );
  }

  @override
  void onReady() {
    super.onReady();
    webController.loadRequest(uri);
  }
}
