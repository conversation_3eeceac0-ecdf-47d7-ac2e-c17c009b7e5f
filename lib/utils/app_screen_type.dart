import 'dart:ui';

enum AppScreenType {
  phoneS,
  phoneM,
  phoneL,
  tablet;

  factory AppScreenType.fromMinSize(double minSize) {
    if (minSize <= 375) return AppScreenType.phoneS;
    if (minSize <= 430) return AppScreenType.phoneM;
    if (minSize <= 700) return AppScreenType.phoneL;
    return AppScreenType.tablet;
  }

  Size get size => switch (this) {
    AppScreenType.phoneS => const Size(375, 812),
    AppScreenType.phoneM => const Size(393, 852),
    AppScreenType.phoneL => const Size(430, 932),
    AppScreenType.tablet => const Size(820, 1180),
  };
}
