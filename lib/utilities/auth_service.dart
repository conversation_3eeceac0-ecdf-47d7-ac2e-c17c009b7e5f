import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  static const String _isLoggedInKey = 'is_logged_in';
  static const String _usernameKey = 'username';
  static const String _loginTimeKey = 'login_time';
  static const int _expirationDays =
      3; // Token will be expired in 3 days since last login

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    bool isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;

    if (!isLoggedIn) {
      return false;
    }

    int? loginTimeMillis = prefs.getInt(_loginTimeKey);
    if (loginTimeMillis == null) {
      return false;
    }

    DateTime loginTime = DateTime.fromMillisecondsSinceEpoch(loginTimeMillis);
    DateTime now = DateTime.now();
    Duration difference = now.difference(loginTime);

    if (difference.inDays >= _expirationDays) {
      await logout();
      return false;
    }

    return true;
  }

  Future<void> setLoggedIn(bool isLoggedIn, {String? username}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, isLoggedIn);

    if (isLoggedIn) {
      await prefs.setInt(_loginTimeKey, DateTime.now().millisecondsSinceEpoch);
    }

    if (username != null) {
      await prefs.setString(_usernameKey, username);
    }
  }

  Future<String?> getUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_usernameKey);
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, false);
  }
}
