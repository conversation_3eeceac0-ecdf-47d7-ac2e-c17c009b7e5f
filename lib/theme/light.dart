import 'package:ai_vanse/extensions/colors_extra_extension.dart';
import 'package:ai_vanse/theme/base.dart';
import 'package:flutter/material.dart';

class LightTheme extends AppTheme {
  @override
  ExtraColors get extraColor => const ExtraColors(
    slate: {
      50: Color(0xFFf8f7f7),
      100: Color(0xFFF0EEEE),
      200: Color(0xFFDDDADA),
      300: Color(0xFFBFBABA),
      400: Color(0xFF9B9595),
      500: Color(0xFF7F7879),
      600: Color(0xFF686162),
      700: Color(0xFF554F4F),
      800: Color(0xFF484444),
      900: Color(0xFF373434),
      950: Color(0xFF2A2727),
    },
    red: {
      50: Color(0xFFFFF0F0),
      100: Color(0xFFFFE2E3),
      200: Color(0xFFFFCACD),
      300: Color(0xFFFF9FA5),
      400: Color(0xFFFF6975),
      500: Color(0xFFFF495C),
      600: Color(0xFFED1132),
      700: Color(0xFFC8082A),
      800: Color(0xFFA8092A),
      900: Color(0xFF8F0C2B),
      950: Color(0xFF500112),
    },
    hint: Color(0xFF99A6B8),
    divider: Color(0xFFE2E4EB),
    unselected: Color(0x33262444),
    selected: Color(0xFF9FE0F2),
    selectedWhite: Color(0xFFFFFFFF),
    progressBG: Color(0xFFCCCCCC),
    defaultTextColor: Color(0xFF1C1C1C),
    energy: Color(0xFF27D045),
    target: Color(0xFFFF6969),
  );

  @override
  ThemeData get baseThemeData => ThemeData.light();

  @override
  Brightness get brightness => Brightness.light;

  @override
  Color get shadow => const Color.fromRGBO(100, 100, 111, 0.2);

  @override
  Color get primary => const Color(0xFF08D2B4);

  @override
  Color get primaryContainer => const Color(0xFFFFFFFF);

  @override
  Color get onPrimary => const Color(0xFF00214E);

  @override
  Color get secondary => const Color(0xFFFF8556);

  @override
  Color get onSecondary => const Color(0xFF323232);

  @override
  Color get tertiary => const Color(0xFF4F58DF);

  @override
  Color get onTertiary => const Color(0xFFFFFFFF);
}
