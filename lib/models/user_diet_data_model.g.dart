// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_diet_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Diet _$DietFromJson(Map<String, dynamic> json) => Diet(
  timestamp: (json['timestamp'] as num).toInt(),
  foodNames: json['foodNames'] as String,
  totalPortionSize: (json['totalPortionSize'] as num).toInt(),
  energy: (json['energy'] as num).toInt(),
  protein: (json['protein'] as num).toInt(),
  fat: (json['fat'] as num).toInt(),
  carbohydrate: (json['carbohydrate'] as num).toInt(),
  fiber: (json['fiber'] as num).toInt(),
  totalSugars: (json['totalSugars'] as num).toInt(),
  calcium: (json['calcium'] as num).toInt(),
  iron: (json['iron'] as num).toInt(),
  magnesium: (json['magnesium'] as num).toInt(),
  phosphorus: (json['phosphorus'] as num).toInt(),
  potassium: (json['potassium'] as num).toInt(),
  sodium: (json['sodium'] as num).toInt(),
  zinc: (json['zinc'] as num).toInt(),
  cholesterol: (json['cholesterol'] as num).toInt(),
  mealType: json['mealType'] as String,
);

Map<String, dynamic> _$DietToJson(Diet instance) => <String, dynamic>{
  'timestamp': instance.timestamp,
  'foodNames': instance.foodNames,
  'totalPortionSize': instance.totalPortionSize,
  'energy': instance.energy,
  'protein': instance.protein,
  'fat': instance.fat,
  'carbohydrate': instance.carbohydrate,
  'fiber': instance.fiber,
  'totalSugars': instance.totalSugars,
  'calcium': instance.calcium,
  'iron': instance.iron,
  'magnesium': instance.magnesium,
  'phosphorus': instance.phosphorus,
  'potassium': instance.potassium,
  'sodium': instance.sodium,
  'zinc': instance.zinc,
  'cholesterol': instance.cholesterol,
  'mealType': instance.mealType,
};
