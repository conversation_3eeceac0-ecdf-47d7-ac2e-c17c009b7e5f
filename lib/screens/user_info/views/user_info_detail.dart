import 'package:ai_vanse/screens/user_info/controllers/user_info_controller.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class UserInfoDetail extends StatefulWidget {
  const UserInfoDetail({super.key});

  @override
  State<UserInfoDetail> createState() => _UserInfoDetailState();
}

class _UserInfoDetailState extends State<UserInfoDetail> {
  final UserInfoController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Center(
            child: AppText(controller.fullname, appTextStyle: AppTextStyle.h3),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  'user_data_gender'.tr,
                  appTextStyle: AppTextStyle.bodyText,
                ),
                Row(
                  children: [
                    AppText(
                      controller.displayGender,
                      appTextStyle: AppTextStyle.bodyText,
                      appTextColor: AppTextColor.onSecondary,
                    ),
                    const Icon(Icons.arrow_forward_ios),
                  ],
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  'user_data_weight'.tr,
                  appTextStyle: AppTextStyle.bodyText,
                ),
                Row(
                  children: [
                    AppText(
                      controller.weight.toString(),
                      appTextStyle: AppTextStyle.bodyText,
                      appTextColor: AppTextColor.onSecondary,
                    ),
                    const Icon(Icons.arrow_forward_ios),
                  ],
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  'user_data_height'.tr,
                  appTextStyle: AppTextStyle.bodyText,
                ),
                Row(
                  children: [
                    AppText(
                      controller.height.toString(),
                      appTextStyle: AppTextStyle.bodyText,
                      appTextColor: AppTextColor.onSecondary,
                    ),
                    const Icon(Icons.arrow_forward_ios),
                  ],
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  'user_data_birthday'.tr,
                  appTextStyle: AppTextStyle.bodyText,
                ),
                Row(
                  children: [
                    AppText(
                      controller.dob != null
                          ? DateFormat('dd/MM/yyyy').format(controller.dob!)
                          : '-',
                      appTextStyle: AppTextStyle.bodyText,
                      appTextColor: AppTextColor.onSecondary,
                    ),
                    const Icon(Icons.arrow_forward_ios),
                  ],
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  'user_data_age'.tr,
                  appTextStyle: AppTextStyle.bodyText,
                ),
                AppText(
                  controller.age.toString(),
                  appTextStyle: AppTextStyle.bodyText,
                  appTextColor: AppTextColor.onSecondary,
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AppText('user_data_bm'.tr, appTextStyle: AppTextStyle.bodyText),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: controller.showBmiInfo,
                  child: Icon(
                    Icons.info_outline,
                    color: context.theme.hintColor,
                  ),
                ),
                const Spacer(),
                AppText(
                  controller.bmiValue!.toInt().toString(),
                  appTextStyle: AppTextStyle.bodyText,
                  appTextColor: AppTextColor.onSecondary,
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  'user_data_bmr'.tr,
                  appTextStyle: AppTextStyle.bodyText,
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: controller.showBmrInfo,
                  child: Icon(
                    Icons.info_outline,
                    color: context.theme.hintColor,
                  ),
                ),
                const Spacer(),
                AppText(
                  controller.bmrValue!.toInt().toString(),
                  appTextStyle: AppTextStyle.bodyText,
                  appTextColor: AppTextColor.onSecondary,
                ),
              ],
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, top: 5),
            child: Divider(),
          ),
        ],
      );
    });
  }
}
