import 'dart:math';

import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vector_math/vector_math_64.dart' as math;

class PinCode extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final bool isPin;
  final MainAxisAlignment mainAxisAlignment;
  final void Function(String)? onChange;
  final Future<String?> Function(String) onComplete;
  final int length;

  const PinCode({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onComplete,
    this.onChange,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.isPin = false,
    this.length = 6,
  });

  @override
  State<PinCode> createState() => PinCodeState();
}

class PinCodeState extends State<PinCode> with SingleTickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> animation;
  final boxSizeWidth = 42.sp;
  final boxSizeHeight = 48.sp;
  final pinSize = 24.sp;
  var isError = false;
  var errorMessage = '';
  late List<String> codes = List.filled(widget.length, '');

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..addListener(() => setState(() {}));

    animation = Tween<double>(
      begin: 00.0,
      end: 120.0,
    ).animate(animationController);
  }

  @override
  void dispose() {
    animationController.dispose();
    super.dispose();
  }

  math.Vector3 _shake() {
    final progress = animationController.value;
    // change 10 to make it vibrate faster
    final offset = sin(progress * pi * 10.0);
    // change 25 to make it vibrate wider
    return math.Vector3(offset * 25, 0.0, 0.0);
  }

  void error({withShake = false}) {
    setState(() {
      isError = true;
      if (withShake) {
        shake();
      }
    });
  }

  void shake() {
    animationController.forward(from: 0);
  }

  void focus() {
    if (widget.focusNode.hasFocus) {
      widget.focusNode.unfocus();
    }

    Future.microtask(widget.focusNode.requestFocus);
  }

  void generateValue({clearError = false}) {
    setState(() {
      final value = widget.controller.text;
      codes = [
        ...value.split(''),
        ...List.filled(widget.length, ''),
      ].sublist(0, widget.length);

      if (clearError) {
        isError = false;
        errorMessage = '';
      }
    });
  }

  Widget buildBox(ThemeData theme, int index) {
    final currentVal = codes[index];
    var isFocus = false;
    if (widget.focusNode.hasFocus) {
      if (index == 0 && currentVal.isEmpty) {
        isFocus = true;
      } else if (currentVal.isEmpty && codes[index - 1].isNotEmpty) {
        isFocus = true;
      }
    }
    var borderColor = Colors.grey.shade500;
    if (isFocus) {
      borderColor = theme.colorScheme.primary;
    }
    if (isError) {
      borderColor = theme.colorScheme.error;
    }

    final value = codes[index];

    return AnimatedContainer(
      height: widget.isPin ? pinSize : boxSizeHeight,
      width: widget.isPin ? pinSize : boxSizeWidth,
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        shape: widget.isPin ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: widget.isPin ? null : BorderRadius.circular(8.r),
        border: Border.all(width: 1.sp, color: borderColor),
      ),
      duration: const Duration(milliseconds: 400),
      child:
          widget.isPin
              ? Container(
                decoration: BoxDecoration(
                  color:
                      value.isNotEmpty
                          ? theme.colorScheme.primaryContainer
                          : theme.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                width: double.infinity,
                height: double.infinity,
              )
              : Center(child: AppText(value, appTextStyle: AppTextStyle.h3)),
    );
  }

  Widget buildFields(ThemeData theme) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.sp),
          child: AppText(
            isError && errorMessage.isNotEmpty ? errorMessage : '',
            appTextStyle: AppTextStyle.bodyText,
            appTextColor: AppTextColor.error,
          ),
        ),
        GestureDetector(
          key: const Key('lbl_pin'),
          onTap: focus,
          child: Transform(
            transform: Matrix4.translation(_shake()),
            child: SizedBox(
              width: boxSizeWidth * (widget.length + 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(
                  widget.length,
                  (index) => buildBox(theme, index),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Stack(
      alignment: Alignment.center,
      children: [
        Opacity(
          opacity: 0,
          child: AbsorbPointer(
            absorbing: true,
            child: TextFormField(
              key: const Key('txt_pin'),
              controller: widget.controller,
              focusNode: widget.focusNode,
              enableInteractiveSelection: false,
              showCursor: false,
              autofocus: false,
              autocorrect: false,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.none,
              keyboardType: TextInputType.number,
              autofillHints: const [AutofillHints.oneTimeCode],
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(widget.length),
              ],
              style: const TextStyle(height: 0),
              onChanged: (value) async {
                generateValue(clearError: true);
                if (widget.onChange != null) {
                  widget.onChange!(value);
                }
                if (value.length == widget.length) {
                  final errMsg = await widget.onComplete(value);
                  setState(() {
                    isError = errMsg?.isNotEmpty == true;
                    if (isError) {
                      widget.controller.text = '';
                      errorMessage = errMsg ?? '';
                      generateValue();
                      shake();
                    }
                  });
                }
              },
            ),
          ),
        ),
        buildFields(theme),
      ],
    );
  }
}
