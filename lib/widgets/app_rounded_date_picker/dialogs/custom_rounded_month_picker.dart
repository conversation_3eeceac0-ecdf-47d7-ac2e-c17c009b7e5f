// ignore_for_file: implementation_imports

import 'dart:async';

import 'package:ai_vanse/widgets/app_rounded_date_picker/widget/custom_rounded_day_picker.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:flutter_rounded_date_picker/src/era_mode.dart';
import 'package:flutter_rounded_date_picker/src/material_rounded_date_picker_style.dart';
import 'package:flutter_rounded_date_picker/src/thai_date_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// A scrollable list of months to allow picking a month.
///
/// Shows the days of each month in a rectangular grid with one column for each
/// day of the week.
///
/// The month picker widget is rarely used directly. Instead, consider using
/// [showDatePicker], which creates a date picker dialog.
///
/// See also:
///
///  * [showDatePicker], which shows a dialog that contains a material design
///    date picker.
///  * [showTimePicker], which shows a dialog that contains a material design
///    time picker.
///

const Duration _kMonthScrollDuration = Duration(milliseconds: 200);

class CustomRoundedMonthPicker extends StatefulWidget {
  /// Creates a month picker.
  ///
  /// Rarely used directly. Instead, typically used as part of the dialog shown
  /// by [showDatePicker].
  CustomRoundedMonthPicker({
    super.key,
    required this.selectedDate,
    required this.onChanged,
    required this.firstDate,
    required this.lastDate,
    this.selectableDayPredicate,
    this.dragStartBehavior = DragStartBehavior.start,
    required this.era,
    this.locale,
    this.fontFamily,
    this.style,
    this.borderRadius = 0,
    this.customWeekDays,
    this.builderDay,
    this.listDateDisabled,
    this.onTapDay,
    this.onMonthChange,
    this.singleTap = false,
    required this.handleOk,
  }) : assert(!firstDate.isAfter(lastDate));

  /// The currently selected date.
  ///
  /// This date is highlighted in the picker.
  final DateTime selectedDate;

  /// Called when the user picks a month.
  final ValueChanged<DateTime> onChanged;

  /// The earliest date the user is permitted to pick.
  final DateTime firstDate;

  /// The latest date the user is permitted to pick.
  final DateTime lastDate;

  /// Optional user supplied predicate function to customize selectable days.
  final SelectableDayPredicate? selectableDayPredicate;

  /// {@macro flutter.widgets.scrollable.dragStartBehavior}
  final DragStartBehavior dragStartBehavior;

  /// Optional era year.
  final EraMode era;
  final Locale? locale;

  /// Font
  final String? fontFamily;

  /// Style
  final MaterialRoundedDatePickerStyle? style;

  final double borderRadius;

  /// Custom Weekday.
  final List<String>? customWeekDays;

  final BuilderDayOfDatePicker? builderDay;

  final List<DateTime>? listDateDisabled;
  final OnTapDay? onTapDay;

  final Function? onMonthChange;

  final bool singleTap;
  final VoidCallback handleOk;

  @override
  CustomRoundedMonthPickerState createState() =>
      CustomRoundedMonthPickerState();
}

class CustomRoundedMonthPickerState extends State<CustomRoundedMonthPicker>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    // Initially display the pre-selected date.
    final int monthPage = _monthDelta(widget.firstDate, widget.selectedDate);
    _dayPickerController = PageController(initialPage: monthPage);
    _handleMonthPageChanged(monthPage);
    _updateCurrentDate();

    // Setup the fade animation for chevrons
    _chevronOpacityController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await _onMonthChange(_currentDisplayedMonthDate);
    });
  }

  @override
  void didUpdateWidget(CustomRoundedMonthPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedDate != oldWidget.selectedDate) {
      final int monthPage = _monthDelta(widget.firstDate, widget.selectedDate);
      _dayPickerController = PageController(initialPage: monthPage);
      _handleMonthPageChanged(monthPage);
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
        await _onMonthChange(_currentDisplayedMonthDate);
      });
    }
  }

  late MaterialLocalizations localizations;
  late TextDirection textDirection;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    localizations = MaterialLocalizations.of(context);
    textDirection = Directionality.of(context);
  }

  late DateTime _todayDate;
  late DateTime _currentDisplayedMonthDate;
  Timer? _timer;
  late PageController _dayPickerController;
  late AnimationController _chevronOpacityController;

  void _updateCurrentDate() {
    _todayDate = DateTime.now();
    final DateTime tomorrow = DateTime(
      _todayDate.year,
      _todayDate.month,
      _todayDate.day + 1,
    );
    Duration timeUntilTomorrow = tomorrow.difference(_todayDate);
    // so we don't miss it by rounding
    timeUntilTomorrow += const Duration(seconds: 1);
    _timer?.cancel();
    _timer = Timer(timeUntilTomorrow, () {
      setState(_updateCurrentDate);
    });
  }

  static int _monthDelta(DateTime startDate, DateTime endDate) {
    return (endDate.year - startDate.year) * 12 +
        endDate.month -
        startDate.month;
  }

  /// Add months to a month truncated date.
  DateTime _addMonthsToMonthDate(DateTime monthDate, int monthsToAdd) {
    return DateTime(
      monthDate.year + monthsToAdd ~/ 12,
      monthDate.month + monthsToAdd % 12,
    );
  }

  Widget _buildItems(BuildContext context, int index) {
    final DateTime month = _addMonthsToMonthDate(widget.firstDate, index);
    return CustomRoundedDayPicker(
      key: ValueKey<DateTime>(month),
      selectedDate: widget.selectedDate,
      currentDate: _todayDate,
      onChanged: widget.onChanged,
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
      displayedMonth: month,
      selectableDayPredicate: widget.selectableDayPredicate,
      dragStartBehavior: widget.dragStartBehavior,
      era: widget.era,
      locale: widget.locale,
      fontFamily: widget.fontFamily,
      style: widget.style,
      borderRadius: widget.borderRadius,
      customWeekDays: widget.customWeekDays,
      builderDay: widget.builderDay,
      listDateDisabled: widget.listDateDisabled,
      onTapDay: widget.onTapDay,
      singleTap: widget.singleTap,
      handleOK: widget.handleOk,
    );
  }

  Future<void> _handleNextMonth() async {
    if (!_isDisplayingLastMonth) {
      SemanticsService.announce(
        _formatMonthYear(_nextMonthDate),
        textDirection,
      );
      _dayPickerController.nextPage(
        duration: _kMonthScrollDuration,
        curve: Curves.ease,
      );
    }
  }

  Future<void> _handlePreviousMonth() async {
    if (!_isDisplayingFirstMonth) {
      SemanticsService.announce(
        _formatMonthYear(_previousMonthDate),
        textDirection,
      );
      _dayPickerController.previousPage(
        duration: _kMonthScrollDuration,
        curve: Curves.ease,
      );
    }
  }

  /// True if the earliest allowable month is displayed.
  bool get _isDisplayingFirstMonth {
    return !_currentDisplayedMonthDate.isAfter(
      DateTime(widget.firstDate.year, widget.firstDate.month),
    );
  }

  /// True if the latest allowable month is displayed.
  bool get _isDisplayingLastMonth {
    return !_currentDisplayedMonthDate.isBefore(
      DateTime(widget.lastDate.year, widget.lastDate.month),
    );
  }

  late DateTime _previousMonthDate;
  late DateTime _nextMonthDate;

  String _formatMonthYear(DateTime dateTime) {
    return '${ThaiDateUtils.getMonthNameFull(dateTime.month)} ${calculateYearEra(widget.era, dateTime.year)}';
  }

  void _handleMonthPageChanged(int monthPage) {
    setState(() {
      _previousMonthDate = _addMonthsToMonthDate(
        widget.firstDate,
        monthPage - 1,
      );
      _currentDisplayedMonthDate = _addMonthsToMonthDate(
        widget.firstDate,
        monthPage,
      );
      _nextMonthDate = _addMonthsToMonthDate(widget.firstDate, monthPage + 1);
    });
  }

  Future<void> _onMonthChange(DateTime newMonth) async {
    if (widget.onMonthChange != null) await widget.onMonthChange!(newMonth);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final Orientation orientation = MediaQuery.of(context).orientation;
    return Container(
      decoration: BoxDecoration(
        color: widget.style?.backgroundPicker,
        borderRadius:
            orientation == Orientation.landscape
                ? BorderRadius.only(
                  topRight: Radius.circular(widget.borderRadius),
                )
                : null,
      ),
      // The month picker just adds month navigation to the day picker, so make
      // it the same height as the DayPicker
      //      height: _kMaxDayPickerHeight,
      child: Stack(
        children: <Widget>[
          Semantics(
            sortKey: _MonthPickerSortKey.calendar,
            child: NotificationListener<ScrollStartNotification>(
              onNotification: (_) {
                _chevronOpacityController.forward();
                return false;
              },
              child: NotificationListener<ScrollEndNotification>(
                onNotification: (_) {
                  _chevronOpacityController.reverse();
                  return false;
                },
                child: PageView.builder(
                  dragStartBehavior: widget.dragStartBehavior,
                  key: ValueKey<DateTime>(widget.selectedDate),
                  controller: _dayPickerController,
                  scrollDirection: Axis.horizontal,
                  itemCount: _monthDelta(widget.firstDate, widget.lastDate) + 1,
                  itemBuilder: _buildItems,
                  onPageChanged: (int monthPage) async {
                    _handleMonthPageChanged(monthPage);
                    await _onMonthChange(_currentDisplayedMonthDate);
                  },
                ),
              ),
            ),
          ),
          _buildArrowLeft(),
          _buildArrowRight(),
        ],
      ),
    );
  }

  Widget _buildArrowLeft() {
    return PositionedDirectional(
      top: widget.style?.marginLeftArrowPrevious ?? 0.sp,
      start: widget.style?.marginLeftArrowPrevious ?? 8.sp,
      child: Semantics(
        sortKey: _MonthPickerSortKey.previousMonth,
        child: IconButton(
          color: widget.style?.colorArrowPrevious,
          disabledColor: widget.style?.colorArrowPrevious?.withAlpha(76),
          iconSize: widget.style?.sizeArrow,
          icon: const Icon(Icons.chevron_left),
          tooltip:
              _isDisplayingFirstMonth
                  ? null
                  : '${localizations.previousMonthTooltip} ${_formatMonthYear(_previousMonthDate)}',
          onPressed: _isDisplayingFirstMonth ? null : _handlePreviousMonth,
        ),
      ),
    );
  }

  Widget _buildArrowRight() {
    return PositionedDirectional(
      top: widget.style?.marginTopArrowNext ?? 0,
      end: widget.style?.marginRightArrowNext ?? 8.sp,
      child: Semantics(
        sortKey: _MonthPickerSortKey.nextMonth,
        child: IconButton(
          color: widget.style?.colorArrowNext,
          disabledColor: widget.style?.colorArrowNext?.withAlpha(76),
          iconSize: widget.style?.sizeArrow,
          icon: const Icon(Icons.chevron_right),
          tooltip:
              _isDisplayingLastMonth
                  ? null
                  : '${localizations.nextMonthTooltip} ${_formatMonthYear(_nextMonthDate)}',
          onPressed: _isDisplayingLastMonth ? null : _handleNextMonth,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _chevronOpacityController.dispose();
    _dayPickerController.dispose();
    super.dispose();
  }
}

// Defines semantic traversal order of the top-level widgets inside the month
// picker.
class _MonthPickerSortKey extends OrdinalSortKey {
  const _MonthPickerSortKey(super.order);

  static const _MonthPickerSortKey previousMonth = _MonthPickerSortKey(1.0);
  static const _MonthPickerSortKey nextMonth = _MonthPickerSortKey(2.0);
  static const _MonthPickerSortKey calendar = _MonthPickerSortKey(3.0);
}
