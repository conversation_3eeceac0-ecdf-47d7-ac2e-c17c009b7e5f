// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'renew_token_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RenewTokenDto {

 int? get id; String? get user; String? get role; String? get token;@JsonKey(name: 'IdToken') String? get idToken;
/// Create a copy of RenewTokenDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RenewTokenDtoCopyWith<RenewTokenDto> get copyWith => _$RenewTokenDtoCopyWithImpl<RenewTokenDto>(this as RenewTokenDto, _$identity);

  /// Serializes this RenewTokenDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RenewTokenDto&&(identical(other.id, id) || other.id == id)&&(identical(other.user, user) || other.user == user)&&(identical(other.role, role) || other.role == role)&&(identical(other.token, token) || other.token == token)&&(identical(other.idToken, idToken) || other.idToken == idToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,user,role,token,idToken);

@override
String toString() {
  return 'RenewTokenDto(id: $id, user: $user, role: $role, token: $token, idToken: $idToken)';
}


}

/// @nodoc
abstract mixin class $RenewTokenDtoCopyWith<$Res>  {
  factory $RenewTokenDtoCopyWith(RenewTokenDto value, $Res Function(RenewTokenDto) _then) = _$RenewTokenDtoCopyWithImpl;
@useResult
$Res call({
 int? id, String? user, String? role, String? token,@JsonKey(name: 'IdToken') String? idToken
});




}
/// @nodoc
class _$RenewTokenDtoCopyWithImpl<$Res>
    implements $RenewTokenDtoCopyWith<$Res> {
  _$RenewTokenDtoCopyWithImpl(this._self, this._then);

  final RenewTokenDto _self;
  final $Res Function(RenewTokenDto) _then;

/// Create a copy of RenewTokenDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = freezed,Object? user = freezed,Object? role = freezed,Object? token = freezed,Object? idToken = freezed,}) {
  return _then(_self.copyWith(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String?,token: freezed == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String?,idToken: freezed == idToken ? _self.idToken : idToken // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _RenewTokenDto implements RenewTokenDto {
  const _RenewTokenDto({this.id, this.user, this.role, this.token, @JsonKey(name: 'IdToken') this.idToken});
  factory _RenewTokenDto.fromJson(Map<String, dynamic> json) => _$RenewTokenDtoFromJson(json);

@override final  int? id;
@override final  String? user;
@override final  String? role;
@override final  String? token;
@override@JsonKey(name: 'IdToken') final  String? idToken;

/// Create a copy of RenewTokenDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RenewTokenDtoCopyWith<_RenewTokenDto> get copyWith => __$RenewTokenDtoCopyWithImpl<_RenewTokenDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RenewTokenDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RenewTokenDto&&(identical(other.id, id) || other.id == id)&&(identical(other.user, user) || other.user == user)&&(identical(other.role, role) || other.role == role)&&(identical(other.token, token) || other.token == token)&&(identical(other.idToken, idToken) || other.idToken == idToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,user,role,token,idToken);

@override
String toString() {
  return 'RenewTokenDto(id: $id, user: $user, role: $role, token: $token, idToken: $idToken)';
}


}

/// @nodoc
abstract mixin class _$RenewTokenDtoCopyWith<$Res> implements $RenewTokenDtoCopyWith<$Res> {
  factory _$RenewTokenDtoCopyWith(_RenewTokenDto value, $Res Function(_RenewTokenDto) _then) = __$RenewTokenDtoCopyWithImpl;
@override @useResult
$Res call({
 int? id, String? user, String? role, String? token,@JsonKey(name: 'IdToken') String? idToken
});




}
/// @nodoc
class __$RenewTokenDtoCopyWithImpl<$Res>
    implements _$RenewTokenDtoCopyWith<$Res> {
  __$RenewTokenDtoCopyWithImpl(this._self, this._then);

  final _RenewTokenDto _self;
  final $Res Function(_RenewTokenDto) _then;

/// Create a copy of RenewTokenDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = freezed,Object? user = freezed,Object? role = freezed,Object? token = freezed,Object? idToken = freezed,}) {
  return _then(_RenewTokenDto(
id: freezed == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int?,user: freezed == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as String?,role: freezed == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String?,token: freezed == token ? _self.token : token // ignore: cast_nullable_to_non_nullable
as String?,idToken: freezed == idToken ? _self.idToken : idToken // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
