import 'dart:io';

import 'package:ai_vanse/extensions/device_info_extension.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

class IdentifierService extends GetxService {
  String _version = '';

  String get version => _version;

  String _buildNumber = '';

  String get buildNumber => _buildNumber;

  String _udid = '';

  String get udid {
    return _udid;
  }

  bool _isAndroidQAndAbove = false;

  bool get isAndroidQAndAbove => _isAndroidQAndAbove;

  Future<void> init() {
    return Future.wait([_setVersion(), _setUdid(), _setAndroidQAndAbove()]);
  }

  Future<void> _setVersion() async {
    final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    _version = packageInfo.version;
    _buildNumber = packageInfo.buildNumber;
  }

  Future<void> _setUdid() async {
    _udid = await FlutterUdid.consistentUdid;
  }

  Future<void> _setAndroidQAndAbove() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidDeviceInfo = await deviceInfo.androidInfo;
      _isAndroidQAndAbove = androidDeviceInfo.isAndroidQAndAbove;
    }
  }
}
