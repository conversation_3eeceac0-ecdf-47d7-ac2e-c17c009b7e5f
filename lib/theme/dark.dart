import 'package:ai_vanse/extensions/colors_extra_extension.dart';
import 'package:ai_vanse/theme/base.dart';
import 'package:flutter/material.dart';

class DarkTheme extends AppTheme {
  @override
  ExtraColors get extraColor => const ExtraColors(
    slate: {
      50: Color(0xff020202),
      100: Color(0xFF171717),
      200: Color(0xFF2C2B2B),
      300: Color(0xFF6E6B6B),
      400: Color(0xFF9B9595),
      500: Color(0xFFA59C9D),
      600: Color(0xFFB3A7A9),
      700: Color(0xFFC8BABA),
      800: Color(0xFFBDB4B4),
      900: Color(0xFFCDC1C1),
      950: Color(0xFFDDCECE),
    },
    red: {
      50: Color(0xFF090808),
      100: Color(0xFF3E3637),
      200: Color(0xFF4E3E3E),
      300: Color(0xFF372123),
      400: Color(0xFF502124),
      500: Color(0xFF611B22),
      600: Color(0xFF630715),
      700: Color(0xFF730418),
      800: Color(0xFFA8092A),
      900: Color(0xFFB81038),
      950: Color(0xFFD1032F),
    },
    hint: Color(0xFF99A6B8),
    divider: Color(0xFFE2E4EB),
    unselected: Color(0x33262444),
    selected: Color(0xFF9FE0F2),
    selectedWhite: Color(0xFFFFFFFF),
    progressBG: Color(0xFFCCCCCC),
    defaultTextColor: Color(0xFF1C1C1C),
    energy: Color(0xFF27D045),
    target: Color(0xFFFF6969),
  );

  @override
  ThemeData get baseThemeData => ThemeData.dark();

  @override
  Brightness get brightness => Brightness.dark;

  @override
  Color get shadow => const Color.fromRGBO(100, 100, 111, 0.2);

  @override
  Color get primary => const Color(0xFFD30A0F);

  @override
  Color get primaryContainer => const Color(0xFFFFFFFF);

  @override
  Color get onPrimary => const Color(0xFF424242);

  @override
  Color get secondary => const Color(0xff605555);

  @override
  Color get onSecondary => const Color(0xFFFFFFFF);

  @override
  Color get tertiary => const Color(0xFF4F58DF);

  @override
  Color get onTertiary => const Color(0xFF424242);
}
