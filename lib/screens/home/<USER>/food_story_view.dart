import 'package:ai_vanse/screens/home/<USER>/home_controller.dart';
import 'package:ai_vanse/screens/home/<USER>/story_post.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class FoodStoryView extends GetView<HomeController> {
  const FoodStoryView({Key? key}) : super(key: key);

  String getInitials(String name) {
    if (name.isEmpty) return '';
    final names = name.split(' ');
    return names[0][0].toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 20.sp),
        _buildUserStoryList(),
        Expanded(child: _buildStoryFeed()),
      ],
    );
  }

  Widget _buildUserStoryList() {
    return SizedBox(
      height: 100.sp,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.sp),
        children: [
          _buildStoryAvatar('My Story', isFirst: true),
          _buildStoryAvatar('Daniel'),
          _buildStoryAvatar('Jan'),
          _buildStoryAvatar('Dr.Connie'),
          _buildStoryAvatar('Dr.Ellen'),
        ],
      ),
    );
  }

  Widget _buildStoryAvatar(String name, {bool isFirst = false}) {
    return Container(
      width: 70.sp,
      margin: EdgeInsets.only(right: 12.sp),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isFirst ? Colors.grey : Colors.green,
                width: 2.sp,
              ),
            ),
            child: CircleAvatar(
              radius: 30.sp,
              backgroundColor: Colors.green,
              child: Text(
                getInitials(name),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 20.sp,
                ),
              ),
            ),
          ),
          SizedBox(height: 4.sp),
          Text(
            name,
            style: TextStyle(fontSize: 12.sp),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStoryFeed() {
    return ListView.builder(
      padding: EdgeInsets.all(16.sp),
      itemCount: controller.storyPosts.length,
      itemBuilder: (context, index) {
        final post = controller.storyPosts[index];
        return _buildStoryCard(post);
      },
    );
  }

  Widget _buildStoryCard(StoryPost post) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.sp),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.sp)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(12.sp),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20.sp,
                  backgroundColor: Colors.green,
                  child: Text(
                    getInitials(post.userName),
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                    ),
                  ),
                ),
                SizedBox(width: 12.sp),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.userName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14.sp,
                        ),
                      ),
                      Text(
                        post.timeAgo,
                        style: TextStyle(color: Colors.grey, fontSize: 12.sp),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Text(
                      'Followed',
                      style: TextStyle(color: Colors.black, fontSize: 12.sp),
                    ),
                    SizedBox(width: 8.sp),
                    Icon(Icons.more_vert, color: Colors.grey),
                  ],
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.sp),
            child: Wrap(
              spacing: 8.sp,
              children:
                  post.tags
                      .map(
                        (tag) => Text(
                          tag,
                          style: TextStyle(color: Colors.grey, fontSize: 12.sp),
                        ),
                      )
                      .toList(),
            ),
          ),
          SizedBox(height: 8.sp),
          if (post.additionalImages.isEmpty)
            CachedNetworkImage(
              imageUrl: post.mainImage,
              height: 196.sp,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          if (post.additionalImages.length == 1)
            Column(
              children: [
                CachedNetworkImage(
                  imageUrl: post.mainImage,
                  height: 196.sp,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
                SizedBox(height: 8.sp),
                CachedNetworkImage(
                  imageUrl: post.additionalImages[0],
                  height: 196.sp,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ],
            ),
          if (post.additionalImages.length >= 2)
            Padding(
              padding: EdgeInsets.all(12.sp),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 7,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.sp),
                      child: CachedNetworkImage(
                        imageUrl: post.mainImage,
                        height: 196.sp,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.sp),
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.sp),
                          child: Image.network(
                            post.additionalImages[0],
                            height: 92.sp,
                            width: 92.sp,
                            fit: BoxFit.cover,
                          ),
                        ),
                        SizedBox(height: 8.sp),
                        if (post.additionalImages.length > 1)
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8.sp),
                            child: Stack(
                              children: [
                                CachedNetworkImage(
                                  imageUrl: post.additionalImages[1],
                                  height: 92.sp,
                                  width: 92.sp,
                                  fit: BoxFit.cover,
                                ),
                                if (post.additionalImages.length > 2)
                                  Container(
                                    height: 92.sp,
                                    width: 92.sp,
                                    color: Colors.black54,
                                    child: Center(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Icons.image,
                                            color: Colors.white,
                                            size: 24.sp,
                                          ),
                                          SizedBox(width: 4.sp),
                                          Text(
                                            '+${post.additionalImages.length - 2}',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 20.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          Padding(
            padding: EdgeInsets.all(12.sp),
            child: Text(
              post.title,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.normal),
            ),
          ),
        ],
      ),
    );
  }
}
