// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'overpass.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OverpassResponse _$OverpassResponseFromJson(Map<String, dynamic> json) =>
    OverpassResponse(
      elements:
          (json['elements'] as List<dynamic>)
              .map((e) => Poi.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$OverpassResponseToJson(OverpassResponse instance) =>
    <String, dynamic>{'elements': instance.elements};

Poi _$PoiFromJson(Map<String, dynamic> json) => Poi(
  id: (json['id'] as num).toInt(),
  lat: (json['lat'] as num?)?.toDouble(),
  lon: (json['lon'] as num?)?.toDouble(),
  tags: json['tags'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$PoiToJson(Poi instance) => <String, dynamic>{
  'id': instance.id,
  'lat': instance.lat,
  'lon': instance.lon,
  'tags': instance.tags,
};
