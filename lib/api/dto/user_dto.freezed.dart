// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserDto {

 int get id; String get userName; String? get email; String? get fullname; String get firstName; String get lastName; String? get tel; String? get subgroupPermission; String get tenantPermission; int get roleId; UserRoleDto get roles;
/// Create a copy of UserDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserDtoCopyWith<UserDto> get copyWith => _$UserDtoCopyWithImpl<UserDto>(this as UserDto, _$identity);

  /// Serializes this UserDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserDto&&(identical(other.id, id) || other.id == id)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.email, email) || other.email == email)&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.tel, tel) || other.tel == tel)&&(identical(other.subgroupPermission, subgroupPermission) || other.subgroupPermission == subgroupPermission)&&(identical(other.tenantPermission, tenantPermission) || other.tenantPermission == tenantPermission)&&(identical(other.roleId, roleId) || other.roleId == roleId)&&(identical(other.roles, roles) || other.roles == roles));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userName,email,fullname,firstName,lastName,tel,subgroupPermission,tenantPermission,roleId,roles);

@override
String toString() {
  return 'UserDto(id: $id, userName: $userName, email: $email, fullname: $fullname, firstName: $firstName, lastName: $lastName, tel: $tel, subgroupPermission: $subgroupPermission, tenantPermission: $tenantPermission, roleId: $roleId, roles: $roles)';
}


}

/// @nodoc
abstract mixin class $UserDtoCopyWith<$Res>  {
  factory $UserDtoCopyWith(UserDto value, $Res Function(UserDto) _then) = _$UserDtoCopyWithImpl;
@useResult
$Res call({
 int id, String userName, String? email, String? fullname, String firstName, String lastName, String? tel, String? subgroupPermission, String tenantPermission, int roleId, UserRoleDto roles
});


$UserRoleDtoCopyWith<$Res> get roles;

}
/// @nodoc
class _$UserDtoCopyWithImpl<$Res>
    implements $UserDtoCopyWith<$Res> {
  _$UserDtoCopyWithImpl(this._self, this._then);

  final UserDto _self;
  final $Res Function(UserDto) _then;

/// Create a copy of UserDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userName = null,Object? email = freezed,Object? fullname = freezed,Object? firstName = null,Object? lastName = null,Object? tel = freezed,Object? subgroupPermission = freezed,Object? tenantPermission = null,Object? roleId = null,Object? roles = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,fullname: freezed == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String?,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,tel: freezed == tel ? _self.tel : tel // ignore: cast_nullable_to_non_nullable
as String?,subgroupPermission: freezed == subgroupPermission ? _self.subgroupPermission : subgroupPermission // ignore: cast_nullable_to_non_nullable
as String?,tenantPermission: null == tenantPermission ? _self.tenantPermission : tenantPermission // ignore: cast_nullable_to_non_nullable
as String,roleId: null == roleId ? _self.roleId : roleId // ignore: cast_nullable_to_non_nullable
as int,roles: null == roles ? _self.roles : roles // ignore: cast_nullable_to_non_nullable
as UserRoleDto,
  ));
}
/// Create a copy of UserDto
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserRoleDtoCopyWith<$Res> get roles {
  
  return $UserRoleDtoCopyWith<$Res>(_self.roles, (value) {
    return _then(_self.copyWith(roles: value));
  });
}
}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _UserDto implements UserDto {
  const _UserDto({required this.id, required this.userName, this.email, this.fullname, required this.firstName, required this.lastName, this.tel, this.subgroupPermission, required this.tenantPermission, required this.roleId, required this.roles});
  factory _UserDto.fromJson(Map<String, dynamic> json) => _$UserDtoFromJson(json);

@override final  int id;
@override final  String userName;
@override final  String? email;
@override final  String? fullname;
@override final  String firstName;
@override final  String lastName;
@override final  String? tel;
@override final  String? subgroupPermission;
@override final  String tenantPermission;
@override final  int roleId;
@override final  UserRoleDto roles;

/// Create a copy of UserDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserDtoCopyWith<_UserDto> get copyWith => __$UserDtoCopyWithImpl<_UserDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserDto&&(identical(other.id, id) || other.id == id)&&(identical(other.userName, userName) || other.userName == userName)&&(identical(other.email, email) || other.email == email)&&(identical(other.fullname, fullname) || other.fullname == fullname)&&(identical(other.firstName, firstName) || other.firstName == firstName)&&(identical(other.lastName, lastName) || other.lastName == lastName)&&(identical(other.tel, tel) || other.tel == tel)&&(identical(other.subgroupPermission, subgroupPermission) || other.subgroupPermission == subgroupPermission)&&(identical(other.tenantPermission, tenantPermission) || other.tenantPermission == tenantPermission)&&(identical(other.roleId, roleId) || other.roleId == roleId)&&(identical(other.roles, roles) || other.roles == roles));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,userName,email,fullname,firstName,lastName,tel,subgroupPermission,tenantPermission,roleId,roles);

@override
String toString() {
  return 'UserDto(id: $id, userName: $userName, email: $email, fullname: $fullname, firstName: $firstName, lastName: $lastName, tel: $tel, subgroupPermission: $subgroupPermission, tenantPermission: $tenantPermission, roleId: $roleId, roles: $roles)';
}


}

/// @nodoc
abstract mixin class _$UserDtoCopyWith<$Res> implements $UserDtoCopyWith<$Res> {
  factory _$UserDtoCopyWith(_UserDto value, $Res Function(_UserDto) _then) = __$UserDtoCopyWithImpl;
@override @useResult
$Res call({
 int id, String userName, String? email, String? fullname, String firstName, String lastName, String? tel, String? subgroupPermission, String tenantPermission, int roleId, UserRoleDto roles
});


@override $UserRoleDtoCopyWith<$Res> get roles;

}
/// @nodoc
class __$UserDtoCopyWithImpl<$Res>
    implements _$UserDtoCopyWith<$Res> {
  __$UserDtoCopyWithImpl(this._self, this._then);

  final _UserDto _self;
  final $Res Function(_UserDto) _then;

/// Create a copy of UserDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userName = null,Object? email = freezed,Object? fullname = freezed,Object? firstName = null,Object? lastName = null,Object? tel = freezed,Object? subgroupPermission = freezed,Object? tenantPermission = null,Object? roleId = null,Object? roles = null,}) {
  return _then(_UserDto(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,userName: null == userName ? _self.userName : userName // ignore: cast_nullable_to_non_nullable
as String,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,fullname: freezed == fullname ? _self.fullname : fullname // ignore: cast_nullable_to_non_nullable
as String?,firstName: null == firstName ? _self.firstName : firstName // ignore: cast_nullable_to_non_nullable
as String,lastName: null == lastName ? _self.lastName : lastName // ignore: cast_nullable_to_non_nullable
as String,tel: freezed == tel ? _self.tel : tel // ignore: cast_nullable_to_non_nullable
as String?,subgroupPermission: freezed == subgroupPermission ? _self.subgroupPermission : subgroupPermission // ignore: cast_nullable_to_non_nullable
as String?,tenantPermission: null == tenantPermission ? _self.tenantPermission : tenantPermission // ignore: cast_nullable_to_non_nullable
as String,roleId: null == roleId ? _self.roleId : roleId // ignore: cast_nullable_to_non_nullable
as int,roles: null == roles ? _self.roles : roles // ignore: cast_nullable_to_non_nullable
as UserRoleDto,
  ));
}

/// Create a copy of UserDto
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserRoleDtoCopyWith<$Res> get roles {
  
  return $UserRoleDtoCopyWith<$Res>(_self.roles, (value) {
    return _then(_self.copyWith(roles: value));
  });
}
}


/// @nodoc
mixin _$UserRoleDto {

 int get id; String get name; String? get description; bool? get forDeveloper; DateTime get createdDate; DateTime? get modifiedDate; DateTime? get deletedDate;
/// Create a copy of UserRoleDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserRoleDtoCopyWith<UserRoleDto> get copyWith => _$UserRoleDtoCopyWithImpl<UserRoleDto>(this as UserRoleDto, _$identity);

  /// Serializes this UserRoleDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserRoleDto&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.forDeveloper, forDeveloper) || other.forDeveloper == forDeveloper)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate)&&(identical(other.modifiedDate, modifiedDate) || other.modifiedDate == modifiedDate)&&(identical(other.deletedDate, deletedDate) || other.deletedDate == deletedDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,forDeveloper,createdDate,modifiedDate,deletedDate);

@override
String toString() {
  return 'UserRoleDto(id: $id, name: $name, description: $description, forDeveloper: $forDeveloper, createdDate: $createdDate, modifiedDate: $modifiedDate, deletedDate: $deletedDate)';
}


}

/// @nodoc
abstract mixin class $UserRoleDtoCopyWith<$Res>  {
  factory $UserRoleDtoCopyWith(UserRoleDto value, $Res Function(UserRoleDto) _then) = _$UserRoleDtoCopyWithImpl;
@useResult
$Res call({
 int id, String name, String? description, bool? forDeveloper, DateTime createdDate, DateTime? modifiedDate, DateTime? deletedDate
});




}
/// @nodoc
class _$UserRoleDtoCopyWithImpl<$Res>
    implements $UserRoleDtoCopyWith<$Res> {
  _$UserRoleDtoCopyWithImpl(this._self, this._then);

  final UserRoleDto _self;
  final $Res Function(UserRoleDto) _then;

/// Create a copy of UserRoleDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? forDeveloper = freezed,Object? createdDate = null,Object? modifiedDate = freezed,Object? deletedDate = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,forDeveloper: freezed == forDeveloper ? _self.forDeveloper : forDeveloper // ignore: cast_nullable_to_non_nullable
as bool?,createdDate: null == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime,modifiedDate: freezed == modifiedDate ? _self.modifiedDate : modifiedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedDate: freezed == deletedDate ? _self.deletedDate : deletedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _UserRoleDto implements UserRoleDto {
  const _UserRoleDto({required this.id, required this.name, this.description, this.forDeveloper, required this.createdDate, this.modifiedDate, this.deletedDate});
  factory _UserRoleDto.fromJson(Map<String, dynamic> json) => _$UserRoleDtoFromJson(json);

@override final  int id;
@override final  String name;
@override final  String? description;
@override final  bool? forDeveloper;
@override final  DateTime createdDate;
@override final  DateTime? modifiedDate;
@override final  DateTime? deletedDate;

/// Create a copy of UserRoleDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserRoleDtoCopyWith<_UserRoleDto> get copyWith => __$UserRoleDtoCopyWithImpl<_UserRoleDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserRoleDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserRoleDto&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.description, description) || other.description == description)&&(identical(other.forDeveloper, forDeveloper) || other.forDeveloper == forDeveloper)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate)&&(identical(other.modifiedDate, modifiedDate) || other.modifiedDate == modifiedDate)&&(identical(other.deletedDate, deletedDate) || other.deletedDate == deletedDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,description,forDeveloper,createdDate,modifiedDate,deletedDate);

@override
String toString() {
  return 'UserRoleDto(id: $id, name: $name, description: $description, forDeveloper: $forDeveloper, createdDate: $createdDate, modifiedDate: $modifiedDate, deletedDate: $deletedDate)';
}


}

/// @nodoc
abstract mixin class _$UserRoleDtoCopyWith<$Res> implements $UserRoleDtoCopyWith<$Res> {
  factory _$UserRoleDtoCopyWith(_UserRoleDto value, $Res Function(_UserRoleDto) _then) = __$UserRoleDtoCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String? description, bool? forDeveloper, DateTime createdDate, DateTime? modifiedDate, DateTime? deletedDate
});




}
/// @nodoc
class __$UserRoleDtoCopyWithImpl<$Res>
    implements _$UserRoleDtoCopyWith<$Res> {
  __$UserRoleDtoCopyWithImpl(this._self, this._then);

  final _UserRoleDto _self;
  final $Res Function(_UserRoleDto) _then;

/// Create a copy of UserRoleDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? description = freezed,Object? forDeveloper = freezed,Object? createdDate = null,Object? modifiedDate = freezed,Object? deletedDate = freezed,}) {
  return _then(_UserRoleDto(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,forDeveloper: freezed == forDeveloper ? _self.forDeveloper : forDeveloper // ignore: cast_nullable_to_non_nullable
as bool?,createdDate: null == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime,modifiedDate: freezed == modifiedDate ? _self.modifiedDate : modifiedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,deletedDate: freezed == deletedDate ? _self.deletedDate : deletedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

/// @nodoc
mixin _$UserRequestDto {

 String? get firstname; String? get lastname; String? get email; String? get tel; HomeAddress? get homeAddress;
/// Create a copy of UserRequestDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserRequestDtoCopyWith<UserRequestDto> get copyWith => _$UserRequestDtoCopyWithImpl<UserRequestDto>(this as UserRequestDto, _$identity);

  /// Serializes this UserRequestDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserRequestDto&&(identical(other.firstname, firstname) || other.firstname == firstname)&&(identical(other.lastname, lastname) || other.lastname == lastname)&&(identical(other.email, email) || other.email == email)&&(identical(other.tel, tel) || other.tel == tel)&&(identical(other.homeAddress, homeAddress) || other.homeAddress == homeAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,firstname,lastname,email,tel,homeAddress);

@override
String toString() {
  return 'UserRequestDto(firstname: $firstname, lastname: $lastname, email: $email, tel: $tel, homeAddress: $homeAddress)';
}


}

/// @nodoc
abstract mixin class $UserRequestDtoCopyWith<$Res>  {
  factory $UserRequestDtoCopyWith(UserRequestDto value, $Res Function(UserRequestDto) _then) = _$UserRequestDtoCopyWithImpl;
@useResult
$Res call({
 String? firstname, String? lastname, String? email, String? tel, HomeAddress? homeAddress
});


$HomeAddressCopyWith<$Res>? get homeAddress;

}
/// @nodoc
class _$UserRequestDtoCopyWithImpl<$Res>
    implements $UserRequestDtoCopyWith<$Res> {
  _$UserRequestDtoCopyWithImpl(this._self, this._then);

  final UserRequestDto _self;
  final $Res Function(UserRequestDto) _then;

/// Create a copy of UserRequestDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? firstname = freezed,Object? lastname = freezed,Object? email = freezed,Object? tel = freezed,Object? homeAddress = freezed,}) {
  return _then(_self.copyWith(
firstname: freezed == firstname ? _self.firstname : firstname // ignore: cast_nullable_to_non_nullable
as String?,lastname: freezed == lastname ? _self.lastname : lastname // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,tel: freezed == tel ? _self.tel : tel // ignore: cast_nullable_to_non_nullable
as String?,homeAddress: freezed == homeAddress ? _self.homeAddress : homeAddress // ignore: cast_nullable_to_non_nullable
as HomeAddress?,
  ));
}
/// Create a copy of UserRequestDto
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HomeAddressCopyWith<$Res>? get homeAddress {
    if (_self.homeAddress == null) {
    return null;
  }

  return $HomeAddressCopyWith<$Res>(_self.homeAddress!, (value) {
    return _then(_self.copyWith(homeAddress: value));
  });
}
}


/// @nodoc
@JsonSerializable(createFactory: false)

class _UserRequestDto implements UserRequestDto {
  const _UserRequestDto({this.firstname, this.lastname, this.email, this.tel, this.homeAddress});
  

@override final  String? firstname;
@override final  String? lastname;
@override final  String? email;
@override final  String? tel;
@override final  HomeAddress? homeAddress;

/// Create a copy of UserRequestDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserRequestDtoCopyWith<_UserRequestDto> get copyWith => __$UserRequestDtoCopyWithImpl<_UserRequestDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserRequestDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserRequestDto&&(identical(other.firstname, firstname) || other.firstname == firstname)&&(identical(other.lastname, lastname) || other.lastname == lastname)&&(identical(other.email, email) || other.email == email)&&(identical(other.tel, tel) || other.tel == tel)&&(identical(other.homeAddress, homeAddress) || other.homeAddress == homeAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,firstname,lastname,email,tel,homeAddress);

@override
String toString() {
  return 'UserRequestDto(firstname: $firstname, lastname: $lastname, email: $email, tel: $tel, homeAddress: $homeAddress)';
}


}

/// @nodoc
abstract mixin class _$UserRequestDtoCopyWith<$Res> implements $UserRequestDtoCopyWith<$Res> {
  factory _$UserRequestDtoCopyWith(_UserRequestDto value, $Res Function(_UserRequestDto) _then) = __$UserRequestDtoCopyWithImpl;
@override @useResult
$Res call({
 String? firstname, String? lastname, String? email, String? tel, HomeAddress? homeAddress
});


@override $HomeAddressCopyWith<$Res>? get homeAddress;

}
/// @nodoc
class __$UserRequestDtoCopyWithImpl<$Res>
    implements _$UserRequestDtoCopyWith<$Res> {
  __$UserRequestDtoCopyWithImpl(this._self, this._then);

  final _UserRequestDto _self;
  final $Res Function(_UserRequestDto) _then;

/// Create a copy of UserRequestDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? firstname = freezed,Object? lastname = freezed,Object? email = freezed,Object? tel = freezed,Object? homeAddress = freezed,}) {
  return _then(_UserRequestDto(
firstname: freezed == firstname ? _self.firstname : firstname // ignore: cast_nullable_to_non_nullable
as String?,lastname: freezed == lastname ? _self.lastname : lastname // ignore: cast_nullable_to_non_nullable
as String?,email: freezed == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String?,tel: freezed == tel ? _self.tel : tel // ignore: cast_nullable_to_non_nullable
as String?,homeAddress: freezed == homeAddress ? _self.homeAddress : homeAddress // ignore: cast_nullable_to_non_nullable
as HomeAddress?,
  ));
}

/// Create a copy of UserRequestDto
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HomeAddressCopyWith<$Res>? get homeAddress {
    if (_self.homeAddress == null) {
    return null;
  }

  return $HomeAddressCopyWith<$Res>(_self.homeAddress!, (value) {
    return _then(_self.copyWith(homeAddress: value));
  });
}
}

/// @nodoc
mixin _$HomeAddress {

 String? get address; String? get city; String? get state; String? get province; String? get zipcode;
/// Create a copy of HomeAddress
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HomeAddressCopyWith<HomeAddress> get copyWith => _$HomeAddressCopyWithImpl<HomeAddress>(this as HomeAddress, _$identity);

  /// Serializes this HomeAddress to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HomeAddress&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.province, province) || other.province == province)&&(identical(other.zipcode, zipcode) || other.zipcode == zipcode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,address,city,state,province,zipcode);

@override
String toString() {
  return 'HomeAddress(address: $address, city: $city, state: $state, province: $province, zipcode: $zipcode)';
}


}

/// @nodoc
abstract mixin class $HomeAddressCopyWith<$Res>  {
  factory $HomeAddressCopyWith(HomeAddress value, $Res Function(HomeAddress) _then) = _$HomeAddressCopyWithImpl;
@useResult
$Res call({
 String? address, String? city, String? state, String? province, String? zipcode
});




}
/// @nodoc
class _$HomeAddressCopyWithImpl<$Res>
    implements $HomeAddressCopyWith<$Res> {
  _$HomeAddressCopyWithImpl(this._self, this._then);

  final HomeAddress _self;
  final $Res Function(HomeAddress) _then;

/// Create a copy of HomeAddress
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? address = freezed,Object? city = freezed,Object? state = freezed,Object? province = freezed,Object? zipcode = freezed,}) {
  return _then(_self.copyWith(
address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,province: freezed == province ? _self.province : province // ignore: cast_nullable_to_non_nullable
as String?,zipcode: freezed == zipcode ? _self.zipcode : zipcode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable(createFactory: false)

class _HomeAddress implements HomeAddress {
  const _HomeAddress({this.address, this.city, this.state, this.province, this.zipcode});
  

@override final  String? address;
@override final  String? city;
@override final  String? state;
@override final  String? province;
@override final  String? zipcode;

/// Create a copy of HomeAddress
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HomeAddressCopyWith<_HomeAddress> get copyWith => __$HomeAddressCopyWithImpl<_HomeAddress>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HomeAddressToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HomeAddress&&(identical(other.address, address) || other.address == address)&&(identical(other.city, city) || other.city == city)&&(identical(other.state, state) || other.state == state)&&(identical(other.province, province) || other.province == province)&&(identical(other.zipcode, zipcode) || other.zipcode == zipcode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,address,city,state,province,zipcode);

@override
String toString() {
  return 'HomeAddress(address: $address, city: $city, state: $state, province: $province, zipcode: $zipcode)';
}


}

/// @nodoc
abstract mixin class _$HomeAddressCopyWith<$Res> implements $HomeAddressCopyWith<$Res> {
  factory _$HomeAddressCopyWith(_HomeAddress value, $Res Function(_HomeAddress) _then) = __$HomeAddressCopyWithImpl;
@override @useResult
$Res call({
 String? address, String? city, String? state, String? province, String? zipcode
});




}
/// @nodoc
class __$HomeAddressCopyWithImpl<$Res>
    implements _$HomeAddressCopyWith<$Res> {
  __$HomeAddressCopyWithImpl(this._self, this._then);

  final _HomeAddress _self;
  final $Res Function(_HomeAddress) _then;

/// Create a copy of HomeAddress
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? address = freezed,Object? city = freezed,Object? state = freezed,Object? province = freezed,Object? zipcode = freezed,}) {
  return _then(_HomeAddress(
address: freezed == address ? _self.address : address // ignore: cast_nullable_to_non_nullable
as String?,city: freezed == city ? _self.city : city // ignore: cast_nullable_to_non_nullable
as String?,state: freezed == state ? _self.state : state // ignore: cast_nullable_to_non_nullable
as String?,province: freezed == province ? _self.province : province // ignore: cast_nullable_to_non_nullable
as String?,zipcode: freezed == zipcode ? _self.zipcode : zipcode // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
