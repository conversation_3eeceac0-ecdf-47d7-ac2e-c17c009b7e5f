import 'package:ai_vanse/utilities/helpers.dart';
import 'package:flutter/foundation.dart';

class GaitCharacteristic {
  // TODO Improving the method
  // e.g differentiate the signal curve to get the peak and trough, get reliable judgement
  // make use of gyroscope data for the inclind angel during standing + walking
  // threshold + sliding windows method, dynamic update the min and max during a specific timestamp
  static const double standingThreshold = 0.505; // standing value for acceMag
  static const double walkingThreshold = 0.010;
  static const double rotatingThreshold = 0.2;
  static const double gyroThreshold = 0.1;
  String previousState = 'Unknown';
  String currentState = 'Unknown';
  DateTime? lastStateTime; // for gait state checking

  // Parameters for Bollinger Bands
  static const int movingAverageWindow = 20;
  static const double bollingerMultiplier = 2.0;

  // Step detection properties
  List<double> accelerometerData = [];
  int _stepCount = 0;
  double _strideTime = 0.0;
  double _strideVelocity = 0.0;
  double _swingTime = 0.0;
  bool _swing = false;
  List<double> durationDifference = [];

  // real time detection parameters
  DateTime? lastStepTime;
  List<DateTime> strideTimeData = [];

  // fixed time stampe parameters
  int lastTimeStampeIndex = 0;
  double lastStepTimeFixedTime = 0;
  List<double> strideTimeDataTimeStampe = [];

  // balance from gyroscope data
  List<double> gyroData = [];

  int stepCount() {
    return _stepCount;
  }

  bool swing() {
    return _swing;
  }

  double strideTime() {
    return _strideTime;
  }

  double strideVelocity() {
    return _strideVelocity;
  }

  double swingTime() {
    return _swingTime;
  }

  double timeSince(int currIndex) {
    return currIndex / 30 * 1000;
  }

  double timeDifference(int currentTime, int preTime) {
    return timeSince(currentTime) - timeSince(preTime);
  }

  // Function to check if the person is standing based on accelerometer data (x, y, z)
  bool _isStanding(double accelerationMagniture) {
    return (accelerationMagniture - standingThreshold).abs() < 0.008;
  }

  // Function to check if the person is walking based on accelerometer data
  bool _isWalking(double accelerationMagniture) {
    return (accelerationMagniture - standingThreshold).abs() > walkingThreshold;
  }

  // Function to check if the person is rotating based on gyroscope data
  bool _isTurning(double gyroMagnitude) {
    return gyroMagnitude >= rotatingThreshold;
  }

  String gaitPhase(double accelerationMagniture, double gyroMagnitude) {
    DateTime now = DateTime.now();

    lastStateTime ??= now; // Initialize lastStateTime if it's null

    if (_isWalking(accelerationMagniture)) {
      // Transition to Walking state
      previousState = currentState;
      currentState = 'Walking';
      lastStateTime = now; // Update last state time
      return currentState;
    }

    if (_isStanding(accelerationMagniture)) {
      if (currentState == 'Walking') {
        Duration duration = now.difference(lastStateTime!);
        if (duration.inSeconds < 2) {
          return currentState; // Remain in Walking state if less than 2 seconds since last Walking
        }
      }

      // Transition to Standing state
      previousState = currentState;
      currentState = 'Standing';
      lastStateTime = now; // Update last state time
      return currentState;
    }

    // Check duration for Walking state if no other state is active
    if (currentState == 'Walking') {
      Duration duration = now.difference(lastStateTime!);
      return duration.inSeconds < 2 ? currentState : 'Unknown';
    }

    return 'Unknown'; // Default case
  }

  // Function to detect strides (steps) in real time data streaming
  int detectStrideRealTime(double accelerationMagnitude) {
    DateTime now = DateTime.now();
    // valid walking state first
    if (currentState != 'Walking') return _stepCount;

    // Store the current acceleration
    accelerometerData.add(accelerationMagnitude);

    // Check if we have enough data points
    if (accelerometerData.length > movingAverageWindow) {
      // Remove the oldest data point
      accelerometerData.removeAt(0);
    }

    // Detect a step based on the sliding window limits
    Map<String, double> bollingerBands = calculateBollingerBands(
      accelerometerData,
      movingAverageWindow,
      bollingerMultiplier,
    );
    double upperBand = bollingerBands['upperBand']!;
    double lowerBand = bollingerBands['lowerBand']!;

    // Check if the acceleration magnitude exceeds the bands
    if (accelerationMagnitude > upperBand ||
        accelerationMagnitude < lowerBand) {
      // If this is the first swing
      if (!_swing) {
        _stepCount++;
        _swing = true; // First swing detected
        lastStepTime = now; // Update last step time
        strideTimeData.add(now);
      } else {
        debugPrint('enter else condition');
        // If swing is true, check duration since last step
        if (lastStepTime != null) {
          debugPrint('enter lastStepTime condition');
          Duration duration = now.difference(lastStepTime!);
          debugPrint(
            'duration in lastStepTime condition: ${duration.inMilliseconds}',
          );
          if (duration.inMilliseconds > 300) {
            // Only count if more than 0.3 second has passed
            debugPrint('enter duration condition');
            _stepCount++;
            _swing = false; // Reset swing for the next step
            lastStepTime = now; // Update last step time
            strideTimeData.add(now);
          }
        }
      }
    } else {
      // Reset swing if acceleration magnitude is within the bands
      _swing = false;
    }
    // debugPrint("mean: $mean, upper: $upperBand, lower: $lowerBand, magnitude: $accelerationMagnitude");

    return _stepCount;
  }

  // Function to detect strides (steps) in fixed time stampe
  void detectStride(double accelerationMagnitude, int index) {
    const int minStepDuration = 300;
    const int maxStepDuration = 1500;

    double now = timeSince(index);
    accelerometerData.add(accelerationMagnitude);
    if (accelerometerData.length > movingAverageWindow) {
      accelerometerData.removeAt(0);
    }

    Map<String, double> bollingerBands = calculateBollingerBands(
      accelerometerData,
      movingAverageWindow,
      bollingerMultiplier,
    );
    if (accelerationMagnitude > bollingerBands['upperBand']! ||
        accelerationMagnitude < bollingerBands['lowerBand']!) {
      double duration = now - lastStepTimeFixedTime;
      if (!_swing ||
          (duration > minStepDuration && duration < maxStepDuration)) {
        _stepCount++;
        _swing = !_swing;
        lastStepTimeFixedTime = now;
        strideTimeDataTimeStampe.add(now);
      }
    } else {
      _swing = false;
    }
  }

  // Function to calculate stride time from IMU data real time
  double averageStrideTimeRealTime() {
    if (strideTimeData.length < 2) {
      return 0.0;
    }

    if (strideTimeData.length > 3) {
      strideTimeData.removeAt(0);
    }
    Duration duration = strideTimeData[1].difference(strideTimeData[0]);
    // print("before if condition, duration is: ${duration.inMilliseconds}");
    if (duration.inMilliseconds > 500) {
      // neglect duration less than 0.5sec, likely is from stride detection noise
      // print("duration: ${duration.inMilliseconds / 1000}");
      durationDifference.add(duration.inMilliseconds / 1000);
    }

    double mean = movingAverage(durationDifference, 10);

    return double.parse(mean.toStringAsFixed(3));
  }

  // Function to calculate stride time from IMU data
  double averageStrideTime() {
    if (strideTimeDataTimeStampe.length < 2) {
      return double.infinity;
    }

    if (strideTimeDataTimeStampe.length > 3) {
      strideTimeDataTimeStampe.removeAt(0);
    }
    // Duration duration = strideTimeDataTimeStampe[1].difference(strideTimeData[0]);
    double duration = strideTimeDataTimeStampe[1] - strideTimeDataTimeStampe[0];
    // print("before if condition, duration is: $duration");
    if (duration > 500) {
      // neglect duration less than 0.5sec, likely is from stride detection noise
      // print("duration: ${duration / 1000}");
      durationDifference.add(duration / 1000);
    }

    return movingAverage(durationDifference, 10);
  }

  double balancingFactor(double gyroMagnitude) {
    // valid standiing state first
    if (currentState != 'Standing') {
      return 0.0;
    }

    // Store the current acceleration
    gyroData.add(gyroMagnitude);

    // Check if we have enough data points
    if (gyroData.length > movingAverageWindow) {
      // Remove the oldest data point
      gyroData.removeAt(0);
    }

    Map<String, double> gyroBollingerBands = calculateBollingerBands(
      gyroData,
      movingAverageWindow,
      bollingerMultiplier,
    );
    double balance = gyroBollingerBands['variance']!;
    return double.parse(balance.toStringAsFixed(5));
  }

  void processStrideData(List<double> data) {
    for (int i = 5; i < data.length; i++) {
      bool walking = _isWalking(data[i]);
      // bool standing = _isStanding(data[i]);
      if (walking) {
        detectStride(data[i], i);
      }

      _strideTime = averageStrideTime();
      _strideVelocity = 1 / _strideTime;
      _swingTime = _strideTime * 0.4;
    }
  }
}
