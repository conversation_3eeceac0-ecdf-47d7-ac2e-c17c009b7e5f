import 'package:json_annotation/json_annotation.dart';

// This is required to connect this file with the code generator
part 'overpass.g.dart';

// Model for the overall API response
@JsonSerializable()
class OverpassResponse {
  final List<Poi> elements;

  OverpassResponse({required this.elements});

  factory OverpassResponse.fromJson(Map<String, dynamic> json) =>
      _$OverpassResponseFromJson(json);
}

// Model for a single Point of Interest (POI)
@JsonSerializable()
class Poi {
  final int id;
  final double? lat;
  final double? lon;
  final Map<String, dynamic>? tags;

  Poi({required this.id, this.lat, this.lon, this.tags});

  // A helper getter to easily access the name from the tags map
  String? get name => tags?['name'];

  factory Poi.fromJson(Map<String, dynamic> json) => _$PoiFromJson(json);

  @override
  String toString() {
    return 'Poi(id: $id, name: $name, lat: $lat, lon: $lon, tags: $tags)';
  }
}
