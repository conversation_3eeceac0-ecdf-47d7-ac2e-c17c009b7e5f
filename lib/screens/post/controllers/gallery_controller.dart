import 'dart:io';

import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/services/permission_service.dart'; // Added import
import 'package:get/get.dart';
import 'package:photo_manager/photo_manager.dart';

class GalleryController extends GetxController {
  final RxList<String> selectedImages = <String>[].obs;
  final MediaLocationPermissionService _mediaLocationPermissionService =
      Get.find();

  Rxn<AssetEntity> asset = Rxn<AssetEntity>();

  void selectImage(String imagePath) {
    selectedImages.add(imagePath);
  }

  void removeImage(String imagePath) {
    selectedImages.remove(imagePath);
  }

  Future<bool> requestLocationPermission() async {
    return await _mediaLocationPermissionService.requestPermission();
  }

  Future<void> requestPermission() async {
    final PermissionState ps =
        await PhotoManager.requestPermissionExtend(); // the method can use optional param `permission`.
    if (ps.isAuth) {
      // Granted
      // You can to get assets here.
    } else if (ps.hasAccess) {
      // Access will continue, but the amount visible depends on the user's selection.
    } else {
      // Limited(iOS) or Rejected, use `==` for more precise judgements.
      // You can call `PhotoManager.openSetting()` to open settings for further steps.
    }
    if (Platform.isAndroid) {
      await requestLocationPermission();
    }
  }

  Future<void> captureImage() async {
    // Consider requesting location permission before capturing, if needed
    // Example:
    // final bool locationGranted = await requestLocationPermission();
    // if (!locationGranted) {
    //   // Handle denied permission (e.g., show a message, return)
    //   print("Location permission denied");
    //   return;
    // }

    // const int imageQuality = 100;
    // const double maxDimension = 2048.0;
    //
    // final XFile? photo = await _picker.pickImage(
    //   source: ImageSource.camera,
    //   imageQuality: imageQuality,
    //   preferredCameraDevice: CameraDevice.rear,
    //   maxHeight: maxDimension,
    //   maxWidth: maxDimension,
    // );
    //
    // if (photo != null) {
    //   // TODO: Process the captured photo (e.g., add to selectedImages or update asset)
    //   // You might want to associate location data here if permission was granted
    //   selectedImages.add(photo.path);
    // }

    Get.toNamed(AppRoutes.capture);
  }
}
