import 'dart:async';

import 'package:ai_vanse/extensions/datetime_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/repositories/activity_repository.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class DatePageView extends StatefulWidget {
  // final DateTime? startDate;
  // final int? dateCount;
  final double? height;
  final ValueChanged<DateTime>? onDateSelected;

  const DatePageView({
    super.key,
    // this.startDate,
    // this.dateCount,
    this.onDateSelected,
    this.height,
  });

  @override
  State<DatePageView> createState() => _DatePageViewState();
}

class _DatePageViewState extends State<DatePageView> {
  late final ActivityRepository _activityRepository = Get.find();
  late final PageController _pageController = PageController(
    viewportFraction: 62.sp / 1.sw,
    initialPage: indexOf,
  );

  Duration get duration => const Duration(milliseconds: 300);

  int itemIndex = 0;

  int get indexOf {
    return _activityRepository.selectedDateRange.indexOf(
      _activityRepository.selectedDate.value,
    );
  }

  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    itemIndex = indexOf;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _pageController.animateToPage(
        duration: duration,
        indexOf,
        curve: Curves.easeInOut,
      );
    });
    everAll(
      [_activityRepository.selectedDate, _activityRepository.selectedDateRange],
      (_) {
        if (itemIndex != indexOf) {
          itemIndex = indexOf;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_pageController.hasClients) {
              _pageController.jumpToPage(indexOf);
            }
          });
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedDateRange = _activityRepository.selectedDateRange;
      return SizedBox(
        height: widget.height ?? 66.sp,
        child: PageView.builder(
          reverse: true,
          controller: _pageController,
          itemCount: selectedDateRange.length,
          onPageChanged: (index) {
            setState(() {
              setState(() {
                itemIndex = index;
              });

              if (_debounce?.isActive ?? false) _debounce?.cancel();
              _debounce = Timer(duration, () {
                _activityRepository.selectedDate.value =
                    selectedDateRange[index];
                widget.onDateSelected?.call(selectedDateRange[index]);
              });
            });
          },
          itemBuilder:
              (context, index) =>
                  _buildDayItem(index, selectedDateRange[index]),
        ),
      );
    });
  }

  Widget _buildDayItem(int index, DateTime date) {
    final bool isSelected = index == itemIndex;
    return GestureDetector(
      onTap: () {
        _pageController.animateToPage(
          index,
          duration: duration,
          curve: Curves.easeInOut,
        );
      },
      child: Container(
        width: 70.sp,
        margin: const EdgeInsets.symmetric(horizontal: 4).w,
        decoration: BoxDecoration(
          color:
              isSelected
                  ? context.theme.extraColors.selectedWhite
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12).r,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AppText(
              date.formatToWeekDay(),
              appTextStyle: AppTextStyle.bodySmall,
              appTextColor:
                  isSelected ? AppTextColor.base : AppTextColor.unselected,
            ),
            AppText(
              date.day.toString(),
              appTextStyle: AppTextStyle.bodyLargeW500,
              appTextColor:
                  isSelected ? AppTextColor.base : AppTextColor.unselected,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }
}
