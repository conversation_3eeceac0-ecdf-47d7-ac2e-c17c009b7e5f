// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_year.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ActivityYear _$ActivityYearFromJson(Map<String, dynamic> json) =>
    _ActivityYear(
      month: _$JsonConverterFromJson<String, DateTime>(
        json['month'],
        const MonthOnlyConverter().fromJson,
      ),
      energy:
          json['energy'] == null
              ? null
              : Energy.fromJson(json['energy'] as Map<String, dynamic>),
      duration:
          json['duration'] == null
              ? null
              : ActivityDuration.fromJson(
                json['duration'] as Map<String, dynamic>,
              ),
      steps: (json['steps'] as num?)?.toInt(),
      distance: (json['distance'] as num?)?.toInt(),
      noOfDate: (json['noOfDate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ActivityYearToJson(_ActivityYear instance) =>
    <String, dynamic>{
      'month': _$JsonConverterToJson<String, DateTime>(
        instance.month,
        const MonthOnlyConverter().toJson,
      ),
      'energy': instance.energy,
      'duration': instance.duration,
      'steps': instance.steps,
      'distance': instance.distance,
      'noOfDate': instance.noOfDate,
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) => json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);

_ActivityDuration _$ActivityDurationFromJson(Map<String, dynamic> json) =>
    _ActivityDuration(
      lying: (json['lying'] as num?)?.toInt(),
      lyingOnBack: (json['lyingOnBack'] as num?)?.toInt(),
      lyingOnStomach: (json['lyingOnStomach'] as num?)?.toInt(),
      lyingOnLeft: (json['lyingOnLeft'] as num?)?.toInt(),
      lyingOnRight: (json['lyingOnRight'] as num?)?.toInt(),
      sitting: (json['sitting'] as num?)?.toInt(),
      standing: (json['standing'] as num?)?.toInt(),
      walking: (json['walking'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ActivityDurationToJson(_ActivityDuration instance) =>
    <String, dynamic>{
      'lying': instance.lying,
      'lyingOnBack': instance.lyingOnBack,
      'lyingOnStomach': instance.lyingOnStomach,
      'lyingOnLeft': instance.lyingOnLeft,
      'lyingOnRight': instance.lyingOnRight,
      'sitting': instance.sitting,
      'standing': instance.standing,
      'walking': instance.walking,
    };

_Energy _$EnergyFromJson(Map<String, dynamic> json) => _Energy(
  lying: (json['lying'] as num?)?.toInt(),
  sitting: (json['sitting'] as num?)?.toInt(),
  standing: (json['standing'] as num?)?.toInt(),
  walking: (json['walking'] as num?)?.toInt(),
  total: (json['total'] as num?)?.toInt(),
);

Map<String, dynamic> _$EnergyToJson(_Energy instance) => <String, dynamic>{
  'lying': instance.lying,
  'sitting': instance.sitting,
  'standing': instance.standing,
  'walking': instance.walking,
  'total': instance.total,
};
