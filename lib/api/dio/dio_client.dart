import 'dart:io';

import 'package:ai_vanse/api/dio/options/json_option.dart';
import 'package:ai_vanse/api/interceptors/auth_interceptor.dart';
import 'package:ai_vanse/api/interceptors/base_url_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class DioClient {
  static bool get enabledDioLogger => true;

  static Dio getInstance({
    bool authenticated = true,
    bool useProxy = false,
    bool notification = false,
  }) {
    final dio = Dio(JsonOption());

    if (useProxy) {
      dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient:
            () =>
                HttpClient()
                  ..findProxy = ((_) => 'PROXY 127.0.0.1:9090')
                  ..badCertificateCallback = (_, __, ___) => true,
      );
    }

    return dio
      ..interceptors.addAll([
        BaseUrlInterceptor(),
        if (authenticated) ...[AuthInterceptor()] else ...[LoginInterceptor()],
        if (kDebugMode && enabledDioLogger)
          PrettyDioLogger(
            requestHeader: true,
            requestBody: true,
            responseHeader: true,
            maxWidth: 150,
          ),
      ]);
  }
}
