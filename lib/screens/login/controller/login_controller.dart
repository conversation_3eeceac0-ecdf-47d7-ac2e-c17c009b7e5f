import 'package:ai_vanse/api/handler/login_error_handler.dart';
import 'package:ai_vanse/repositories/user_repository.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/services/dialog_service.dart';
import 'package:ai_vanse/utils/common.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class LoginController extends GetxController {
  late final UserRepository _userRepository = Get.find();
  late final DialogService _dialogService = Get.find();

  final RxString username = RxString('');
  final RxString password = RxString('');

  final RxBool isRememberMe = RxBool(false);
  final RxBool canContinue = RxBool(false);

  @override
  void onInit() {
    super.onInit();
    everAll([username, password], (_) => _checkCanContinue());
  }

  void setUsername(String value) {
    username.value = value;
  }

  void setPassword(String value) {
    password.value = value;
  }

  void _checkCanContinue() {
    canContinue.value = username.value.isNotBlank && password.value.isNotBlank;
  }

  Future<void> login() async {
    Common.showLoading();
    try {
      await _userRepository.login(username.value, password.value);
      Common.hideLoading();

      Get.offAndToNamed(AppRoutes.home);
    } on BadCredentialException catch (e, stacktrace) {
      Common.hideLoading();

      debugPrintStack(stackTrace: stacktrace, label: e.toString());
      _dialogService.showMessageDialog(
        title: 'login_error_title_bad_credential'.tr,
        message: 'login_error_description_bad_credential'.tr,
        okLabel: 'common_cta_ok'.tr,
      );
    } catch (e, stacktrace) {
      Common.hideLoading();

      debugPrintStack(stackTrace: stacktrace, label: e.toString());
      _dialogService.showMessageDialog(
        title: 'common_error_title_no_connection'.tr,
        okLabel: 'common_cta_ok'.tr,
        message: 'common_error_description_no_connection'.tr,
      );
    } finally {
      Common.hideLoading();
    }
  }

  Future<void> selectServer() async {}
}
