import 'package:envied/envied.dart';

part 'env.g.dart';

@Envied(path: '.env')
final class Env {
  @EnviedField(varName: 'IMGBB_API_KEY', obfuscate: true)
  String imgbbApiKey = _Env.imgbbApiKey;

  @EnviedField(varName: 'IMGBB_UPLOAD_ENDPOINT', obfuscate: true)
  String imgbbUploadEndpoint = _Env.imgbbUploadEndpoint;

  @EnviedField(varName: 'AI_SERVICE_API_ENDPOINT', obfuscate: true)
  String aiServiceApiEndpoint = _Env.aiServiceApiEndpoint;

  @EnviedField(varName: 'TRANSLATE_SERVICE_API_ENDPOINT', obfuscate: true)
  String translateServiceApiEndpoint = _Env.translateServiceApiEndpoint;

  @EnviedField(varName: 'AUTH_SERVICE_API_ENDPOINT', obfuscate: true)
  String authServiceApiEndpoint = _Env.authServiceApiEndpoint;
}

// Run the following command to generate the .env.g.dart file:
// dart run build_runner build -d
