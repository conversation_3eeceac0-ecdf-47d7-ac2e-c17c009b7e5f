import 'dart:math';

import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:flutter/widgets.dart';

class AutoAxisCountGrid extends StatelessWidget {
  const AutoAxisCountGrid({
    super.key,
    required this.itemCrossAxisMinSize,
    required this.itemBuilder,
    this.minCrossAxisCount,
    this.maxCrossAxisCount,
    required this.itemCount,
    this.itemMainAxisSize,
    this.padding,
    this.scrollDirection = Axis.vertical,
    this.mainAxisSpacing = 0.0,
    this.crossAxisSpacing = 0.0,
    this.childAspectRatio,
    this.shrinkWrap = false,
    this.physics,
    this.controller,
  }) : assert(minCrossAxisCount == null || minCrossAxisCount > 0),
       assert(maxCrossAxisCount == null || maxCrossAxisCount > 0),
       assert(itemCrossAxisMinSize > 0);

  final IndexedWidgetBuilder itemBuilder;
  final int? minCrossAxisCount;
  final int? maxCrossAxisCount;
  final double itemCrossAxisMinSize;
  final double? itemMainAxisSize;
  final int itemCount;
  final EdgeInsets? padding;
  final Axis scrollDirection;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double? childAspectRatio;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final ScrollController? controller;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: buildGridView);
  }

  Widget buildGridView(BuildContext context, BoxConstraints constraints) {
    final EdgeInsets padding = this.padding ?? context.safeAreaWithoutTop;
    final int crossAxisCount = _getCrossAxisCount(constraints.deflate(padding));
    final aspectRatio = _getAspectRatio(context, constraints, crossAxisCount);

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: aspectRatio,
      ),
      scrollDirection: scrollDirection,
      padding: padding,
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      shrinkWrap: shrinkWrap,
      physics: physics,
      controller: controller,
    );
  }

  int _getCrossAxisCount(BoxConstraints boxConstraints) {
    final double crossAxisSize = _getCrossAxisSize(boxConstraints);
    final int crossAxisCount = (crossAxisSize / itemCrossAxisMinSize).floor();
    if (minCrossAxisCount == null && minCrossAxisCount == null) {
      return crossAxisCount;
    }

    if (minCrossAxisCount != null) {
      return max(minCrossAxisCount!, crossAxisCount);
    }

    if (maxCrossAxisCount != null) {
      return min(maxCrossAxisCount!, crossAxisCount);
    }

    return crossAxisCount.clamp(minCrossAxisCount!, maxCrossAxisCount!);
  }

  double _getAspectRatio(
    BuildContext context,
    BoxConstraints boxConstraints,
    int crossAxisCount,
  ) {
    if (itemMainAxisSize == null) {
      return childAspectRatio ?? 1.0;
    }
    final double itemCrossAxisSize =
        _getCrossAxisSize(boxConstraints) / crossAxisCount;
    return itemCrossAxisSize / itemMainAxisSize!;
  }

  double _getCrossAxisSize(BoxConstraints boxConstraints) =>
      scrollDirection == Axis.vertical
          ? boxConstraints.maxWidth
          : boxConstraints.maxHeight;
}
