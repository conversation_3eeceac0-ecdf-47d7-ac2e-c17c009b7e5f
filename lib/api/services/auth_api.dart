import 'package:ai_vanse/api/dio/dio_client.dart';
import 'package:ai_vanse/model/token/token.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_api.g.dart';

@RestApi(baseUrl: '/api/auth')
abstract class AuthApi {
  factory AuthApi() => _AuthApi(DioClient.getInstance(authenticated: false));

  @POST('/myactsense/login')
  Future<HttpResponse<Token>> login(@Body() Map<String, dynamic> body);
}
