import 'package:ai_vanse/extensions/colors_extra_extension.dart';
import 'package:ai_vanse/extensions/string_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class AppTextInput extends StatelessWidget {
  final String? label;
  final String? hintText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final AutovalidateMode? autovalidateMode;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final TextAlign textAlign;
  final double? fontSize;
  final double? fontHeight;
  final bool? enabled;
  final bool readOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final void Function(String)? onChanged;
  final VoidCallback? onTap;
  final bool required;
  final bool email;
  final bool password;
  final bool phoneNumber;
  final bool multiLines;
  final int? minLines;
  final int? maxLines;
  final String? Function(String?)? validator;
  final String? Function(String?)? validatorCustom;

  const AppTextInput({
    super.key,
    this.label,
    this.controller,
    this.focusNode,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.hintText,
    this.keyboardType,
    this.textInputAction,
    this.contentPadding,
    this.textAlign = TextAlign.start,
    this.fontSize,
    this.fontHeight,
    this.enabled,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onChanged,
    this.onTap,
    this.required = false,
    this.email = false,
    this.password = false,
    this.phoneNumber = false,
    this.multiLines = false,
    this.minLines = 1,
    this.maxLines = 1,
    this.validator,
    this.validatorCustom,
  });

  @override
  Widget build(BuildContext context) {
    final textField = TextFormField(
      controller: controller,
      focusNode: focusNode,
      autovalidateMode: autovalidateMode,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      enabled: enabled,
      readOnly: readOnly,
      obscureText: password,
      onChanged: onChanged,
      onTap: onTap,
      minLines: minLines ?? (multiLines ? 3 : null),
      maxLines: maxLines ?? (multiLines ? 5 : null),
      textAlign: textAlign,
      decoration: InputDecoration(
        isDense: true,
        hintText: hintText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: Colors.white,
        contentPadding:
            contentPadding ??
            EdgeInsets.symmetric(vertical: 8.sp, horizontal: 16.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(32.r),
          borderSide: BorderSide(
            color: context.theme.extraColors.hint,
            width: 1.sp,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(32.r),
          borderSide: BorderSide(
            color: context.theme.extraColors.hint,
            width: 1.sp,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(32.r),
          borderSide: BorderSide(
            color: context.theme.colorScheme.onPrimary,
            width: 1.sp,
          ),
        ),
        hintStyle: AppTextStyle.bodyText.textStyle.copyWith(
          color: context.theme.extraColors.hint,
        ),
        errorStyle: AppTextStyle.bodyText.textStyle,
      ),
      style: AppTextStyle.bodyText.textStyle,
      validator:
          validatorCustom ??
          (value) {
            if (required && value.isNullOrEmpty) {
              return 'validate_required'.tr;
            }

            if (email &&
                value.isNotNullOrEmpty &&
                !GetUtils.isEmail(value ?? '')) {
              return 'validate_invalid_email'.tr;
            }

            if (phoneNumber &&
                value.isNotNullOrEmpty &&
                !GetUtils.isPhoneNumber(value ?? '')) {
              return 'validate_invalid_phone_number'.tr;
            }

            return validator?.call(value);
          },
    );

    if (label.isNullOrEmpty) {
      return textField;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            AppText(label!, appTextStyle: AppTextStyle.bodyBold),
            if (required)
              AppText(
                '*',
                appTextStyle: AppTextStyle.bodyBold,
                color: Theme.of(context).extraColors.red.c500,
              ),
          ],
        ),
        SizedBox(height: 8.sp),
        textField,
      ],
    );
  }
}
