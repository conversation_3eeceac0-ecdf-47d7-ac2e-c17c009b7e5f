import 'package:ai_vanse/extensions/datetime_extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

extension StringNullableExtension on String? {
  bool get isNullOrEmpty => this?.isEmpty ?? true;

  bool get isNotNullOrEmpty => this?.isNotEmpty == true;

  String ifNullOrBlank(String defaultValue) {
    if (this == null || isBlank == true) return defaultValue;
    return this!;
  }
}

extension StringExtension on String {
  String toPattern(String pattern, {String separator = '-'}) {
    final valWithoutSeparator = replaceAll(separator, '');
    final patternWithoutSeparator = pattern.replaceAll(separator, '');
    if (valWithoutSeparator.length == patternWithoutSeparator.length) {
      int startAt = 0;
      final val = split('');
      while (pattern.contains(separator, startAt)) {
        startAt = pattern.indexOf(separator, startAt);
        val.insert(startAt, separator);
        startAt++;
      }

      return val.join();
    }

    return this;
  }

  String toMobileFormat() {
    if (length == 10) {
      final str = split('');
      str.insert(3, '-');
      str.insert(7, '-');

      return str.join();
    } else if (length == 9) {
      final str = split('');
      str.insert(2, '-');
      str.insert(6, '-');

      return str.join();
    }

    return this;
  }

  TimeOfDay? toTime() {
    if (isEmpty) {
      return null;
    }

    try {
      if (startsWith('TimeOfDay')) {
        return replaceAll(
          'TimeOfDay(',
          '',
        ).replaceAll(':', '').replaceAll(')', '').toTime();
      }

      if (length == 4) {
        final h = substring(0, 2).toInt();
        final m = substring(2).toInt();

        return TimeOfDay(hour: h, minute: m);
      } else if (RegExp(r'^\d{2}:\d{2}(:\d{2})?$').hasMatch(this)) {
        final times = split(':');
        final h = times[0].toInt();
        final m = times[1].toInt();

        return TimeOfDay(hour: h, minute: m);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  DateTime? toDateTimeWithBuddhistFormat() {
    if (isEmpty) {
      return null;
    }

    try {
      final dates = split('/');
      final d = dates[0];
      final m = dates[1];
      var y = dates[2].toInt();
      if (y > 2400) {
        y -= 543;
      }

      final date = '$y-$m-$d';
      if (date.length != 10) {
        return null;
      }

      return date.toDateTime();
    } catch (_) {
      return null;
    }
  }

  DateTime? toDateTime({TimeOfDay? time}) {
    if (isEmpty) {
      return null;
    }

    try {
      if (this is DateTime) {
        return this as DateTime;
      }

      final date = DateTime.parse(this);
      if (time != null) {
        return date.addTime(time);
      }

      return date;
    } catch (e) {
      return null;
    }
  }

  int toInt() {
    try {
      if (isEmpty || !GetUtils.isNum(this)) {
        return 0;
      }

      return int.parse(replaceAll(',', ''));
    } catch (e) {
      return 0;
    }
  }

  bool toBoolean() {
    try {
      if (isEmpty || !GetUtils.isBool(this)) {
        return false;
      }

      return bool.parse(this);
    } catch (e) {
      return false;
    }
  }

  double? toDouble({double? defaultValue = 0.0}) {
    try {
      if (isEmpty) {
        return defaultValue;
      }

      if (codeUnits.length == 1 && codeUnits.first == 13) {
        return defaultValue;
      }

      return double.tryParse(
        replaceAll(',', '').replaceAll('\n', '').trim().removeAllWhitespace,
      );
    } catch (e) {
      return defaultValue;
    }
  }

  List<String> toArrayString([String splitBy = ',']) {
    if (isEmpty) {
      return [];
    }

    return split(splitBy);
  }

  List<String> splitFirst(String separator) {
    final int separatorIndex = indexOf(separator);
    if (separatorIndex == -1) return [this];
    return [
      substring(0, separatorIndex).trim(),
      substring(separatorIndex + 1).trim(),
    ];
  }

  String ifBlank(String defaultValue) {
    if (isBlank == true) return defaultValue;
    return this;
  }
}
