import 'dart:math';

import 'package:ai_vanse/api/converter.dart';
import 'package:ai_vanse/api/services/activity_api.dart';
import 'package:ai_vanse/api/services/available_date_api.dart';
import 'package:ai_vanse/extensions/response_extension.dart';
import 'package:ai_vanse/model/activity/activity.dart';
import 'package:ai_vanse/model/activity/activity_unit.dart';
import 'package:ai_vanse/model/activity/activity_year.dart';
import 'package:ai_vanse/model/activity/available_date.dart';
import 'package:ai_vanse/services/token_service.dart';
import 'package:dartx/dartx.dart';
import 'package:get/get.dart';

class ActivityRepository extends GetxService {
  static const dateRangeCount = 14;

  late final TokenService _tokenService = Get.find();
  late final ActivityApi _activityApi = Get.find();
  late final AvailableDateApi _availableDateApi = Get.find();

  late final DateOnlyConverter _dateOnlyConverter = const DateOnlyConverter();

  final Rxn<AvailableDate> availableDate = Rxn();
  final Rx<DateTime> selectedDate = Rx(DateTime.now());
  final RxList<DateTime> selectedDateRange = RxList([]);

  final Rx<ActivityUnit> unit = Rx(ActivityUnit.step);

  @override
  void onInit() {
    super.onInit();
    selectedDateRange.value = _calculateDateRange();
  }

  Future<int> get patientId async =>
      (await _tokenService.getToken())!.patientId;

  Future<Activity> getActivityAsDay() async {
    return _activityApi
        .getActivityAsDay(
          patientId: await patientId,
          date: _dateOnlyConverter.toJson(selectedDate.value),
        )
        .toResponse();
  }

  Future<List<Activity>> getActivityAsWeek(DateTime date) async {
    return _activityApi
        .getActivityAsWeek(
          patientId: await patientId,
          date: _dateOnlyConverter.toJson(date),
        )
        .toResponse();
  }

  Future<List<Activity>> getActivityAsMonth(DateTime date) async {
    return _activityApi
        .getActivityAsMonth(
          patientId: await patientId,
          date: _dateOnlyConverter.toJson(date),
        )
        .toResponse();
  }

  Future<List<ActivityYear>> getActivityAsYear(DateTime date) async {
    return _activityApi
        .getActivityAsYear(
          patientId: await patientId,
          date: _dateOnlyConverter.toJson(date),
        )
        .toResponse();
  }

  Future<AvailableDate?> getAvailableDateByPatient() async {
    availableDate.value =
        await _availableDateApi
            .getAvailableDateByPatientId(patientId: await patientId)
            .toResponse();

    return availableDate.value;
  }

  void setSelectedDateAndCalculateDateRange(DateTime dateTime) {
    selectedDate.value = dateTime;
    final calculateDateRange = _calculateDateRange();
    selectedDateRange.value = calculateDateRange;
  }

  List<DateTime> _calculateDateRange() {
    final int dateFromNow =
        DateTime.now().date.difference(selectedDate.value.date).inDays;
    final int forwardCount = min(dateFromNow + 1, (dateRangeCount / 2).floor());
    final int backwardCount = dateRangeCount - forwardCount;
    return List.generate(
      dateRangeCount,
      (index) =>
          selectedDate.value.subtract(Duration(days: backwardCount - index)),
    ).reversed.toList();
  }
}
