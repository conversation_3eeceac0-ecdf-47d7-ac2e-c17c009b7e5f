import 'dart:async';
import 'dart:convert';

import 'package:ai_vanse/model/token/token.dart';
import 'package:ai_vanse/services/secure_storage_service.dart';
import 'package:get/get.dart';

class TokenService extends GetxService {
  static const _tokenKey = 'TOKEN';

  final SecureStorageService _secureStorageService = Get.find();

  FutureOr<Token?> getToken() {
    return _getToken();
  }

  Future<void> setToken(Token token) {
    final String json = jsonEncode(token.toJson());

    return _secureStorageService.set(TokenService._tokenKey, json);
  }

  Future<Token?> _getToken() {
    return _secureStorageService
        .get(TokenService._tokenKey)
        .then((results) {
          if (results == null) {
            return null;
          }

          final Token token = Token.fromJson(jsonDecode(results));

          return token;
        })
        .onError((error, stackTrace) {
          return null;
        });
  }

  Future<void> deleteToken() {
    return _secureStorageService.delete(TokenService._tokenKey);
  }
}
