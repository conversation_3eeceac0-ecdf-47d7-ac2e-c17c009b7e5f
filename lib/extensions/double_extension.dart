import 'package:ai_vanse/extensions/string_extension.dart';
import 'package:intl/intl.dart';

extension DoubleExtension on double {
  String toCurrencyFormat({bool showDecimal = true}) {
    final formater = NumberFormat(showDecimal ? '#,##0.00' : '#,##0');
    return formater.format(this);
  }

  double roundFixed([int fractionDigits = 2]) {
    return toStringAsFixed(2).toDouble(defaultValue: 0.0) ?? 0.0;
  }
}

extension NullDoubleExtension on double? {
  double roundFixed([int fractionDigits = 2]) {
    if (this == null || this == 0.0) return 0.0;

    return this!.roundFixed(fractionDigits);
  }
}
