import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum AppButtonOutlineColor { primary }

class AppButtonOutline extends StatelessWidget {
  final void Function()? onPressed;
  final String label;
  final Widget? icon;
  final AppButtonOutlineColor? appButtonOutlineColor;
  final double? borderRadius;
  final TextAlign? textAlign;
  final EdgeInsetsGeometry? padding;
  final AppTextStyle appTextStyle;

  const AppButtonOutline({
    super.key,
    required this.label,
    required this.onPressed,
    this.icon,
    this.appButtonOutlineColor = AppButtonOutlineColor.primary,
    this.appTextStyle = AppTextStyle.bodyText,
    this.borderRadius,
    this.textAlign,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final color = theme.colorScheme.primary;
    final fontColor = theme.colorScheme.primary;
    // switch (appButtonOutlineColor) {
    //   case AppButtonOutlineColor.secondary:
    //     color = theme.colorScheme.secondary;
    //     fontColor = theme.colorScheme.secondary;
    //     break;
    // }

    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 32.sp),
        ),
        side: BorderSide(width: 1.sp, color: color),
        padding: padding ?? EdgeInsets.symmetric(horizontal: 4.sp),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null)
            Padding(padding: EdgeInsets.only(right: 8.sp), child: icon!),
          Expanded(
            child: AppText(
              label,
              appTextStyle: appTextStyle,
              color: fontColor,
              textAlign: textAlign ?? TextAlign.center,
              autoSize: true,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}
