import 'dart:async';

import 'package:ai_vanse/api/interceptors/http_interceptor.dart';
import 'package:ai_vanse/constants/config.dart';
import 'package:dio/dio.dart';

abstract class UrlInterceptor extends HttpInterceptor {
  String get url;

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    options.baseUrl = url + options.baseUrl;

    super.onRequest(options, handler);
  }
}

class BaseUrlInterceptor extends UrlInterceptor {
  @override
  String get url => AppConfig.aiServiceApiEndpoint;
}

class LoginInterceptor extends UrlInterceptor {
  @override
  String get url => AppConfig.authServiceApiEndpoint;
}
