import 'dart:async';

import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/screens/login/controller/login_controller.dart';
import 'package:ai_vanse/widgets/app_button.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:ai_vanse/widgets/app_text_input.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late final LoginController controller = Get.find();

  final txtUsernameController = TextEditingController();
  final txtPasswordController = TextEditingController();

  bool showLoginForm = false;
  bool showPasswordVisibilityToggle = false;

  @override
  void initState() {
    super.initState();
    txtUsernameController.addListener(
      () => controller.setUsername(txtUsernameController.text),
    );
    txtPasswordController.addListener(
      () => controller.setPassword(txtPasswordController.text),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Timer(const Duration(milliseconds: 700), () {
        if (mounted) {
          setState(() {
            showLoginForm = true;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      resizeToAvoidBottomInset: true,
      autoHideKeyboard: true,
      body: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Flexible(flex: 1, child: _buildLogo()),
            _buildContent(),
            const Flexible(flex: 1, child: SizedBox.expand()),
          ],
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return GestureDetector(
      onTap: _preFill,
      child: Hero(
        tag: 'logo',
        child: Center(child: Assets.svgs.logo.svg(width: 232.sp)),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.sp),
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 600),
        opacity: showLoginForm ? 1 : 0,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 32.sp),
            AppTextInput(
              hintText: 'login_input_username'.tr,
              controller: txtUsernameController,
              textInputAction: TextInputAction.next,
              prefixIcon: SizedBox.square(
                dimension: 14.sp,
                child: Center(child: Assets.svgs.userActive.svg()),
              ),
              suffixIcon: const SizedBox.shrink(),
            ),
            SizedBox(height: 16.sp),
            AppTextInput(
              hintText: 'login_input_password'.tr,
              controller: txtPasswordController,
              password: !showPasswordVisibilityToggle,
              textInputAction: TextInputAction.done,
              prefixIcon: SizedBox.square(
                dimension: 14.sp,
                child: Center(child: Assets.svgs.lockActive.svg()),
              ),
              suffixIcon: IconButton(
                onPressed: () {
                  setState(() {
                    showPasswordVisibilityToggle =
                        !showPasswordVisibilityToggle;
                  });
                },
                icon:
                    showPasswordVisibilityToggle
                        ? Assets.svgs.eyeInactive.svg()
                        : Assets.svgs.eyeActive.svg(),
              ),
            ),
            SizedBox(height: 8.sp),
            _buildExtra(),
            SizedBox(height: 8.sp),
            Obx(
              () => AppButton(
                label: 'login_cta'.tr,
                onPressed:
                    controller.canContinue.value
                        ? () => controller.login()
                        : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExtra() {
    return Row(
      children: [
        const SizedBox(),
        const Spacer(),
        TextButton(
          onPressed: controller.selectServer,
          child: AppText('login_select_server'.tr.toLowerCase()),
        ),
      ],
    );
  }

  void _preFill() {
    if (!kDebugMode) return;
    txtUsernameController.text = 'daolivel';
    txtPasswordController.text = '123456';
  }
}
