import 'package:ai_vanse/repositories/user_repository.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:get/get.dart';

class SplashController extends GetxController {
  late final UserRepository _userRepository = Get.find();

  void init() {
    Future.wait([Future.delayed(const Duration(seconds: 3)), _checkIsLogin()]);
  }

  Future<void> _checkIsLogin() async {
    if (await _userRepository.hasToken) {
      await _fetchData();
      Get.offNamed(AppRoutes.home);
    } else {
      Get.offNamed(AppRoutes.login);
    }
  }

  Future<void> _fetchData() async {}
}
