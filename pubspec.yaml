name: ai_vanse
description: "a flutter bluetooth sensor program"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+6

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_blue_plus: ^1.33.4
  permission_handler: ^12.0.0+1
  fl_chart: ^0.69.0
  sensors_plus: ^6.1.0
  iirjdart: ^0.1.0
  shared_preferences: ^2.5.3
  intl: ^0.19.0
  photo_manager: ^3.6.4
  photo_manager_image_provider: ^2.2.0
  dotted_border: ^2.1.0
  camera: ^0.11.1
  device_info_plus: ^11.4.0
  translator: ^1.0.3+1
  flutter_localizations:
    sdk: flutter
  http: ^1.2.2
  sqflite: ^2.4.1
  path: ^1.9.0
  json_annotation: ^4.9.0
  event_bus: ^2.0.1
  envied: ^1.1.1
  get: ^4.7.2
  freezed: ^3.0.6
  dio: ^5.8.0+1
  retrofit: ^4.4.2
  pretty_dio_logger: ^1.4.0
  url_launcher: ^6.3.1
  image_picker: ^1.1.2
  flutter_udid: ^4.0.0
  package_info_plus: ^8.3.0
  flutter_screenutil: ^5.9.3
  dartx: ^1.2.0
  flutter_rounded_date_picker: ^3.0.4
  auto_size_text: ^3.0.0
  google_fonts: ^6.2.1
  flutter_secure_storage: ^9.2.4
  get_storage: ^2.1.1
  webview_flutter: ^4.13.0
  percent_indicator: ^4.2.5
  skeletonizer: ^1.4.3
  flutter_svg: ^2.1.0
  meta: ^1.16.0
  cached_network_image: ^3.4.1
  flutter_map: ^8.1.1
  latlong2: ^0.9.1
  flutter_image_compress: ^2.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.5
  envied_generator: ^1.1.1
  retrofit_generator: ^9.2.0
  flutter_gen_runner: ^5.10.0
  freezed_annotation: ^3.0.0

flutter:

  uses-material-design: true
  assets:
    - assets/images/
    - assets/svgs/

flutter_gen:
  output: lib/asset/
  line_length: 80

  integrations:
    flutter_svg: true
