import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTextButton extends StatelessWidget {
  final void Function()? onPressed;
  final String label;
  final Widget? icon;
  final Color? textColor;
  final TextAlign? textAlign;
  final AppTextStyle? appTextStyle;
  final MainAxisAlignment? mainAxisAlignment;
  final double? borderRadius;

  const AppTextButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.icon,
    this.textColor,
    this.textAlign,
    this.appTextStyle,
    this.mainAxisAlignment,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 32.sp),
        ),
      ),
      child: Row(
        mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
        children: [
          if (icon != null)
            Padding(padding: EdgeInsets.only(right: 4.sp), child: icon!),
          Expanded(
            child: AppText(
              label,
              appTextStyle: appTextStyle ?? AppTextStyle.bodyText,
              textAlign: textAlign ?? TextAlign.center,
              color: textColor ?? Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
