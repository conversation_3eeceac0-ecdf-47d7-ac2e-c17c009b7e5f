import 'package:ai_vanse/screens/web_view/controller/web_view_page_controller.dart';
import 'package:ai_vanse/widgets/app_loading.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewPage extends GetView<WebViewPageController> {
  const WebViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      title: Get.arguments['title'],
      body: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: AppLoading());
      }
      return WebViewWidget(controller: controller.webController);
    });
  }
}
