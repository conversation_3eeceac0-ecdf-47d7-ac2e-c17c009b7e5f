import 'package:ai_vanse/api/dio/options/json_option.dart';
import 'package:ai_vanse/models/overpass.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'overpass_api.g.dart';

@RestApi(baseUrl: 'https://overpass-api.de/api')
abstract class OverpassApi {
  factory OverpassApi() => _OverpassApi(Dio(JsonOption()));

  @GET('/interpreter')
  Future<HttpResponse<OverpassResponse>> getNearbyPois(
    @Query('data') String data,
  );
}
