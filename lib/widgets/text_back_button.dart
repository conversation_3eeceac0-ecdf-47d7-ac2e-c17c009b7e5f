import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TextBackButton extends StatelessWidget {
  const TextBackButton({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: Get.back,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedB<PERSON>(width: 8.5.sp),
          Icon(Icons.chevron_left, color: context.theme.colorScheme.tertiary),
          AppText(
            'common_cta_back'.tr,
            appTextStyle: AppTextStyle.bodyText,
            appTextColor: AppTextColor.tertiary,
            autoSize: true,
          ),
        ],
      ),
    );
  }
}
