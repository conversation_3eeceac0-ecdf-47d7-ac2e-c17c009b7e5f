import 'dart:async';
import 'dart:io';

import 'package:ai_vanse/services/dialog_service.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

enum PermissionGroup {
  gallery,
  camera,
  storage,
  notification,
  bluetooth,
  bluetoothScan,
  location,
  health,
}

abstract class PermissionService extends GetxService {
  PermissionService(PermissionGroup permission) {
    _permission = _convertToPermission(permission);
  }

  late final Permission _permission;
  late final DialogService _dialogService = Get.find();

  String get noPermissionMessage => switch (_permission) {
    Permission.camera => 'no_permission_camera'.tr,
    Permission.storage => 'no_permission_storage'.tr,
    Permission.photos => 'no_permission_gallery'.tr,
    Permission.activityRecognition => 'no_permission_health'.tr,
    _ => '',
  };

  String get limitedPermissionMessage => switch (_permission) {
    Permission.photos => 'limit_permission_gallery'.tr,
    _ => '',
  };

  Permission _convertToPermission(PermissionGroup permission) {
    switch (permission) {
      case PermissionGroup.camera:
        return Permission.camera;
      case PermissionGroup.gallery:
        if (Platform.isIOS) {
          return Permission.photos;
        }
        return Permission.storage;
      case PermissionGroup.storage:
        return Permission.storage;
      case PermissionGroup.notification:
        return Permission.notification;
      case PermissionGroup.bluetooth:
        return Permission.bluetooth;
      case PermissionGroup.bluetoothScan:
        return Permission.bluetoothScan;
      case PermissionGroup.location:
        return Permission.location;
      case PermissionGroup.health:
        return Permission.activityRecognition;
    }
  }

  Future<bool> isPermissionGranted() {
    return _permission.status.then(
      (status) => status == PermissionStatus.granted,
    );
  }

  Future<bool> _checkAndroidPermission(PermissionStatus status) async {
    if (status.isGranted) {
      return true;
    }
    if (status.isDenied) {
      return _checkRequestPermission();
    }
    if (status.isPermanentlyDenied) {
      showNoPermissionDialog();
      return false;
    }
    return _requestPermission();
  }

  Future<bool> _checkIOSPermission(PermissionStatus status) async {
    if (status.isGranted || status.isLimited) {
      return true;
    }
    if (status.isDenied) {
      return _checkRequestPermission();
    }
    if (status.isRestricted) {
      showNoPermissionDialog();
      return false;
    }
    if (status.isPermanentlyDenied) {
      showNoPermissionDialog();
      return false;
    }
    return _requestPermission();
  }

  Future<bool> _checkRequestPermission() async {
    final bool shouldShowRequestPermission =
        await _permission.shouldShowRequestRationale;
    if (shouldShowRequestPermission) {
      return _requestPermission();
    }
    final bool requestPermissionResult = await _requestPermission();
    if (requestPermissionResult) {
      return true;
    }

    final bool neverShowAgainChecked =
        !await _permission.shouldShowRequestRationale;
    if (neverShowAgainChecked) {
      showNoPermissionDialog();
    }
    return false;
  }

  Future<bool> requestPermission() async {
    final PermissionStatus status = await _permission.status;

    if (Platform.isIOS) {
      return _checkIOSPermission(status);
    }
    return _checkAndroidPermission(status);
  }

  Future<bool> _requestPermission() async {
    final PermissionStatus result = await _permission.request();
    return result == PermissionStatus.granted;
  }

  void showNoPermissionDialog() {
    _dialogService
        .showNoPermissionDialog(noPermissionMessage)
        .then(_openSetting);
  }

  void _openSetting(OkCancelResult? result) {
    if (result == OkCancelResult.ok) {
      openAppSetting();
    }
  }

  Future<bool> openAppSetting() => openAppSettings();
}

class StoragePermissionService extends PermissionService {
  StoragePermissionService() : super(PermissionGroup.storage);
}

class CameraPermissionService extends PermissionService {
  CameraPermissionService() : super(PermissionGroup.camera);
}

class GalleryPermissionService extends PermissionService {
  GalleryPermissionService() : super(PermissionGroup.gallery);

  @override
  Future<bool> requestPermission() async {
    final PermissionStatus status = await _permission.status;

    if (Platform.isIOS) {
      return _checkIOSPermission(status);
    }
    return true;
  }
}

class NotificationPermissionService extends PermissionService {
  NotificationPermissionService() : super(PermissionGroup.notification);
}

class BluetoothPermissionService extends PermissionService {
  BluetoothPermissionService() : super(PermissionGroup.bluetooth);
}

class BluetoothScanPermissionService extends PermissionService {
  BluetoothScanPermissionService() : super(PermissionGroup.bluetoothScan);
}

class LocationPermissionService extends PermissionService {
  LocationPermissionService() : super(PermissionGroup.location);
}

class HealthPermissionService extends PermissionService {
  HealthPermissionService() : super(PermissionGroup.health);
}
