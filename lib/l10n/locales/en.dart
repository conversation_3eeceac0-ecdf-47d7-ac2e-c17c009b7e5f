/// Translation for English ("EN")
// ignore_for_file: lines_longer_than_80_chars
abstract class LocaleEn {
  static const Map<String,String> data = {
    "common_cta_save": "Save",
    "common_cta_confirm": "Confirm",
    "common_cta_cancel": "Cancel",
    "common_cta_close": "Close",
    "common_cta_back": "Back",
    "common_cta_next": "Next",
    "common_cta_ok": "OK",
    "common_sort": "Sort",
    "common_sort_by_name_asc": "Name A > Z",
    "common_sort_by_name_desc": "Name Z > A",
    "common_sort_by_hn_asc": "HN 1 > 9",
    "common_sort_by_hn_desc": "HN 9 > 1",
    "common_sort_by_updated_asc": "Latest time",
    "common_sort_by_updated_desc": "Oldest time",
    "common_delete": "Delete",
    "common_success": "Success",
    "common_location": "Location",
    "common_year": "Years",
    "error_general_title": "An error occurred!",
    "error_general_message": "Please contact support or try again.",
    "error_login_error_invalid_username_password": "Invalid username or password",
    "login_title": "Welcome to Presense",
    "login_input_username": "Username",
    "login_input_password": "Password",
    "login_chk_remember_me": "Remember password",
    "login_link_forgot": "Forgot password",
    "login_cta": "Login",
    "login_cta_select_server": "Select Server",
    "login_pin_error_invalid_pin": "Invalid PIN",
    "server_pin_title": "access the server you are authorized for",
    "server_pin_warning1": "If you do not know the code",
    "server_pin_warning2": "Please contact the admin to request the code",
    "menu_follower": "Follower",
    "menu_patient": "Patient",
    "menu_notification": "Notification",
    "menu_me": "Me",
    "menu_setting": "Settings",
    "hello": "Hello",
    "patient_title": "Patients",
    "patient_total_label": "Patients",
    "patient_follow_up_title": "Follow up on patient care",
    "patient_follow_up_search_hint": "Search by name or patient ID",
    "patient_follow_up_total": "Patients under follow-up",
    "patient_follow_up_no_data": "No patients under follow-up found\nPlease refresh or manage patients to follow up",
    "patient_follow_up_no_data_cta": "Manage Patients",
    "patient_vital_signs_latest_data": "Latest record (@datetime)",
    "patient_go_to_vital_signs_cta": "Record vital signs",
    "patient_go_to_vital_signs_title": "Record vital signs",
    "patient_general_info": "General Info",
    "patient_summary_report": "Summary Report",
    "vital_signs_hearth_rate": "Heart rate",
    "vital_signs_hearth_rate_unit": "bpm",
    "vital_signs_blood_pressure": "Blood pressure",
    "vital_signs_blood_pressure_unit": "mm/Hg",
    "vital_signs_respiratory_rate": "Respiratory rate",
    "vital_signs_respiratory_rate_unit": "bpm",
    "vital_signs_temparature": "Temperature",
    "vital_signs_temparature_unit": "°C",
    "vital_signs_oxygen": "Oxygen saturation",
    "vital_signs_oxygen_unit": "%",
    "vital_signs_diabetes": "Blood sugar",
    "vital_signs_diabetes_unit": "mg/dL",
    "vital_signs_diabetes_addition_label": "Fasting before test",
    "vital_signs_weight": "Weight",
    "vital_signs_weight_unit": "kg",
    "vital_signs_height": "Height",
    "vital_signs_height_unit": "cm",
    "vital_signs_my_device": "My Devices",
    "vital_signs_read": "Read vital signs",
    "vital_signs_stop_read": "Stop read vital signs",
    "vital_signs_reading": "Reading vital signs...",
    "vital_signs_request_bluetooth": "Request Bluetooth",
    "vital_signs_devices_found": "Devices found",
    "vital_signs_connected_devices": "Connected devices",
    "vital_signs_dialog_success": "Vital signs have been recorded.",
    "vital_signs_dialog_fail": "Unable to record vital signs.",
    "vital_signs_title": "Vital Signs",
    "warning_setting_title": "Notification Setting",
    "warning_setting_enabled": "Enable notifications",
    "setting_stand_up": "Stand up",
    "setting_walk": "Walk",
    "setting_move": "Move",
    "setting_dont_move": "Stay still",
    "setting_dont_turn_over": "No turn over",
    "setting_fall": "Fall",
    "setting_low_battery": "Low battery",
    "setting_no_signal": "No signal",
    "setting_start_time": "from",
    "setting_end_time": "to",
    "setting_move_level": "Movement Level",
    "setting_move_low_level": "low",
    "setting_move_medium_level": "medium",
    "setting_move_high_level": "high",
    "setting_dont_move_limit": "Do not move",
    "setting_dont_turn_over_limit": "Do not turn over",
    "setting_dont_move_per_day": "Day",
    "setting_dont_turn_over_per_minute": "Minutes",
    "setting_dont_turn_over_per_hour": "Hours",
    "setting_dialog_success": "Settings saved successfully",
    "setting_dialog_error": "Unable to save settings",
    "setting_into_area": "Into",
    "setting_outof_area": "Out of",
    "setting_menu_logout": "Logout",
    "setting_menu_logout_confirmation": "Do you want to logout?",
    "setting_menu_device": "Device",
    "setting_menu_device_subtitle": "Add/Remove your devices",
    "setting_menu_notification": "Notification",
    "setting_menu_privacy_policy": "Privacy Policy",
    "setting_menu_hello": "Hello Nurse!",
    "patient_activity_title": "Activity",
    "patient_activity_sitting": "Sitting",
    "patient_activity_standing_normal": "Standing",
    "patient_activity_walking_normal": "Walking",
    "patient_activity_running": "Running",
    "patient_activity_jumping": "Jumping",
    "patient_activity_lying": "Lying",
    "patient_activity_lying_on_back": "Lying On Back",
    "patient_activity_lying_on_stomach": "Lying On Stomach",
    "patient_activity_lying_left": "Lying On Left",
    "patient_activity_lying_right": "Lying On Right",
    "patient_activity_getup": "Get Up",
    "patient_activity_movement": "Movement",
    "patient_activity_lowBattery": "Low Battery",
    "patient_activity_device_no_signal": "Device No Signal",
    "patient_activity_router_no_signal": "Router No Signal",
    "patient_activity_not_turn_for": "Not Turn For",
    "patient_activity_no_movement_for": "No Movement For",
    "patient_activity_fall": "Falling",
    "patient_activity_out_of_area": "Out Of Area",
    "patient_activity_into_area": "Into Area",
    "patient_activity_walking_at_night": "Walking At Night",
    "patient_activity_recharge_battery": "Recharge Battery",
    "patient_activity_device_bad_signal": "Device Bad Signal",
    "patient_activity_emergency_alarm": "Emergency Alarm",
    "patient_activity_unknown": "Unknown",
    "patient_edit_information": "Edit Patient Information",
    "patient_edit_dialog_success": "Patient information saved successfully",
    "patient_edit_dialog_error": "Unable to save patient information",
    "patient_edit_contact_dialog_success": "Contact information saved successfully",
    "patient_edit_contact_dialog_error": "Unable to save contact information",
    "patient_edit_custom": "Customize",
    "patient_information": "Patient Information",
    "patient_contact_information": "Contact Information",
    "patient_contact": "Contact",
    "patient_device": "Device",
    "patient_notification": "Notification",
    "notification_acknowledge": "Acknowledge",
    "notification_last_seen": "Last seen",
    "days_ago": "@day days ago",
    "user_profile_title": "Personal Information",
    "user_profile_edit_title": "Edit Personal Information",
    "change_password": "Change Password",
    "edit_user_profile": "Edit",
    "username": "Username",
    "email": "Email",
    "relationship": "Relationship",
    "date_of_birth": "Date of birth",
    "gender": "Gender",
    "blood_group": "Blood Group",
    "phone_number": "Phone Number",
    "patient_name": "Name",
    "name": "Name",
    "surname": "Surname",
    "new_password": "New Password",
    "new_password_again": "Enter Password Again",
    "password_not_valid": "Password must contain numbers and letters.",
    "password_not_long": "Password must be at least 6 characters long.",
    "password_not_same": "Password not same",
    "male": "Male",
    "female": "Female",
    "phone_not_valid": "Invalid Phone Number",
    "email_not_valid": "Invalid Email Format",
    "required": "Required",
    "change_password_dialog_success": "Change Password Success",
    "edit_user_profile_dialog_success": "Edit Personal Information Success",
    "add_device_title": "Scan QR Code",
    "add_device_description": "Place QR code in center",
    "add_device_toggle_flash": "Toggle flash",
    "add_device_flash_off": "Turn off flash",
    "add_device_flash_on": "Turn on flash",
    "add_device_gallery": "Open Gallery",
    "add_device_invalid": "Invalid QR Code",
    "add_device_duplicate": "Already have this sensor",
    "add_more_detail": "Add more details",
    "device_not_supported": "This device is not supported",
    "camera_access_denied": "Camera permission denied",
    "camera_access_open_setting": "Open Setting",
    "no_permission_gallery": "Cannot access photo. Please grant permission.",
    "no_permission_storage": "Cannot access storage. Please grant permission.",
    "no_permission_camera": "Cannot access camera. Please grant permission.",
    "no_permission_health": "Cannot access health data. Please grant permission.",
    "contact_info_spouse": "Spouse",
    "contact_info_children": "Children",
    "contact_info_relative": "Relative",
    "contact_info_other": "Other",
    "show_all": "Show all",
    "select_items": "Select @number Items",
    "select_item": "Select @number Item",
    "language": "Language",
    "home": "Home",
    "gait": "Gait",
    "journal": "Journal",
    "dietary": "Dietary",
    "profile": "Profile",
    "help": "Help",
    "settings": "Settings",
    "logout": "Logout",
    "confirmLogout": "Confirm Logout",
    "logoutConfirmMessage": "Are you sure you want to logout?",
    "appName": "AiVanse",
    "password": "Password",
    "pleaseEnterUsername": "Please enter username",
    "pleaseEnterPassword": "Please enter password",
    "login": "Login",
    "loginSuccessful": "Login successful",
    "loginFailed": "Login failed. Please check your credentials and try again.",
    "error": "An error occurred. Please try again later.",
    "g": "g",
    "ml": "ml",
    "todayIs": "Today is",
    "nutritionsummary": "Nutrition Summary",
    "kcal": "kcal",
    "carbs": "Carbs",
    "fats": "Fats",
    "protein": "Protein (g)",
    "dietaryFibre": "Dietary Fibre",
    "dailySugarConsumption": "Daily Sugar Consumption",
    "dailySaltConsumption": "Daily Salt Consumption",
    "dailyWaterConsumption": "Daily Water Consumption",
    "breakfast": "Breakfast",
    "lunch": "Lunch",
    "dinner": "Dinner",
    "snack": "Snack",
    "gram": "gram",
    "gaitAssessment": "Gait Assessment",
    "instructions": "Instructions",
    "gaitInstruction1": "The app will guide you through a series of gait assessments to help you understand your walking pattern.",
    "gaitInstruction2": "You will be asked to walk around, stand still, and perform other movements. Please ensure you have enough space to move around.",
    "gaitInstruction3": "In order to provide an accurate analysis report. Please ensure your bluetooth device is charged and connected.",
    "gaitInstruction4": "Each assessment will take 15 seconds to complete. Please pay close attention to the timer on the screen.",
    "gaitInstruction5": "If you feel unwell or uncomfortable during the assessment, please stop immediately and consult a healthcare professional.",
    "startAssessment": "Start Assessment",
    "connectToDevice": "Connect to Device",
    "myDevice": "My Devices",
    "availableDevices": "Available Devices",
    "connected": "Connected",
    "disconnected": "Disconnected",
    "assessment": "Assessment",
    "balanceAssessment": "Balance Assessment",
    "standStillFor15Seconds": "Stand Still for 15 seconds",
    "strideAssessment": "Stride Assessment",
    "walkFor15Seconds": "Walk for 15 seconds",
    "ok": "OK",
    "nextStep": "Next",
    "retry": "retry",
    "noJournalFound": "No journal entries found.",
    "stability": "Stability",
    "balanceVariance": "Balance Variance",
    "varianceAP": "Variance AP",
    "varianceML": "Variance ML",
    "stride": "Stride",
    "averageStepTime": "Average Step Time",
    "averageSwingTime": "Average Swing Time",
    "totalStepCount": "Total step count",
    "avgStepTime": "Average step time",
    "avgStrideVelocity": "Avg. stride velocity",
    "avgSwingTime": "Avg. swing time",
    "analysis": "Analysis",
    "clickToSelect": "Click to select / take an image",
    "aiResponse": "AI Response",
    "noResponse": "No response",
    "confirm": "Confirm",
    "foodNames": "Food Name(s)",
    "totalPortionSize": "Total Portion Size (g)",
    "energy": "Energy (kcal)",
    "fat": "Fat (g)",
    "carbohydrate": "Carbohydrate (g)",
    "fiber": "Fiber (g)",
    "totalSugars": "Total Sugars (g)",
    "calcium": "Calcium (mg)",
    "iron": "Iron (mg)",
    "magnesium": "Magnesium (mg)",
    "phosphorus": "Phosphorus (mg)",
    "potassium": "Potassium (mg)",
    "sodium": "Sodium (mg)",
    "zinc": "Zinc (mg)",
    "cholesterol": "Cholesterol (mg)",
    "serverError": "Server Error. Try Again Later.",
    "age": "Age",
    "weight": "Weight (kg)",
    "height": "Height (cm)",
    "nutritionalGoals": "Nutritional Goals",
    "calorieLimit": "Calorie Limit",
    "fatLimit": "Fat Limit (g)",
    "carbLimit": "Carb Limit (g)",
    "proteinLimit": "Protein Limit (g)",
    "fiberLimit": "Fiber Limit (g)",
    "waterIntakeGoal": "Water Intake Goal (ml)",
    "sugarIntakeGoal": "Sugar Intake Goal (g)",
    "saltIntakeGoal": "Salt Intake Goal (g)",
    "foodPreferences": "Food Preferences",
    "noFoodPreferences": "No food preferences",
    "vegetarian": "Vegetarian",
    "vegan": "Vegan",
    "pescetarian": "Pescetarian",
    "allergies": "Allergies",
    "noAllergies": "No Allergies",
    "glutenIntolerant": "Gluten Intolerant",
    "wheatIntolerant": "Wheat Intolerant",
    "lactoseIntolerant": "Lactose Intolerant",
    "allergicToMilk": "Allergic to Milk",
    "allergicToEgg": "Allergic to Egg",
    "allergicToShellfish": "Allergic to Shellfish",
    "allergicToFish": "Allergic to Fish",
    "allergicToNuts": "Allergic to Nuts",
    "personalInfo": "Personal Information",
    "profileUpdated": "Profile Updated!",
    "goalUpdated": "Goal Updated!",
    "saveChange": "Save Changes",
    "save": "Save",
    "cancel": "Cancel",
    "adjustUsingSlider": "Adjust using slider",
    "value": "Value",
    "saveDiet": "Save",
    "dietSaved": "Saved",
    "current_language": "English",
    "language_thai": "ไทย",
    "language_english": "English",
    "language_japanese": "日本語",
    "language_chinese_simplified": "简体中文",
    "common_weekDay_1": "Mon",
    "common_weekDay_2": "Tue",
    "common_weekDay_3": "Wed",
    "common_weekDay_4": "Thu",
    "common_weekDay_5": "Fri",
    "common_weekDay_6": "Sat",
    "common_weekDay_7": "Sun",
    "common_error_title_no_connection": "Cannot connect",
    "common_error_description_no_connection": "Please try again",
    "common_statistic": "Statistics",
    "no_permission_title": "No permission",
    "no_permission_message": "Please grant permission",
    "open_settings": "Open settings",
    "no_permission_health_connect_title": "Application not installed",
    "no_permission_health_connect_description": "Please install Health Connect",
    "install_health_connect": "Install",
    "login_select_server": "Select Server",
    "login_error_title_bad_credential": "cannot login",
    "login_error_description_bad_credential": "l':'Username or Password is not correct. Please try again",
    "login_select_server_enter_password": "Enter Admin Password",
    "login_select_server_enter_password_incorrect": "Incorrect Password",
    "home_title": "Home",
    "home_today": "Today",
    "home_detail_calorie": "Kcal",
    "home_detail_day_goal": "Daily Goal",
    "home_unit_step": "Step",
    "home_unit_distance": "Km",
    "home_unit_calorie": "Kcal",
    "home_unit_duration": "Hour",
    "statistic_title": "Statistics",
    "statistic_seven_day": "7 Days",
    "statistic_four_week": "4 Weeks",
    "statistic_one_year": "1 Year",
    "statistic_total_calorie": "TOTAL",
    "statistic_total_step": "TOTAL",
    "statistic_total_distance": "TOTAL",
    "statistic_total_duration": "TOTAL",
    "statistic_average_per_day": "AVG / DAY",
    "statistic_average_per_month": "AVG / MONTH",
    "setting_title": "Setting",
    "setting_menu_sensor": "Sensor",
    "setting_menu_sensor_sub_tittle": "Add/Remove your devices",
    "setting_menu_terms_and_conditions": "Terms and Conditions",
    "setting_menu_about_us": "About Us",
    "setting_disclaimer": "Data from AirNode, not medical advice. See a healthcare professional.",
    "user_data_title": "Profile",
    "user_data_gender": "Gender",
    "user_data_weight": "Weight (kg)",
    "user_data_height": "Height (cm)",
    "user_data_birthday": "Birthday",
    "user_data_age": "Age",
    "user_data_bm": "Body Mass Index (BMI)",
    "user_data_bmr": "Basal Metabolic Rate (BMR)",
    "user_data_bmr_detail": "Basal Metabolic Rate (BMR) represents the minimum amount of energy the body requires at rest. It is calculated using the Harris-Benedict equation.",
    "user_data_bmr_calculate": "Male: BMR = 10 x Weight(kg) + 6.25 x Height(cm) - 5 x Age(year) + 5\\n\\nFemale: BMR = 10 x Weight(kg) + 6.25 x Height(cm) - 5 x Age(year) - 161",
    "user_data_bmi_detail": "Body Mass Index (BMI) is an indicator used to assess an individual body weight status in relation to their height, determining whether it falls within a healthy range according to the following equation",
    "user_data_bmi_calculate": "BMI = Weight(kg) / Height(m) x Height(m)",
    "sleep_title": "Sleep",
    "ble_hub_start_fail": "Service cannot start",
    "ble_hub_start_fail_permission_not_granted": "Permission is not granted",
    "ble_hub_start_fail_bluetooth_not_enabled": "Bluetooth is not enabled",
    "ble_hub_start_fail_unknown": "unknown error",
    "ble_hub_start_fail_no_device": "No device",
    "ble_battery_not_available": "Not Available",
    "ble_title": "My Devices",
    "week": "7 Days",
    "month": "4 Weeks",
    "year": "1 Year",
    "sleeping_result": "Total Sleep Time",
    "sleeping_stat_result": "TOTAL",
    "daily_average": "Daily Average",
    "lying_on_back": "On Back",
    "lying_on_left_side": "On Left",
    "lying_on_right_side": "On Right",
    "lying_on_stomach": "On Stomach",
    "app_version": "V.1.0.0 (1)",
    "development": "Development",
    "version": "Version",
    "smart_watch_menu_title": "Smartwatch",
    "sync_button": "Sync Data",
    "notification_message": "Latest Sync Date",
    "dialog_title": "Confirm Sync",
    "dialog_message": "Are you sure you want to sync data?",
    "dialog_cancel": "Cancel",
    "dialog_confirm": "Sync",
    "close_button": "Close",
    "complete_sync": "Completed Sync",
    "dialog_granted_message": "MyActSense+ collects notification and nearby devices data for scan bluetooth in foreground service and show notification",
    "dialog_granted_accept": "Accept",
    "dialog_granted_deny": "Deny",
    "health_sync_description": "We will synchronize your health data to enable your caretaker to efficiently monitor your heart rate, blood oxygen level, and respiratory rate.",
    "calories": "Calories",
    "target": "Target"
};
}
