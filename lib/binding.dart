import 'package:ai_vanse/api/services/activity_api.dart';
import 'package:ai_vanse/api/services/auth_api.dart';
import 'package:ai_vanse/api/services/available_date_api.dart';
import 'package:ai_vanse/repositories/activity_repository.dart';
import 'package:ai_vanse/repositories/user_repository.dart';
import 'package:ai_vanse/services/dialog_service.dart';
import 'package:ai_vanse/services/identifier_service.dart';
import 'package:ai_vanse/services/permission_service.dart';
import 'package:ai_vanse/services/preference_service.dart';
import 'package:ai_vanse/services/secure_storage_service.dart';
import 'package:ai_vanse/services/token_service.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

Future<void> registerService() async {
  await Future.wait([
    registerSingletonServices(),
    registerRepositoryServices(),
  ]);
  await registerApiServices();
}

Future<void> registerSingletonServices() async {
  await Get.put(IdentifierService(), permanent: true).init();

  Get.lazyPut(GetStorage.new);
  Get.lazyPut(PreferenceService.new);
  Get.lazyPut(DialogService.new);

  Get.lazyPut(TokenService.new);

  Get.lazyPut(StoragePermissionService.new);
  Get.lazyPut(CameraPermissionService.new);
  Get.lazyPut(GalleryPermissionService.new);
  Get.lazyPut(NotificationPermissionService.new);
  Get.lazyPut(BluetoothPermissionService.new);
  Get.lazyPut(BluetoothScanPermissionService.new);
  Get.lazyPut(LocationPermissionService.new);
  Get.lazyPut(HealthPermissionService.new);
  Get.lazyPut(SecureStorageService.new);
}

Future<void> registerApiServices() async {
  Get.lazyPut(AuthApi.new);
  Get.lazyPut(ActivityApi.new);
  Get.lazyPut(AvailableDateApi.new);
}

Future<void> registerRepositoryServices() async {
  Get.lazyPut(UserRepository.new);

  Get.lazyPut(ActivityRepository.new);
}
