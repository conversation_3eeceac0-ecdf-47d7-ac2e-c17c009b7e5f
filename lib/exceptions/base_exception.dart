import 'package:get/get.dart';

abstract class BaseException implements Exception {
  const BaseException({String? title, String? code})
    : _title = title ?? 'error_general_title',
      _code = code ?? 'error_general_message';

  final String _title;
  final String _code;

  String get title {
    final key = _title.startsWith('error_') ? _title : 'error_$_title';
    final translation = key.tr;

    if (translation == key) {
      // Unable to find translation
      return 'error_general_title'.tr;
    }

    return translation;
  }

  String get code => _code;

  String get message {
    final key = _code.startsWith('error_') ? _code : 'error_$_code';
    final translation = key.tr;

    if (translation == key) {
      // Unable to find translation
      return '${'error_general_message'.tr}\n(error: $_code)';
    }

    return translation;
  }
}
