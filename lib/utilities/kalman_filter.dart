class Filter {
  double filter(double value) {
    return value;
  }
}

class <PERSON><PERSON><PERSON>ilter extends Filter {
  final double q; // Process noise covariance
  final double r; // Measurement noise covariance
  double x = 0.0; // Value
  double p = 1.0; // Estimation error covariance
  double k = 0.0; // Kalman gain

  KalmanFilter({this.q = 0.001, this.r = 0.1, this.x = 0.0});

  @override
  double filter(double value) {
    // Prediction update
    p = p + q;

    // Measurement update
    k = p / (p + r);
    x = x + k * (value - x);
    p = (1 - k) * p;

    return x;
  }
}
