import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:ai_vanse/constants/config.dart';
import 'package:ai_vanse/models/persist_data_service.dart';
import 'package:ai_vanse/models/translate_service.dart';
import 'package:ai_vanse/models/user_diet_data_model.dart';
import 'package:ai_vanse/screen/image_picker_screen.dart';
import 'package:ai_vanse/utilities/animations.dart';
import 'package:ai_vanse/utilities/event_bus.dart';
import 'package:ai_vanse/utilities/events.dart';
import 'package:ai_vanse/utilities/media_services.dart';
import 'package:ai_vanse/utilities/snackbar.dart';
import 'package:ai_vanse/utilities/theme_constants.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:photo_manager_image_provider/photo_manager_image_provider.dart';
import 'package:translator/translator.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;

class DietaryAssessmentScreen extends StatefulWidget {
  const DietaryAssessmentScreen({super.key});

  @override
  State<DietaryAssessmentScreen> createState() =>
      _DietaryAssessmentScreenState();
}

class _DietaryAssessmentScreenState extends State<DietaryAssessmentScreen>
    with WidgetsBindingObserver {
  AssetEntity? selectedAsset;
  bool hasAIResponse = false;
  bool waitingForResponse = false;
  bool dietSaved = false;
  final TranslateService translator = TranslateService();
  LinkedHashMap<String, dynamic> resultMap = LinkedHashMap<String, String>();
  LinkedHashMap<String, Object?> localizedResultMap =
      LinkedHashMap<String, Object?>();

  String selectedMealType = 'Breakfast';
  final List<String> mealTypes = ['Breakfast', 'Lunch', 'Dinner', 'Snack'];
  DateTime selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (hasAIResponse) {
      _updateAIResponseOutput();
    }
  }

  Future<void> _pickImage(int maxCount, RequestType requestType) async {
    if (waitingForResponse) {
      showSnackBar(context, 'Waiting for response...');
      return;
    }

    await MediaServices().requestPermissions();
    if (!MediaServices().permissionsGranted) {
      if (mounted) {
        showSnackBar(context, 'Please grant permissions to access media');
      }
      return;
    }

    if (mounted) {
      final result = await Navigator.push(
        context,
        getSlideNavigationRouteBuilder(
          ImagePickerScreen(maxCount: maxCount, requestType: requestType),
        ),
      );
      setState(() {
        selectedAsset = result;
      });
    }
  }

  Widget buildImagePreview() {
    final screenWidth = MediaQuery.of(context).size.width;

    return GestureDetector(
      onTap: () {
        _pickImage(1, RequestType.image);
      },
      child: DottedBorder(
        padding: EdgeInsets.all(0),
        borderType: BorderType.RRect,
        radius: Radius.circular(8.0),
        color: Color.fromARGB(140, 70, 70, 70),
        child: Container(
          decoration: BoxDecoration(
            color: Color.fromARGB(40, 180, 180, 180),
            borderRadius: BorderRadius.circular(8.0),
          ),
          clipBehavior: Clip.antiAlias,
          height: screenWidth * 0.75,
          child: Center(
            child:
                (selectedAsset == null)
                    ? Text('clickToSelect'.tr)
                    : SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: AssetEntityImage(
                        selectedAsset!,
                        isOriginal: false,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                        thumbnailSize: const ThumbnailSize.square(2000),
                      ),
                    ),
          ),
        ),
      ),
    );
  }

  Widget _buildAIResponseText() {
    if (waitingForResponse) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: CircularProgressIndicator(color: Colors.black87),
      );
    }
    if (!hasAIResponse) {
      return Text('noResponse'.tr);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          localizedResultMap.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 2.5),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      _getLocalizedKey(entry.key),
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      entry.value.toString(),
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.right,
                      softWrap: true,
                      maxLines: 10,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Future<String?> uploadImageToIMGBB(File imageFile) async {
    final imgbbApiKey = AppConfig.imgbbApiKey;
    final imgbbUploadEndpoint = AppConfig.imgbbUploadEndpoint;

    try {
      // Get original file size
      final originalSize = await imageFile.length();
      print(
        'Original image size: ${(originalSize / 1024 / 1024).toStringAsFixed(2)} MB',
      );

      final dir = path.dirname(imageFile.path);
      final ext = path.extension(imageFile.path);
      final compressedFile = File('$dir/compressed$ext');

      final result = await FlutterImageCompress.compressAndGetFile(
        imageFile.path,
        compressedFile.path,
        quality: 85,
        minWidth: 720,
        minHeight: 720,
      );

      if (result == null) {
        _handleError('Image compression failed');
        return null;
      }

      // Get compressed file size
      final compressedSize = await result.length();
      print(
        'Compressed image size: ${(compressedSize / 1024 / 1024).toStringAsFixed(2)} MB',
      );
      print(
        'Compression ratio: ${((1 - compressedSize / originalSize) * 100).toStringAsFixed(1)}%',
      );

      final url = Uri.parse('$imgbbUploadEndpoint$imgbbApiKey');
      final request = http.MultipartRequest('POST', url)
        ..files.add(await http.MultipartFile.fromPath('image', result.path));

      final response = await request.send();

      // Delete the compressed file after upload
      await compressedFile.delete();

      if (response.statusCode != 200) {
        return null;
      }

      final responseData = await response.stream.bytesToString();
      final jsonResponse = jsonDecode(responseData);
      return jsonResponse['data']['url'];
    } catch (e) {
      _handleError('Service error: Image upload failed');
      return null;
    }
  }

  Future<void> getAIResponse(File imageFile) async {
    setState(() {
      dietSaved = false;
      hasAIResponse = false;
      waitingForResponse = true;
    });

    try {
      final imageUrl = await uploadImageToIMGBB(imageFile);
      if (imageUrl == null) {
        _handleError('Service error: Image upload failed');
        return;
      }

      final apiEndpoint = AppConfig.aiServiceApiEndpoint;
      final response = await http
          .post(
            Uri.parse(apiEndpoint),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'image_path': imageUrl}),
          )
          .timeout(Duration(seconds: 90));

      if (response.statusCode != 200) {
        _handleError('Bad request: ${response.statusCode}');
        return;
      }

      if (!await _parseAIResponse(response.body)) {
        _handleError('Service error: invalid response');
        return;
      }

      hasAIResponse = true;
    } on TimeoutException catch (_) {
      _handleError('Bad request: timed out');
    } catch (e) {
      if (mounted) {
        _handleError('serverError'.tr);
      }
    } finally {
      waitingForResponse = false;
      setState(() {
        /* refresh button control */
      });
    }
  }

  String _getLocalizedKey(String key) {
    switch (key) {
      case 'foodNames':
        return 'foodNames'.tr;
      case 'totalPortionSize':
        return 'totalPortionSize'.tr;
      case 'energy':
        return 'energy'.tr;
      case 'protein':
        return 'protein'.tr;
      case 'fat':
        return 'fat'.tr;
      case 'carbohydrate':
        return 'carbohydrate'.tr;
      case 'fiber':
        return 'fiber'.tr;
      case 'totalSugars':
        return 'totalSugars'.tr;
      case 'calcium':
        return 'calcium'.tr;
      case 'iron':
        return 'iron'.tr;
      case 'magnesium':
        return 'magnesium'.tr;
      case 'phosphorus':
        return 'phosphorus'.tr;
      case 'potassium':
        return 'potassium'.tr;
      case 'sodium':
        return 'sodium'.tr;
      case 'zinc':
        return 'zinc'.tr;
      case 'cholesterol':
        return 'cholesterol'.tr;
      default:
        return key;
    }
  }

  Future<bool> _updateAIResponseOutput() async {
    _setWaitingForResponse(true);

    for (var entry in resultMap.entries) {
      var key = entry.key;
      var value = entry.value;

      if (!mounted) return false;

      final shouldTranslate = 'localeName'.tr == 'zh' && key == 'foodNames';

      if (shouldTranslate) {
        final translation = await translate(value);
        if (translation.isEmpty) {
          _setWaitingForResponse(false);
          return false;
        }
        localizedResultMap[key] = translation;
      } else {
        localizedResultMap[key] = value;
      }
    }

    _setWaitingForResponse(false);
    return true;
  }

  Future<String> translate(String value) async {
    try {
      final translator = GoogleTranslator();
      final translation = await translator.translate(value, to: 'zh-cn');

      return translation.text;
    } catch (e) {
      return '';
    }
  }

  void _setWaitingForResponse(bool value) {
    setState(() {
      waitingForResponse = value;
    });
  }

  Future<bool> _parseAIResponse(String response) async {
    final Map<String, dynamic> jsonResponse = jsonDecode(response);

    if (jsonResponse['error'] != null) {
      _handleError('Service error: Try again or choose a different image');
      return false;
    }

    final String? result = jsonResponse['result'];
    if (result == null || result.isEmpty) {
      _handleError('Service error: invalid result');
      return false;
    }

    final List<String> values = result.split(';').map((e) => e.trim()).toList();

    List<String> keys = [
      'foodNames',
      'totalPortionSize',
      'energy',
      'protein',
      'fat',
      'carbohydrate',
      'fiber',
      'totalSugars',
      'calcium',
      'iron',
      'magnesium',
      'phosphorus',
      'potassium',
      'sodium',
      'zinc',
      'cholesterol',
    ];

    for (int i = 0; i < keys.length; i++) {
      if (i < values.length) {
        resultMap[keys[i]] = values[i];
      }
    }

    return await _updateAIResponseOutput();
  }

  void _handleError(String message) {
    if (mounted) {
      showSnackBar(context, message);
    }
    setState(() {
      waitingForResponse = false;
    });
  }

  Future<void> _insertAiResponseToDb() async {
    if (hasAIResponse) {
      var formattedDate = DateTime.parse(
        DateFormat('yyyy-MM-dd').format(selectedDate),
      );
      var currentTimeStamp = formattedDate.millisecondsSinceEpoch;
      // print(
      //     "has ai response, now insert into database, selected meal type is $selectedMealType, $currentTimeStamp, $formattedDate");

      var diet = Diet(
        timestamp: currentTimeStamp,
        foodNames: localizedResultMap['foodNames'].toString(),
        totalPortionSize: int.parse(
          localizedResultMap['totalPortionSize'].toString(),
        ),
        energy: int.parse(localizedResultMap['energy'].toString()),
        protein: int.parse(localizedResultMap['protein'].toString()),
        fat: int.parse(localizedResultMap['fat'].toString()),
        carbohydrate: int.parse(localizedResultMap['carbohydrate'].toString()),
        fiber: int.parse(localizedResultMap['fiber'].toString()),
        totalSugars: int.parse(localizedResultMap['totalSugars'].toString()),
        calcium: int.parse(localizedResultMap['calcium'].toString()),
        iron: int.parse(localizedResultMap['iron'].toString()),
        magnesium: int.parse(localizedResultMap['magnesium'].toString()),
        phosphorus: int.parse(localizedResultMap['phosphorus'].toString()),
        potassium: int.parse(localizedResultMap['potassium'].toString()),
        sodium: int.parse(localizedResultMap['sodium'].toString()),
        zinc: int.parse(localizedResultMap['zinc'].toString()),
        cholesterol: int.parse(localizedResultMap['cholesterol'].toString()),
        mealType: selectedMealType,
      );

      await PersistDataService().insertDiet(diet.toMap());
    }
  }

  Widget _buildSaveDietButton() {
    return ElevatedButton(
      onPressed:
          !hasAIResponse || dietSaved
              ? null
              : () async {
                await _insertAiResponseToDb();
                if (mounted) {
                  showSnackBar(context, 'dietSaved'.tr);
                }
                setState(() {
                  dietSaved = true;
                });
                eventBus.fire(DietSaveBtnTriggerEmit(dietSaved));
              },
      child: Text('saveDiet'.tr),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: ListView(
            children: <Widget>[
              Text(
                'dietary'.tr,
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              Divider(),
              buildImagePreview(),
              Divider(color: Colors.transparent, height: 20),
              _buildExecuteButton(context),
              Divider(),
              _buildAIResponseBox(context),
              Divider(color: Colors.transparent, height: 20),
              _buildDatePicker(),
              _buildMealTypeSelector(context),
              _buildSaveDietButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDatePicker() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          DateFormat.yMMMMd(
            Localizations.localeOf(context).toString(),
          ).format(selectedDate),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        IconButton(
          icon: Icon(Icons.calendar_month_outlined),
          onPressed: () async {
            DateTime? pickedDate = await showDatePicker(
              context: context,
              initialDate: selectedDate,
              firstDate: DateTime(2020),
              lastDate: DateTime(2101),
            );
            if (pickedDate != null && pickedDate != selectedDate) {
              setState(() {
                selectedDate = pickedDate; // Update the selected date
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildMealTypeSelector(BuildContext context) {
    return DropdownButton<String>(
      value: selectedMealType,
      onChanged: (String? newValue) {
        if (newValue != null) {
          setState(() {
            selectedMealType = newValue;
          });
        }
      },
      items:
          mealTypes.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                getLocalizedMealType(value, context),
              ), // Fetch localized string
            );
          }).toList(),
    );
  }

  String getLocalizedMealType(String key, BuildContext context) {
    switch (key) {
      case 'Breakfast':
        return 'breakfast'.tr;
      case 'Lunch':
        return 'lunch'.tr;
      case 'Dinner':
        return 'dinner'.tr;
      case 'Snack':
        return 'snack'.tr;
      default:
        return key;
    }
  }

  Widget _buildExecuteButton(BuildContext context) {
    return ElevatedButton(
      onPressed:
          selectedAsset == null || waitingForResponse
              ? null
              : _executeButtonPressed,
      child: Text('analysis'.tr),
    );
  }

  void _executeButtonPressed() async {
    final file = await selectedAsset!.file;
    if (file != null) {
      await getAIResponse(file);
    }
  }

  Container _buildAIResponseBox(BuildContext context) {
    return Container(
      decoration: glassBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Text(
              'aiResponse'.tr,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Divider(color: Colors.transparent),
            _buildAIResponseText(),
          ],
        ),
      ),
    );
  }
}
