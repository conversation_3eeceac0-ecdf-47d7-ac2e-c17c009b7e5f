import 'package:json_annotation/json_annotation.dart';

part 'user_diet_data_model.g.dart';

@JsonSerializable()
class Diet {
  final int timestamp;
  final String foodNames;
  final int totalPortionSize;
  final int energy;
  final int protein;
  final int fat;
  final int carbohydrate;
  final int fiber;
  final int totalSugars;
  final int calcium;
  final int iron;
  final int magnesium;
  final int phosphorus;
  final int potassium;
  final int sodium;
  final int zinc;
  final int cholesterol;
  final String mealType;

  const Diet({
    required this.timestamp,
    required this.foodNames,
    required this.totalPortionSize,
    required this.energy,
    required this.protein,
    required this.fat,
    required this.carbohydrate,
    required this.fiber,
    required this.totalSugars,
    required this.calcium,
    required this.iron,
    required this.magnesium,
    required this.phosphorus,
    required this.potassium,
    required this.sodium,
    required this.zinc,
    required this.cholesterol,
    required this.mealType,
  });

  factory Diet.fromMap(Map<String, Object?> map) {
    return Diet(
      timestamp: map['timestamp'] as int,
      foodNames: map['foodNames'] as String,
      totalPortionSize: map['totalPortionSize'] as int,
      energy: map['energy'] as int,
      protein: map['protein'] as int,
      fat: map['fat'] as int,
      carbohydrate: map['carbohydrate'] as int,
      fiber: map['fiber'] as int,
      totalSugars: map['totalSugars'] as int,
      calcium: map['calcium'] as int,
      iron: map['iron'] as int,
      magnesium: map['magnesium'] as int,
      phosphorus: map['phosphorus'] as int,
      potassium: map['potassium'] as int,
      sodium: map['sodium'] as int,
      zinc: map['zinc'] as int,
      cholesterol: map['cholesterol'] as int,
      mealType: map['mealType'] as String,
    );
  }

  Map<String, Object?> toMap() {
    return {
      'timestamp': timestamp,
      'foodNames': foodNames,
      'totalPortionSize': totalPortionSize,
      'energy': energy,
      'protein': protein,
      'fat': fat,
      'carbohydrate': carbohydrate,
      'fiber': fiber,
      'totalSugars': totalSugars,
      'calcium': calcium,
      'iron': iron,
      'magnesium': magnesium,
      'phosphorus': phosphorus,
      'potassium': potassium,
      'sodium': sodium,
      'zinc': zinc,
      'cholesterol': cholesterol,
      'mealType': mealType,
    };
  }
}

String get createDietTableString =>
    'CREATE TABLE diets(timestamp INTEGER, foodNames TEXT, totalPortionSize INTEGER, energy INTEGER, protein INTEGER, fat INTEGER, carbohydrate INTEGER, fiber INTEGER, totalSugars INTEGER, calcium INTEGER, iron INTEGER, magnesium INTEGER, phosphorus INTEGER, potassium INTEGER, sodium INTEGER, zinc INTEGER, cholesterol INTEGER, mealType TEXT)';

// dart command to generate the user_diet_data_model.g.dart file
// dart run build_runner build --delete-conflicting-outputs
