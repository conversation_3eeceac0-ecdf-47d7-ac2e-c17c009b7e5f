import 'package:ai_vanse/api/dio/dio_client.dart';
import 'package:ai_vanse/model/activity/activity.dart';
import 'package:ai_vanse/model/activity/activity_year.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'activity_api.g.dart';

@RestApi(baseUrl: 'api/activitysummary')
abstract class ActivityApi {
  factory ActivityApi() =>
      _ActivityApi(DioClient.getInstance(authenticated: false));

  @GET('/{patientId}/as/date/{date}')
  Future<HttpResponse<Activity>> getActivityAsDay({
    @Path('patientId') required int patientId,
    @Path('date') required String date,
  });

  @GET('/{patientId}/as/week/{date}')
  Future<HttpResponse<List<Activity>>> getActivityAsWeek({
    @Path('patientId') required int patientId,
    @Path('date') required String date,
  });

  @GET('/{patientId}/as/month/{date}')
  Future<HttpResponse<List<Activity>>> getActivityAsMonth({
    @Path('patientId') required int patientId,
    @Path('date') required String date,
  });

  @GET('/{patientId}/as/year/{date}')
  Future<HttpResponse<List<ActivityYear>>> getActivityAsYear({
    @Path('patientId') required int patientId,
    @Path('date') required String date,
  });
}
