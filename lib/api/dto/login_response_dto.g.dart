// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_response_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LoginResponseDto _$LoginResponseDtoFromJson(Map<String, dynamic> json) =>
    _LoginResponseDto(
      accessToken: json['AccessToken'] as String,
      idToken: json['IdToken'] as String,
      refreshToken: json['RefreshToken'] as String,
      tokenType: json['TokenType'] as String,
      expiresIn: (json['ExpiresIn'] as num).toInt(),
    );

Map<String, dynamic> _$LoginResponseDtoToJson(_LoginResponseDto instance) =>
    <String, dynamic>{
      'AccessToken': instance.accessToken,
      'IdToken': instance.idToken,
      'RefreshToken': instance.refreshToken,
      'TokenType': instance.tokenType,
      'ExpiresIn': instance.expiresIn,
    };
