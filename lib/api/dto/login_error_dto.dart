import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_error_dto.freezed.dart';
part 'login_error_dto.g.dart';

@freezed
abstract class LoginErrorDto with _$LoginErrorDto {
  @JsonSerializable(fieldRename: FieldRename.none, explicitToJson: true)
  const factory LoginErrorDto({
    @JsonKey(name: '__type') String? type,
    String? message,
  }) = _LoginErrorDto;

  factory LoginErrorDto.fromJson(dynamic json) => _$LoginErrorDtoFromJson(json);
}
