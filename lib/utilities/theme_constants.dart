import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

ThemeData get lightTheme => ThemeData(
  appBarTheme: const AppBarTheme(
    shadowColor: Colors.teal,
    elevation: 1.0,
    centerTitle: false,
    color: Colors.teal,
    titleTextStyle: TextStyle(
      color: Colors.white70,
      fontSize: 20.0,
      fontWeight: FontWeight.bold,
    ),
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.teal,
      foregroundColor: Colors.white,
    ),
  ),
);

BoxDecoration get glassBoxDecoration => BoxDecoration(
  color: Color.fromARGB(40, 180, 180, 180),
  borderRadius: BorderRadius.circular(12.0),
);

BoxDecoration get selectedBoxDecoration => BoxDecoration(
  color: Colors.blue[500],
  borderRadius: BorderRadius.circular(12.0),
);

ButtonStyle get positiveElevatedButtonStyle => ElevatedButton.styleFrom(
  elevation: 5,
  backgroundColor: Colors.blue,
  foregroundColor: Colors.white,
);

ButtonStyle get negativeElevatedButtonStyle => ElevatedButton.styleFrom(
  elevation: 5,
  side: BorderSide(color: Colors.red, width: 2),
  foregroundColor: Colors.red,
  backgroundColor: Colors.white,
);
