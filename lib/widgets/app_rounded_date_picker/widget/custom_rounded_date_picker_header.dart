// ignore_for_file: implementation_imports

import 'package:flutter/material.dart';
import 'package:flutter_rounded_date_picker/src/era_mode.dart';
import 'package:flutter_rounded_date_picker/src/material_rounded_date_picker_style.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomRoundedDatePickerHeader extends StatelessWidget {
  const CustomRoundedDatePickerHeader({
    super.key,
    required this.selectedDate,
    required this.mode,
    required this.onModeChanged,
    required this.orientation,
    required this.era,
    required this.borderRadius,
    this.imageHeader,
    this.description = '',
    this.fontFamily,
    this.style,
  });

  final DateTime selectedDate;
  final DatePickerMode mode;
  final ValueChanged<DatePickerMode> onModeChanged;
  final Orientation orientation;
  final MaterialRoundedDatePickerStyle? style;

  /// Era custom
  final EraMode era;

  /// Border
  final double borderRadius;

  ///  Header
  final ImageProvider? imageHeader;

  /// Header description
  final String description;

  /// Font
  final String? fontFamily;

  void _handleChangeMode(DatePickerMode value) {
    if (value != mode) onModeChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    final MaterialLocalizations localizations = MaterialLocalizations.of(
      context,
    );
    final ThemeData themeData = Theme.of(context);
    final TextTheme headerTextTheme = themeData.primaryTextTheme;
    Color? dayColor;
    Color? yearColor;
    switch (themeData.brightness) {
      case Brightness.light:
        dayColor = mode == DatePickerMode.day ? Colors.black87 : Colors.black54;
        yearColor =
            mode == DatePickerMode.year ? Colors.black87 : Colors.black54;
        break;
      case Brightness.dark:
        dayColor = mode == DatePickerMode.day ? Colors.white : Colors.white70;
        yearColor = mode == DatePickerMode.year ? Colors.white : Colors.white70;
        break;
    }

    if (style?.textStyleDayButton?.color != null) {
      style?.textStyleDayButton = style?.textStyleDayButton?.copyWith(
        color: style?.textStyleDayButton?.color ?? dayColor,
      );
    }

    if (style?.textStyleDayButton?.fontFamily != null) {
      style?.textStyleDayButton = style?.textStyleDayButton?.copyWith(
        fontFamily: fontFamily,
      );
    }

    final TextStyle dayStyle =
        style?.textStyleDayButton ??
        headerTextTheme.headlineMedium!.copyWith(
          color: dayColor,
          fontFamily: fontFamily,
        );
    final TextStyle yearStyle =
        style?.textStyleYearButton ??
        headerTextTheme.titleMedium!.copyWith(
          color: yearColor,
          fontFamily: fontFamily,
        );

    Color? backgroundColor;
    if (style?.backgroundHeader != null) {
      backgroundColor = style?.backgroundHeader;
    } else {
      backgroundColor = themeData.primaryColor;
    }

    EdgeInsets padding;
    MainAxisAlignment mainAxisAlignment;
    switch (orientation) {
      case Orientation.landscape:
        padding = style?.paddingDateYearHeader ?? EdgeInsets.all(8.sp);
        mainAxisAlignment = MainAxisAlignment.start;
        break;
      case Orientation.portrait:
        padding = style?.paddingDateYearHeader ?? EdgeInsets.all(16.sp);
        mainAxisAlignment = MainAxisAlignment.center;
        break;
    }

    final Widget yearButton = IgnorePointer(
      ignoring: mode != DatePickerMode.day,
      child: _DateHeaderButton(
        color: Colors.transparent,
        onTap: Feedback.wrapForTap(
          () => _handleChangeMode(DatePickerMode.year),
          context,
        ),
        child: Semantics(
          selected: mode == DatePickerMode.year,
          child: Text(
            '${calculateYearEra(era, selectedDate.year)}',
            style: yearStyle,
          ),
        ),
      ),
    );

    final Widget descriptionWidget = Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 8.sp),
      child: Text(
        description,
        style: TextStyle(
          color: yearStyle.color,
          fontSize: 14.sp,
          fontFamily: fontFamily,
        ),
      ),
    );

    final Widget dayButton = IgnorePointer(
      ignoring: mode == DatePickerMode.day,
      child: _DateHeaderButton(
        color: Colors.transparent,
        onTap: Feedback.wrapForTap(
          () => _handleChangeMode(DatePickerMode.day),
          context,
        ),
        child: Semantics(
          selected: mode == DatePickerMode.day,
          child: Text(
            localizations.formatMediumDate(selectedDate),
            style: dayStyle,
          ),
        ),
      ),
    );

    BorderRadius borderRadiusData = BorderRadius.only(
      topLeft: Radius.circular(borderRadius),
      topRight: Radius.circular(borderRadius),
    );

    if (orientation == Orientation.landscape) {
      borderRadiusData = BorderRadius.only(
        topLeft: Radius.circular(borderRadius),
        bottomLeft: Radius.circular(borderRadius),
      );
    }

    return Container(
      width: 168.sp,
      decoration: BoxDecoration(
        image:
            imageHeader != null
                ? DecorationImage(image: imageHeader!, fit: BoxFit.cover)
                : null,
        color: backgroundColor,
        borderRadius: borderRadiusData,
      ),
      padding: padding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if (description.isNotEmpty) descriptionWidget,
          yearButton,
          dayButton,
        ],
      ),
    );
  }
}

class _DateHeaderButton extends StatelessWidget {
  const _DateHeaderButton({this.onTap, this.color, this.child});

  final VoidCallback? onTap;
  final Color? color;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    return Material(
      type: MaterialType.button,
      color: color,
      child: InkWell(
        borderRadius: kMaterialEdges[MaterialType.button],
        highlightColor: theme.highlightColor,
        splashColor: theme.splashColor,
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 8.sp),
          child: child,
        ),
      ),
    );
  }
}
