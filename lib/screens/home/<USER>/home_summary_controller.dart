import 'package:ai_vanse/model/activity/activity.dart';
import 'package:ai_vanse/model/activity/activity_summary.dart';
import 'package:ai_vanse/model/activity/activity_unit.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/screens/home/<USER>/meal_item.dart';
import 'package:get/get.dart';

class HomeSummaryController extends GetxController {
  // final ActivityRepository _activityRepository = Get.find();
  // final UserRepository _userRepository = Get.find();

  // late final DialogService _dialogService = Get.find();

  final RxBool isLoading = RxBool(false);
  final Rxn<Activity> activity = Rxn();
  final Rx<ActivitySummary> activitySummary = Rx(
    ActivitySummary.fromActivity(),
  );

  Rx<ActivityUnit> get unit => ActivityUnit.calorie.obs;
  final RxDouble goal = RxDouble(2000);
  final RxDouble progress = RxDouble(0);

  int get totalEnergy => activity.value?.energy?.total ?? 580;

  final meals =
      <MealItem>[
        MealItem(
          name: 'Pho',
          imageUrl:
              'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43',
          time: '12:15',
          calories: 180,
          progress: 0.7,
        ),
        MealItem(
          name: 'Pad Thai',
          imageUrl: 'https://images.unsplash.com/photo-**********-0d155014e29e',
          time: '14:15',
          calories: 400,
          progress: 0.85,
        ),
      ].obs;

  @override
  void onInit() {
    super.onInit();
    everAll([activity, goal], (_) => updateProgress());
    // ever(_activityRepository.selectedDate, onDateChange);
  }

  @override
  void onReady() {
    super.onReady();
    _fetchActivity();
    fetchAvailableDate();
    // _userRepository.patient.goal?.let(goal.call);
  }

  void onCalendar() {}

  Future<void> onDateChange(DateTime date) async {
    _fetchActivity();
  }

  Future<void> _fetchActivity() async {
    // isLoading.value = true;
    // try {
    //   final Activity activityAsDay =
    //       await _activityRepository.getActivityAsDay();
    //   activity.value = activityAsDay;
    //   activitySummary.value = ActivitySummary.fromActivity(
    //     activity: activityAsDay,
    //   );
    // } catch (e, stacktrace) {
    //   activitySummary.value = ActivitySummary.fromActivity();
    //   debugPrintStack(label: e.toString(), stackTrace: stacktrace);
    // } finally {
    //   isLoading.value = false;
    // }
  }

  Future<void> fetchAvailableDate() async {
    // return _activityRepository.getAvailableDateByPatient();
  }

  void updateProgress() {
    progress.value = (totalEnergy) / goal.value;
  }

  void onGraph() {
    Get.toNamed(AppRoutes.activityStatistic);
  }
}
