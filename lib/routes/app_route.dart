abstract class AppRoutes {
  static const home = '/';
  static const example = '/example';
  static const welcome = '/welcome';
  static const login = '/login';
  static const assertmentInfo = '/assertment/info';
  static const assertment = '/assertment';
  static const balanceAssertmentInfo = '/balanceAssertment/info';
  static const balanceAssertment = '/balanceAssertment';
  static const camera = '/camera';
  static const dietaryHome = '/dietaryHome';
  static const findDevice = '/device';
  static const exampleGrid = '/example-grid';
  static const characterDetail = '/example/character';
  static const selectServer = '/login/select-server';
  static const selectServerPassword = '/login/select-server-password';
  static const loginOTP = '/login/otp';
  static const web = '/web';
  static const userInfo = '/user-info';
  static const setting = '/setting';
  static const settingDevice = '/setting/device';
  static const sleepingStats = '/sleeping/stats';
  static const sleeping = '/sleeping';
  static const activityStatistic = '/activity/statistic';
  static const smartWatchConnect = '/smartwatch/connect';
  static const foodChoiceDetail = '/food-choice/detail';
  static const languageSetting = '/setting/language';
  static const imagePicker = '/image-picker';
  static const createMeal = '/create-meal';
  static const capture = '/capture';
}
