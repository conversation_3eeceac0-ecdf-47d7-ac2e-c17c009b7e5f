// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'activity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Activity {

 Energy? get energy; ActivityDuration? get duration; int? get steps; int? get distance; DateTime? get createdDate;
/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ActivityCopyWith<Activity> get copyWith => _$ActivityCopyWithImpl<Activity>(this as Activity, _$identity);

  /// Serializes this Activity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Activity&&(identical(other.energy, energy) || other.energy == energy)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.steps, steps) || other.steps == steps)&&(identical(other.distance, distance) || other.distance == distance)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,energy,duration,steps,distance,createdDate);

@override
String toString() {
  return 'Activity(energy: $energy, duration: $duration, steps: $steps, distance: $distance, createdDate: $createdDate)';
}


}

/// @nodoc
abstract mixin class $ActivityCopyWith<$Res>  {
  factory $ActivityCopyWith(Activity value, $Res Function(Activity) _then) = _$ActivityCopyWithImpl;
@useResult
$Res call({
 Energy? energy, ActivityDuration? duration, int? steps, int? distance, DateTime? createdDate
});


$EnergyCopyWith<$Res>? get energy;$ActivityDurationCopyWith<$Res>? get duration;

}
/// @nodoc
class _$ActivityCopyWithImpl<$Res>
    implements $ActivityCopyWith<$Res> {
  _$ActivityCopyWithImpl(this._self, this._then);

  final Activity _self;
  final $Res Function(Activity) _then;

/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? energy = freezed,Object? duration = freezed,Object? steps = freezed,Object? distance = freezed,Object? createdDate = freezed,}) {
  return _then(_self.copyWith(
energy: freezed == energy ? _self.energy : energy // ignore: cast_nullable_to_non_nullable
as Energy?,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as ActivityDuration?,steps: freezed == steps ? _self.steps : steps // ignore: cast_nullable_to_non_nullable
as int?,distance: freezed == distance ? _self.distance : distance // ignore: cast_nullable_to_non_nullable
as int?,createdDate: freezed == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}
/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EnergyCopyWith<$Res>? get energy {
    if (_self.energy == null) {
    return null;
  }

  return $EnergyCopyWith<$Res>(_self.energy!, (value) {
    return _then(_self.copyWith(energy: value));
  });
}/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActivityDurationCopyWith<$Res>? get duration {
    if (_self.duration == null) {
    return null;
  }

  return $ActivityDurationCopyWith<$Res>(_self.duration!, (value) {
    return _then(_self.copyWith(duration: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _Activity implements Activity {
  const _Activity({this.energy, this.duration, this.steps, this.distance, this.createdDate});
  factory _Activity.fromJson(Map<String, dynamic> json) => _$ActivityFromJson(json);

@override final  Energy? energy;
@override final  ActivityDuration? duration;
@override final  int? steps;
@override final  int? distance;
@override final  DateTime? createdDate;

/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ActivityCopyWith<_Activity> get copyWith => __$ActivityCopyWithImpl<_Activity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ActivityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Activity&&(identical(other.energy, energy) || other.energy == energy)&&(identical(other.duration, duration) || other.duration == duration)&&(identical(other.steps, steps) || other.steps == steps)&&(identical(other.distance, distance) || other.distance == distance)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,energy,duration,steps,distance,createdDate);

@override
String toString() {
  return 'Activity(energy: $energy, duration: $duration, steps: $steps, distance: $distance, createdDate: $createdDate)';
}


}

/// @nodoc
abstract mixin class _$ActivityCopyWith<$Res> implements $ActivityCopyWith<$Res> {
  factory _$ActivityCopyWith(_Activity value, $Res Function(_Activity) _then) = __$ActivityCopyWithImpl;
@override @useResult
$Res call({
 Energy? energy, ActivityDuration? duration, int? steps, int? distance, DateTime? createdDate
});


@override $EnergyCopyWith<$Res>? get energy;@override $ActivityDurationCopyWith<$Res>? get duration;

}
/// @nodoc
class __$ActivityCopyWithImpl<$Res>
    implements _$ActivityCopyWith<$Res> {
  __$ActivityCopyWithImpl(this._self, this._then);

  final _Activity _self;
  final $Res Function(_Activity) _then;

/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? energy = freezed,Object? duration = freezed,Object? steps = freezed,Object? distance = freezed,Object? createdDate = freezed,}) {
  return _then(_Activity(
energy: freezed == energy ? _self.energy : energy // ignore: cast_nullable_to_non_nullable
as Energy?,duration: freezed == duration ? _self.duration : duration // ignore: cast_nullable_to_non_nullable
as ActivityDuration?,steps: freezed == steps ? _self.steps : steps // ignore: cast_nullable_to_non_nullable
as int?,distance: freezed == distance ? _self.distance : distance // ignore: cast_nullable_to_non_nullable
as int?,createdDate: freezed == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$EnergyCopyWith<$Res>? get energy {
    if (_self.energy == null) {
    return null;
  }

  return $EnergyCopyWith<$Res>(_self.energy!, (value) {
    return _then(_self.copyWith(energy: value));
  });
}/// Create a copy of Activity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ActivityDurationCopyWith<$Res>? get duration {
    if (_self.duration == null) {
    return null;
  }

  return $ActivityDurationCopyWith<$Res>(_self.duration!, (value) {
    return _then(_self.copyWith(duration: value));
  });
}
}


/// @nodoc
mixin _$ActivityDuration {

 int? get lying; int? get lyingOnBack; int? get lyingOnStomach; int? get lyingOnLeft; int? get lyingOnRight; int? get sitting; int? get standing; int? get walking;
/// Create a copy of ActivityDuration
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ActivityDurationCopyWith<ActivityDuration> get copyWith => _$ActivityDurationCopyWithImpl<ActivityDuration>(this as ActivityDuration, _$identity);

  /// Serializes this ActivityDuration to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ActivityDuration&&(identical(other.lying, lying) || other.lying == lying)&&(identical(other.lyingOnBack, lyingOnBack) || other.lyingOnBack == lyingOnBack)&&(identical(other.lyingOnStomach, lyingOnStomach) || other.lyingOnStomach == lyingOnStomach)&&(identical(other.lyingOnLeft, lyingOnLeft) || other.lyingOnLeft == lyingOnLeft)&&(identical(other.lyingOnRight, lyingOnRight) || other.lyingOnRight == lyingOnRight)&&(identical(other.sitting, sitting) || other.sitting == sitting)&&(identical(other.standing, standing) || other.standing == standing)&&(identical(other.walking, walking) || other.walking == walking));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lying,lyingOnBack,lyingOnStomach,lyingOnLeft,lyingOnRight,sitting,standing,walking);

@override
String toString() {
  return 'ActivityDuration(lying: $lying, lyingOnBack: $lyingOnBack, lyingOnStomach: $lyingOnStomach, lyingOnLeft: $lyingOnLeft, lyingOnRight: $lyingOnRight, sitting: $sitting, standing: $standing, walking: $walking)';
}


}

/// @nodoc
abstract mixin class $ActivityDurationCopyWith<$Res>  {
  factory $ActivityDurationCopyWith(ActivityDuration value, $Res Function(ActivityDuration) _then) = _$ActivityDurationCopyWithImpl;
@useResult
$Res call({
 int? lying, int? lyingOnBack, int? lyingOnStomach, int? lyingOnLeft, int? lyingOnRight, int? sitting, int? standing, int? walking
});




}
/// @nodoc
class _$ActivityDurationCopyWithImpl<$Res>
    implements $ActivityDurationCopyWith<$Res> {
  _$ActivityDurationCopyWithImpl(this._self, this._then);

  final ActivityDuration _self;
  final $Res Function(ActivityDuration) _then;

/// Create a copy of ActivityDuration
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lying = freezed,Object? lyingOnBack = freezed,Object? lyingOnStomach = freezed,Object? lyingOnLeft = freezed,Object? lyingOnRight = freezed,Object? sitting = freezed,Object? standing = freezed,Object? walking = freezed,}) {
  return _then(_self.copyWith(
lying: freezed == lying ? _self.lying : lying // ignore: cast_nullable_to_non_nullable
as int?,lyingOnBack: freezed == lyingOnBack ? _self.lyingOnBack : lyingOnBack // ignore: cast_nullable_to_non_nullable
as int?,lyingOnStomach: freezed == lyingOnStomach ? _self.lyingOnStomach : lyingOnStomach // ignore: cast_nullable_to_non_nullable
as int?,lyingOnLeft: freezed == lyingOnLeft ? _self.lyingOnLeft : lyingOnLeft // ignore: cast_nullable_to_non_nullable
as int?,lyingOnRight: freezed == lyingOnRight ? _self.lyingOnRight : lyingOnRight // ignore: cast_nullable_to_non_nullable
as int?,sitting: freezed == sitting ? _self.sitting : sitting // ignore: cast_nullable_to_non_nullable
as int?,standing: freezed == standing ? _self.standing : standing // ignore: cast_nullable_to_non_nullable
as int?,walking: freezed == walking ? _self.walking : walking // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ActivityDuration implements ActivityDuration {
  const _ActivityDuration({this.lying, this.lyingOnBack, this.lyingOnStomach, this.lyingOnLeft, this.lyingOnRight, this.sitting, this.standing, this.walking});
  factory _ActivityDuration.fromJson(Map<String, dynamic> json) => _$ActivityDurationFromJson(json);

@override final  int? lying;
@override final  int? lyingOnBack;
@override final  int? lyingOnStomach;
@override final  int? lyingOnLeft;
@override final  int? lyingOnRight;
@override final  int? sitting;
@override final  int? standing;
@override final  int? walking;

/// Create a copy of ActivityDuration
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ActivityDurationCopyWith<_ActivityDuration> get copyWith => __$ActivityDurationCopyWithImpl<_ActivityDuration>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ActivityDurationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ActivityDuration&&(identical(other.lying, lying) || other.lying == lying)&&(identical(other.lyingOnBack, lyingOnBack) || other.lyingOnBack == lyingOnBack)&&(identical(other.lyingOnStomach, lyingOnStomach) || other.lyingOnStomach == lyingOnStomach)&&(identical(other.lyingOnLeft, lyingOnLeft) || other.lyingOnLeft == lyingOnLeft)&&(identical(other.lyingOnRight, lyingOnRight) || other.lyingOnRight == lyingOnRight)&&(identical(other.sitting, sitting) || other.sitting == sitting)&&(identical(other.standing, standing) || other.standing == standing)&&(identical(other.walking, walking) || other.walking == walking));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lying,lyingOnBack,lyingOnStomach,lyingOnLeft,lyingOnRight,sitting,standing,walking);

@override
String toString() {
  return 'ActivityDuration(lying: $lying, lyingOnBack: $lyingOnBack, lyingOnStomach: $lyingOnStomach, lyingOnLeft: $lyingOnLeft, lyingOnRight: $lyingOnRight, sitting: $sitting, standing: $standing, walking: $walking)';
}


}

/// @nodoc
abstract mixin class _$ActivityDurationCopyWith<$Res> implements $ActivityDurationCopyWith<$Res> {
  factory _$ActivityDurationCopyWith(_ActivityDuration value, $Res Function(_ActivityDuration) _then) = __$ActivityDurationCopyWithImpl;
@override @useResult
$Res call({
 int? lying, int? lyingOnBack, int? lyingOnStomach, int? lyingOnLeft, int? lyingOnRight, int? sitting, int? standing, int? walking
});




}
/// @nodoc
class __$ActivityDurationCopyWithImpl<$Res>
    implements _$ActivityDurationCopyWith<$Res> {
  __$ActivityDurationCopyWithImpl(this._self, this._then);

  final _ActivityDuration _self;
  final $Res Function(_ActivityDuration) _then;

/// Create a copy of ActivityDuration
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lying = freezed,Object? lyingOnBack = freezed,Object? lyingOnStomach = freezed,Object? lyingOnLeft = freezed,Object? lyingOnRight = freezed,Object? sitting = freezed,Object? standing = freezed,Object? walking = freezed,}) {
  return _then(_ActivityDuration(
lying: freezed == lying ? _self.lying : lying // ignore: cast_nullable_to_non_nullable
as int?,lyingOnBack: freezed == lyingOnBack ? _self.lyingOnBack : lyingOnBack // ignore: cast_nullable_to_non_nullable
as int?,lyingOnStomach: freezed == lyingOnStomach ? _self.lyingOnStomach : lyingOnStomach // ignore: cast_nullable_to_non_nullable
as int?,lyingOnLeft: freezed == lyingOnLeft ? _self.lyingOnLeft : lyingOnLeft // ignore: cast_nullable_to_non_nullable
as int?,lyingOnRight: freezed == lyingOnRight ? _self.lyingOnRight : lyingOnRight // ignore: cast_nullable_to_non_nullable
as int?,sitting: freezed == sitting ? _self.sitting : sitting // ignore: cast_nullable_to_non_nullable
as int?,standing: freezed == standing ? _self.standing : standing // ignore: cast_nullable_to_non_nullable
as int?,walking: freezed == walking ? _self.walking : walking // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$Energy {

 int? get lying; int? get sitting; int? get standing; int? get walking; int? get total;
/// Create a copy of Energy
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EnergyCopyWith<Energy> get copyWith => _$EnergyCopyWithImpl<Energy>(this as Energy, _$identity);

  /// Serializes this Energy to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Energy&&(identical(other.lying, lying) || other.lying == lying)&&(identical(other.sitting, sitting) || other.sitting == sitting)&&(identical(other.standing, standing) || other.standing == standing)&&(identical(other.walking, walking) || other.walking == walking)&&(identical(other.total, total) || other.total == total));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lying,sitting,standing,walking,total);

@override
String toString() {
  return 'Energy(lying: $lying, sitting: $sitting, standing: $standing, walking: $walking, total: $total)';
}


}

/// @nodoc
abstract mixin class $EnergyCopyWith<$Res>  {
  factory $EnergyCopyWith(Energy value, $Res Function(Energy) _then) = _$EnergyCopyWithImpl;
@useResult
$Res call({
 int? lying, int? sitting, int? standing, int? walking, int? total
});




}
/// @nodoc
class _$EnergyCopyWithImpl<$Res>
    implements $EnergyCopyWith<$Res> {
  _$EnergyCopyWithImpl(this._self, this._then);

  final Energy _self;
  final $Res Function(Energy) _then;

/// Create a copy of Energy
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? lying = freezed,Object? sitting = freezed,Object? standing = freezed,Object? walking = freezed,Object? total = freezed,}) {
  return _then(_self.copyWith(
lying: freezed == lying ? _self.lying : lying // ignore: cast_nullable_to_non_nullable
as int?,sitting: freezed == sitting ? _self.sitting : sitting // ignore: cast_nullable_to_non_nullable
as int?,standing: freezed == standing ? _self.standing : standing // ignore: cast_nullable_to_non_nullable
as int?,walking: freezed == walking ? _self.walking : walking // ignore: cast_nullable_to_non_nullable
as int?,total: freezed == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Energy implements Energy {
  const _Energy({this.lying, this.sitting, this.standing, this.walking, this.total});
  factory _Energy.fromJson(Map<String, dynamic> json) => _$EnergyFromJson(json);

@override final  int? lying;
@override final  int? sitting;
@override final  int? standing;
@override final  int? walking;
@override final  int? total;

/// Create a copy of Energy
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EnergyCopyWith<_Energy> get copyWith => __$EnergyCopyWithImpl<_Energy>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EnergyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Energy&&(identical(other.lying, lying) || other.lying == lying)&&(identical(other.sitting, sitting) || other.sitting == sitting)&&(identical(other.standing, standing) || other.standing == standing)&&(identical(other.walking, walking) || other.walking == walking)&&(identical(other.total, total) || other.total == total));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,lying,sitting,standing,walking,total);

@override
String toString() {
  return 'Energy(lying: $lying, sitting: $sitting, standing: $standing, walking: $walking, total: $total)';
}


}

/// @nodoc
abstract mixin class _$EnergyCopyWith<$Res> implements $EnergyCopyWith<$Res> {
  factory _$EnergyCopyWith(_Energy value, $Res Function(_Energy) _then) = __$EnergyCopyWithImpl;
@override @useResult
$Res call({
 int? lying, int? sitting, int? standing, int? walking, int? total
});




}
/// @nodoc
class __$EnergyCopyWithImpl<$Res>
    implements _$EnergyCopyWith<$Res> {
  __$EnergyCopyWithImpl(this._self, this._then);

  final _Energy _self;
  final $Res Function(_Energy) _then;

/// Create a copy of Energy
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? lying = freezed,Object? sitting = freezed,Object? standing = freezed,Object? walking = freezed,Object? total = freezed,}) {
  return _then(_Energy(
lying: freezed == lying ? _self.lying : lying // ignore: cast_nullable_to_non_nullable
as int?,sitting: freezed == sitting ? _self.sitting : sitting // ignore: cast_nullable_to_non_nullable
as int?,standing: freezed == standing ? _self.standing : standing // ignore: cast_nullable_to_non_nullable
as int?,walking: freezed == walking ? _self.walking : walking // ignore: cast_nullable_to_non_nullable
as int?,total: freezed == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
