import 'package:ai_vanse/screens/dietary/controller/dietary_controller.dart';
import 'package:ai_vanse/screens/home/<USER>/home_controller.dart';
import 'package:ai_vanse/screens/home/<USER>/home_summary_controller.dart';
import 'package:ai_vanse/screens/setting/controllers/setting_controller.dart';
import 'package:get/get.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HomeController>(
      () => HomeController(),
    );
    Get.lazyPut<SettingController>(SettingController.new);
    Get.lazyPut(HomeSummaryController.new);
    Get.lazyPut(DietaryController.new);
  }
}
