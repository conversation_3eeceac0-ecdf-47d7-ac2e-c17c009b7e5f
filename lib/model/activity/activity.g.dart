// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Activity _$ActivityFromJson(Map<String, dynamic> json) => _Activity(
  energy:
      json['energy'] == null
          ? null
          : Energy.fromJson(json['energy'] as Map<String, dynamic>),
  duration:
      json['duration'] == null
          ? null
          : ActivityDuration.fromJson(json['duration'] as Map<String, dynamic>),
  steps: (json['steps'] as num?)?.toInt(),
  distance: (json['distance'] as num?)?.toInt(),
  createdDate:
      json['createdDate'] == null
          ? null
          : DateTime.parse(json['createdDate'] as String),
);

Map<String, dynamic> _$ActivityToJson(_Activity instance) => <String, dynamic>{
  'energy': instance.energy,
  'duration': instance.duration,
  'steps': instance.steps,
  'distance': instance.distance,
  'createdDate': instance.createdDate?.toIso8601String(),
};

_ActivityDuration _$ActivityDurationFromJson(Map<String, dynamic> json) =>
    _ActivityDuration(
      lying: (json['lying'] as num?)?.toInt(),
      lyingOnBack: (json['lyingOnBack'] as num?)?.toInt(),
      lyingOnStomach: (json['lyingOnStomach'] as num?)?.toInt(),
      lyingOnLeft: (json['lyingOnLeft'] as num?)?.toInt(),
      lyingOnRight: (json['lyingOnRight'] as num?)?.toInt(),
      sitting: (json['sitting'] as num?)?.toInt(),
      standing: (json['standing'] as num?)?.toInt(),
      walking: (json['walking'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ActivityDurationToJson(_ActivityDuration instance) =>
    <String, dynamic>{
      'lying': instance.lying,
      'lyingOnBack': instance.lyingOnBack,
      'lyingOnStomach': instance.lyingOnStomach,
      'lyingOnLeft': instance.lyingOnLeft,
      'lyingOnRight': instance.lyingOnRight,
      'sitting': instance.sitting,
      'standing': instance.standing,
      'walking': instance.walking,
    };

_Energy _$EnergyFromJson(Map<String, dynamic> json) => _Energy(
  lying: (json['lying'] as num?)?.toInt(),
  sitting: (json['sitting'] as num?)?.toInt(),
  standing: (json['standing'] as num?)?.toInt(),
  walking: (json['walking'] as num?)?.toInt(),
  total: (json['total'] as num?)?.toInt(),
);

Map<String, dynamic> _$EnergyToJson(_Energy instance) => <String, dynamic>{
  'lying': instance.lying,
  'sitting': instance.sitting,
  'standing': instance.standing,
  'walking': instance.walking,
  'total': instance.total,
};
