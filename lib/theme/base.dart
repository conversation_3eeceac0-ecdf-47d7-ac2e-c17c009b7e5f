import 'package:ai_vanse/extensions/colors_extra_extension.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

abstract class AppTheme {
  Color get primary;

  Color get primaryContainer;

  Color get onPrimary;

  Color get secondary;

  Color get onSecondary;

  Color get tertiary;

  Color get onTertiary;

  Color get shadow;

  ThemeData get baseThemeData;

  ExtraColors get extraColor;

  Brightness get brightness;

  late final ColorScheme colorScheme = baseThemeData.colorScheme.copyWith(
    brightness: brightness,
    primary: primary,
    onPrimary: onPrimary,
    primaryContainer: primaryContainer,
    secondary: secondary,
    onSecondary: onSecondary,
    shadow: shadow,
    tertiary: tertiary,
    onTertiary: onTertiary,
  );

  ThemeData themeData() {
    return ThemeData(
      useMaterial3: true,
      fontFamily: GoogleFonts.prompt().fontFamily,
      primaryColor: primary,
      colorScheme: colorScheme,
      extensions: [extraColor],
      appBarTheme: AppBarTheme(
        centerTitle: true,
        backgroundColor: colorScheme.primaryContainer,
        surfaceTintColor: colorScheme.primaryContainer,
      ),
      buttonTheme: ButtonThemeData(disabledColor: extraColor.divider),
      tabBarTheme: TabBarThemeData(
        indicatorColor: tertiary,
        indicatorSize: TabBarIndicatorSize.tab,
        labelColor: tertiary,
        unselectedLabelColor: extraColor.unselected,
        labelStyle: AppTextStyle.bodyBold.textStyle,
        unselectedLabelStyle: AppTextStyle.bodyText.textStyle,
      ),
      dividerColor: extraColor.slate.c100,
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: onPrimary,
        selectedItemColor: primary,
        unselectedItemColor: Colors.grey.shade400,
        showSelectedLabels: true,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
      ),
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: <TargetPlatform, PageTransitionsBuilder>{
          TargetPlatform.android: PredictiveBackPageTransitionsBuilder(),
        },
      ),
    );
  }
}
