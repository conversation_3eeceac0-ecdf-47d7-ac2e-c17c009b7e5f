import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';

class RaderPlot extends StatefulWidget {
  final List<Offset> dataPoints;
  final int? intervals;

  const RaderPlot({super.key, required this.dataPoints, this.intervals});

  @override
  State<RaderPlot> createState() => _RaderPlotState();
}

class _RaderPlotState extends State<RaderPlot> {
  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.of(context).size.width;
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: ShapeDecoration(
        shape: CircleBorder(side: BorderSide(color: Colors.grey, width: 1)),
      ),
      width: deviceWidth * 0.75,
      height: deviceWidth * 0.75,
      child: CustomPaint(
        painter: Ra<PERSON><PERSON>hartPainter(widget.dataPoints, widget.intervals),
      ),
    );
  }
}

class RaderChartPainter extends CustomPainter {
  int _intervals = 4;
  List<Offset> _dataPoints = [];

  RaderChartPainter(List<Offset> dataPoints, int? intervals) {
    _dataPoints = dataPoints;
    if (intervals != null) {
      _intervals = intervals;
    }
  }

  // Draw labels
  final textPainter = TextPainter(
    textAlign: TextAlign.center,
    textDirection: TextDirection.ltr,
  );

  // Function to draw text
  void drawText(
    Canvas canvas,
    String text,
    double x,
    double y,
    Size canvasSize,
  ) {
    textPainter.text = TextSpan(
      text: text,
      style: TextStyle(color: Colors.black, fontSize: 12),
    );
    textPainter.layout();
    double dx = clampDouble(
      x - textPainter.width / 2,
      0,
      canvasSize.width - textPainter.width,
    );
    double dy = clampDouble(
      y - textPainter.height / 2,
      0,
      canvasSize.height - textPainter.height,
    );
    textPainter.paint(canvas, Offset(dx, dy));
  }

  Offset clampToCircle(Offset point, Size size) {
    Offset center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final dx = point.dx - center.dx;
    final dy = point.dy - center.dy;
    final distance = sqrt(dx * dx + dy * dy);
    if (distance > radius) {
      final angle = atan2(dy, dx);
      return Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );
    }
    return point;
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.grey
          ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    final dataPaint =
        Paint()
          ..color = Colors.blue.withOpacity(0.5)
          ..style = PaintingStyle.fill;

    for (var point in _dataPoints) {
      Offset dt = Offset(
        center.dx - point.dx * radius,
        center.dy - point.dy * radius,
      );
      dt = clampToCircle(dt, size);
      canvas.drawCircle(dt, 6, dataPaint);
    }

    canvas.drawLine(
      Offset(center.dx, 0),
      Offset(center.dx, size.height),
      paint,
    );

    canvas.drawLine(Offset(0, center.dy), Offset(size.width, center.dy), paint);

    for (var i = 1; i <= _intervals; i++) {
      canvas.drawCircle(center, radius / _intervals * i, paint);
      drawText(
        canvas,
        'G$i',
        center.dx,
        center.dy + radius / _intervals * i,
        size,
      );
    }

    drawText(canvas, 'AP', center.dx, center.dy - radius, size);
    drawText(canvas, 'ML', center.dx + radius, center.dy, size);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
