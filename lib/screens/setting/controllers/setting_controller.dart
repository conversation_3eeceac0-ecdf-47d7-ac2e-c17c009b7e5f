import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/repositories/user_repository.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/screens/setting/models/setting_menu_item.dart';
import 'package:ai_vanse/services/dialog_service.dart';
import 'package:ai_vanse/services/identifier_service.dart';
import 'package:get/get.dart';

class SettingController extends GetxController {
  late final UserRepository _userRepository = Get.find();
  late final IdentifierService _identifierService = Get.find();

  String get version =>
      '${_identifierService.version} (${_identifierService.buildNumber})';

  late final List<SettingMenuItem> menuList = [
    SettingMenuItem(
      title: 'language',
      icon: Assets.svgs.icoRoundedCheckbox.path,
      onTap: () {
        Get.toNamed(AppRoutes.languageSetting);
      },
    ),
    SettingMenuItem(
      title: 'setting_menu_terms_and_conditions',
      icon: Assets.svgs.icoRoundedCheckbox.path,
      onTap: () {
        Get.toNamed(
          AppRoutes.web,
          arguments: {
            'title': 'setting_menu_terms_and_conditions'.tr,
            'uri': Uri.parse('https://dev.airpresense.tech/term-of-use'),
          },
        );
      },
    ),
    SettingMenuItem(
      title: 'setting_menu_logout',
      icon: Assets.svgs.icoRoundedLogout.path,
      onTap: logout,
    ),
  ];

  Future<void> logout() async {
    final result = await Get.find<DialogService>().showMessageDialog(
      title: 'common_cta_confirm'.tr,
      message: 'setting_menu_logout_confirmation'.tr,
      okLabel: 'setting_menu_logout'.tr,
      cancelLabel: 'common_cta_cancel'.tr,
    );

    if (result == OkCancelResult.ok) {
      await _userRepository.logout();
      Get.offAndToNamed(AppRoutes.login);
    }
  }
}
