import 'package:flutter/material.dart';

extension ContextExtension on BuildContext {
  Size get screenSize => MediaQuery.of(this).size;

  FocusScopeNode get currentFocus => FocusScope.of(this);

  void hideKeyboard({microTask = false}) {
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      if (microTask) {
        Future.microtask(() => FocusManager.instance.primaryFocus!.unfocus());
      } else {
        FocusManager.instance.primaryFocus!.unfocus();
      }
    }
  }

  double get safeAreaTopSize => MediaQuery.of(this).padding.top;

  EdgeInsets get safeAreaBottomPadding =>
      EdgeInsets.only(bottom: MediaQuery.of(this).padding.bottom);

  EdgeInsets get safeAreaWithoutTop =>
      MediaQuery.of(this).padding.copyWith(top: 0);

  EdgeInsets get viewInsetsBottom =>
      EdgeInsets.only(bottom: MediaQuery.of(this).viewInsets.bottom);

  EdgeInsets get viewInsets => MediaQuery.of(this).viewInsets;
}
