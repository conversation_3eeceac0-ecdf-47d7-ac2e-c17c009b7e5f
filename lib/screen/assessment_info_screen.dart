// import 'package:ai_vanse/widgets/main_content_container.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_localizations/flutter_localizations.dart';
//
// class AssessmentInfoScreen extends StatefulWidget {
//   final String? title;
//   final VoidCallback? onNext;
//   final List<Map<String, dynamic>>? steps;
//
//   const AssessmentInfoScreen({super.key, this.title, this.steps, this.onNext});
//
//   @override
//   State<AssessmentInfoScreen> createState() => _AssessmentInfoScreenState();
// }
//
// class _AssessmentInfoScreenState extends State<AssessmentInfoScreen> {
//   Widget buildStepTile(Icon icon, String text) {
//     return Column(
//       children: [
//         Divider(),
//         Row(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             icon,
//             VerticalDivider(),
//             Expanded(
//               child: Text(text, style: Theme.of(context).textTheme.bodyMedium),
//             ),
//           ],
//         ),
//       ],
//     );
//   }
//
//   Widget buildAssessmentSteps(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
//       child: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Center(
//               child: Text(
//                 widget.title ?? 'Assessment Info',
//                 textAlign: TextAlign.center,
//                 style: Theme.of(context).textTheme.headlineMedium,
//               ),
//             ),
//             SizedBox(height: 20),
//             Text(
//               AppLocalizations.of(context)!.instructions,
//               style: Theme.of(context).textTheme.titleLarge,
//             ),
//             ...?widget.steps?.map(
//               (step) => buildStepTile(step['icon'], step['text']),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget buildStartAssessmentButton(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
//       child: ElevatedButton(
//         onPressed: widget.onNext,
//         child: Container(
//           height: 50.0,
//           alignment: Alignment.center,
//           child: Text(
//             AppLocalizations.of(context)!.ok,
//             style: TextStyle(fontSize: 20),
//           ),
//         ),
//       ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return MainContentContainer(
//       child: Column(
//         children: [
//           Expanded(child: buildAssessmentSteps(context)),
//           Divider(),
//           buildStartAssessmentButton(context),
//         ],
//       ),
//     );
//   }
// }
