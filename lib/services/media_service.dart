import 'package:ai_vanse/services/permission_service.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_picker_android/image_picker_android.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';

class MediaService extends GetxService {
  late final GalleryPermissionService _galleryPermissionHelper = Get.find();

  late final ImagePicker _imagePicker = ImagePicker();

  Future<XFile?> pickImage({bool isSquareCrop = false}) {
    final ImagePickerPlatform imagePickerImplementation =
        ImagePickerPlatform.instance;
    if (imagePickerImplementation is ImagePickerAndroid) {
      imagePickerImplementation.useAndroidPhotoPicker = true;
    }

    return _imagePicker.pickImage(source: ImageSource.gallery).onError((_, __) {
      _galleryPermissionHelper.showNoPermissionDialog();
      return null;
    });
  }
}
