import 'package:ai_vanse/constants/preference.dart';
import 'package:ai_vanse/extensions/string_extension.dart';
import 'package:ai_vanse/l10n/translate.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class PreferenceService extends GetxService {
  late final GetStorage _box = GetStorage();

  // final FlutterSecureStorage _storage;

  final RxSet<String> subscribePatientIds = RxSet<String>({});

  AppTranslationLanguage? get appLanguage {
    final appLanguage = _box.read<String>(PreferenceKey.appLanguage);
    if (appLanguage.isNullOrEmpty) {
      return null;
    }

    return AppTranslationLanguage.values.byName(appLanguage!);
  }

  set appLanguage(AppTranslationLanguage? language) {
    _box.write(PreferenceKey.appLanguage, language?.name);
  }

  Future<void> clearLoginInfo() async {}
}
