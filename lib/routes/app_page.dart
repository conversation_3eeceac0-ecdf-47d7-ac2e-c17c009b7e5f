import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/screens/home/<USER>/home_binding.dart';
import 'package:ai_vanse/screens/home/<USER>/food_choice_detail_page.dart';
import 'package:ai_vanse/screens/home/<USER>/home_page.dart';
import 'package:ai_vanse/screens/login/binding/login_binding.dart';
import 'package:ai_vanse/screens/login/views/login_page.dart';
import 'package:ai_vanse/screens/setting/bindings/setting_binding.dart';
import 'package:ai_vanse/screens/setting/views/language_setting_page.dart';
import 'package:ai_vanse/screens/setting/views/setting_page.dart';
import 'package:ai_vanse/screens/splash/binding/splash_binding.dart';
import 'package:ai_vanse/screens/splash/views/splash_page.dart';
import 'package:ai_vanse/screens/user_info/bindings/user_info_binding.dart';
import 'package:ai_vanse/screens/user_info/views/user_info_page.dart';
import 'package:ai_vanse/screens/web_view/binding/web_view_binding.dart';
import 'package:ai_vanse/screens/web_view/view/web_view_page.dart';
import 'package:get/get.dart';

final List<GetPage> pages = [
  GetPage(
    name: AppRoutes.welcome,
    page: () => const SplashPage(),
    binding: SplashBinding(),
    transition: Transition.fadeIn,
  ),
  GetPage(
    name: AppRoutes.home,
    page: () => const HomePage(),
    bindings: [HomeBinding(), UserInfoBinding()],
    transition: Transition.fadeIn,
    transitionDuration: const Duration(milliseconds: 800),
  ),

  GetPage(
    name: AppRoutes.login,
    page: () => const LoginPage(),
    binding: LoginBinding(),
    transition: Transition.fadeIn,
    transitionDuration: const Duration(milliseconds: 800),
  ),
  GetPage(
    name: AppRoutes.web,
    page: () => const WebViewPage(),
    binding: WebViewBinding(),
  ),
  GetPage(
    name: AppRoutes.userInfo,
    page: () => const UserInfoPage(),
    binding: UserInfoBinding(),
  ),
  GetPage(
    name: AppRoutes.setting,
    page: () => const SettingPage(),
    binding: SettingBinding(),
  ),
  GetPage(name: AppRoutes.foodChoiceDetail, page: () => FoodChoiceDetailPage()),
  GetPage(
    name: AppRoutes.languageSetting,
    page: () => LanguageSettingPage(),
    binding: SettingBinding(),
  ),
];
