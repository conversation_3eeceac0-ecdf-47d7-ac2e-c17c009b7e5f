const Map<String, String> sensorServiceUuidIndexMap = {
  '47442014-0F63-5B27-9122-************': 'bsnIoT',
  '47442015-0F63-5B27-9122-************': 'bsnIoTCharAccel',
  '47442016-0F63-5B27-9122-************': 'bsnIoTCharGyro',
  '47442017-0F63-5B27-9122-************': 'bsnIoTCharMag',
  '47442018-0F63-5B27-9122-************': 'bsnIoTCharTemp',
  '47442019-0F63-5B27-9122-************': 'bsnIoTCharHumidity',
  '4744201A-0F63-5B27-9122-************': 'bsnIoTCharLed',
  '4744201B-0F63-5B27-9122-************': 'bsnIoTCharScreentimeMessage',
  '4744201C-0F63-5B27-9122-************': 'bsnIoTCharIbeacon',
  '4744201D-0F63-5B27-9122-************': 'bsnIoTCharDust',
  '4744201E-0F63-5B27-9122-************': 'bsnIoTCharPpg',
  '4744201F-0F63-5B27-9122-************': 'bsnIoTCharTouch',
  '47442020-0F63-5B27-9122-************': 'bsnIoTCharImu',
  '47442021-0F63-5B27-9122-************': 'bsnIoTCharSamplingFreq',
  '47442022-0F63-5B27-9122-************': 'bsnIoTCharSamplingFreqRead',
  '47442023-0F63-5B27-9122-************': 'bsnIoTCharReset',
  '47442024-0F63-5B27-9122-************': 'bsnIoTCharImuPwrMode',
  '47442025-0F63-5B27-9122-************': 'bsnIoTCharAdvTimeInterval',
  '47442026-0F63-5B27-9122-************': 'bsnIoTCharBattery',
  '47442027-0F63-5B27-9122-************': 'bsnIoTCharWriteToFlash',
  '47442028-0F63-5B27-9122-************': 'bsnIoTCharReadFromFlash',
  '47442029-0F63-5B27-9122-************': 'bsnIoTCharBarometer',
  '4744202A-0F63-5B27-9122-************': 'bsnIoTCharMultiTouch',
  '00002a29-0000-1000-8000-00805f9b34fb': 'bsnIoTCharManufacturer',
};

const Map<String, String> sensorServiceUuidMap = {
  'bsnIoT': '47442014-0F63-5B27-9122-************',
  'bsnIoTCharAccel': '47442015-0F63-5B27-9122-************',
  'bsnIoTCharGyro': '47442016-0F63-5B27-9122-************',
  'bsnIoTCharMag': '47442017-0F63-5B27-9122-************',
  'bsnIoTCharTemp': '47442018-0F63-5B27-9122-************',
  'bsnIoTCharHumidity': '47442019-0F63-5B27-9122-************',
  'bsnIoTCharLed': '4744201A-0F63-5B27-9122-************',
  'bsnIoTCharScreentimeMessage': '4744201B-0F63-5B27-9122-************',
  'bsnIoTCharIbeacon': '4744201C-0F63-5B27-9122-************',
  'bsnIoTCharDust': '4744201D-0F63-5B27-9122-************',
  'bsnIoTCharPpg': '4744201E-0F63-5B27-9122-************',
  'bsnIoTCharTouch': '4744201F-0F63-5B27-9122-************',
  'bsnIoTCharImu': '47442020-0F63-5B27-9122-************',
  'bsnIoTCharSamplingFreq': '47442021-0F63-5B27-9122-************',
  'bsnIoTCharSamplingFreqRead': '47442022-0F63-5B27-9122-************',
  'bsnIoTCharReset': '47442023-0F63-5B27-9122-************',
  'bsnIoTCharImuPwrMode': '47442024-0F63-5B27-9122-************',
  'bsnIoTCharAdvTimeInterval': '47442025-0F63-5B27-9122-************',
  'bsnIoTCharBattery': '47442026-0F63-5B27-9122-************',
  'bsnIoTCharWriteToFlash': '47442027-0F63-5B27-9122-************',
  'bsnIoTCharReadFromFlash': '47442028-0F63-5B27-9122-************',
  'bsnIoTCharBarometer': '47442029-0F63-5B27-9122-************',
  'bsnIoTCharMultiTouch': '4744202A-0F63-5B27-9122-************',
  'bsnIoTCharManufacturer': '00002a29-0000-1000-8000-00805f9b34fb',
};

const List<String> sensorPlatformNameList = ['e-AR2016'];
