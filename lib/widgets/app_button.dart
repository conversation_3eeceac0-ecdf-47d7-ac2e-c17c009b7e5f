import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

enum AppButtonColor { primary, secondary, linePrimary, white }

class AppButton extends StatelessWidget {
  final void Function()? onPressed;
  final String label;
  final Widget? icon;
  final EdgeInsetsGeometry? padding;
  final AppButtonColor? appButtonColor;
  final AppTextStyle appTextStyle;
  final double? borderRadius;

  const AppButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.icon,
    this.padding,
    this.appButtonColor = AppButtonColor.primary,
    this.appTextStyle = AppTextStyle.bodyText,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Color backgroundColor = theme.colorScheme.primary;
    // Color splashColor = theme.colorScheme.secondary;
    Color foregroundColor = theme.colorScheme.onPrimary;
    switch (appButtonColor) {
      case AppButtonColor.secondary:
        backgroundColor = theme.colorScheme.secondary;
        // splashColor = theme.colorScheme.primary;
        foregroundColor = theme.colorScheme.onSecondary;
        break;
      case AppButtonColor.white:
        backgroundColor = Colors.white;
      // splashColor = theme.colorScheme.primary;
      // foregroundColor = theme.colorScheme.onSecondary;
      default:
    }

    return ElevatedButton(
      onPressed: onPressed,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return context.theme.extraColors.divider;
          }
          return backgroundColor;
        }),
        foregroundColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return context.theme.extraColors.hint;
          }
          return foregroundColor;
        }),
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 32.sp),
          ),
        ),
      ).copyWith(
        overlayColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.pressed)) {
            return backgroundColor;
          }
          return null;
        }),
      ),
      child: Padding(
        padding:
            padding ?? EdgeInsets.symmetric(vertical: 10.sp, horizontal: 16.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null)
              Padding(padding: EdgeInsets.only(right: 8.sp), child: icon!),
            AppText(label, appTextStyle: appTextStyle, color: foregroundColor),
          ],
        ),
      ),
    );
  }
}
