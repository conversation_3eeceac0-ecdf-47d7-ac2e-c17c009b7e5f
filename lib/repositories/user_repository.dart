import 'package:ai_vanse/api/handler/login_error_handler.dart';
import 'package:ai_vanse/api/services/auth_api.dart';
import 'package:ai_vanse/extensions/response_extension.dart';
import 'package:ai_vanse/model/patient/patient.dart';
import 'package:ai_vanse/model/token/token.dart';
import 'package:ai_vanse/services/preference_service.dart';
import 'package:ai_vanse/services/token_service.dart';
import 'package:get/get.dart';

class UserRepository extends GetxService {
  late final TokenService _tokenService = Get.find();
  late final AuthApi _authApi = Get.find();
  late final PreferenceService _preferenceService = Get.find();

  Future<Token> get token async => (await _tokenService.getToken())!;

  Future<int> get patientId async =>
      (await _tokenService.getToken())!.patientId;

  Future<int> get userId async => (await _tokenService.getToken())!.userId;

  Patient? _patient;

  Patient get patient => _patient!;

  Future<bool> get hasToken async =>
      (await _tokenService.getToken())?.token != null;

  Future<void> login(String username, String password) {
    return _authApi
        .login({'username': username, 'password': password})
        .toResponse(handler: LoginErrorHandler())
        .then(_setToken);
  }

  Future<void> _setToken(Token token) {
    return _tokenService.setToken(token);
  }

  Future<void> logout() {
    _patient = null;
    return _tokenService.deleteToken();
  }
}
