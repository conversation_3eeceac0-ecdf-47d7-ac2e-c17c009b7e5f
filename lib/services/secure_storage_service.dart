import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class SecureStorageService extends GetxService {
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  Future<void> set(String key, String value) {
    return _storage.write(key: key, value: value);
  }

  Future<void> setMap(Map<String, String> map) {
    return Future.forEach(
      map.entries,
      (entry) => _storage.write(key: entry.key, value: entry.value),
    );
  }

  Future<String?> get(String key) {
    return _storage.read(key: key);
  }

  Future<Map<String, String?>> getMap(List<String> keys) {
    final Map<String, String?> result = {};
    return Future.forEach(
      keys,
      (key) => _storage.read(key: key).then((value) {
        result[key] = value;
      }),
    ).then((_) => result);
  }

  Future<void> setSecureStorage(String key, String? value) {
    return _storage.write(key: key, value: value);
  }

  Future<void> deleteSecureStorage(String key) {
    return _storage.delete(key: key);
  }

  Future<String?> getSecureStorage(String key) {
    return _storage.read(key: key);
  }

  Future<bool> getSecureStorageBoolean(String key) async {
    return (await getSecureStorage(key) ?? 'false').toLowerCase() == 'true';
  }

  Future<void> delete(String key) {
    return _storage.delete(key: key);
  }

  Future<void> deleteAll() {
    return _storage.deleteAll();
  }
}
