// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'success_response_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SuccessResponseDto {

 String? get success;
/// Create a copy of SuccessResponseDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SuccessResponseDtoCopyWith<SuccessResponseDto> get copyWith => _$SuccessResponseDtoCopyWithImpl<SuccessResponseDto>(this as SuccessResponseDto, _$identity);

  /// Serializes this SuccessResponseDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SuccessResponseDto&&(identical(other.success, success) || other.success == success));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success);

@override
String toString() {
  return 'SuccessResponseDto(success: $success)';
}


}

/// @nodoc
abstract mixin class $SuccessResponseDtoCopyWith<$Res>  {
  factory $SuccessResponseDtoCopyWith(SuccessResponseDto value, $Res Function(SuccessResponseDto) _then) = _$SuccessResponseDtoCopyWithImpl;
@useResult
$Res call({
 String? success
});




}
/// @nodoc
class _$SuccessResponseDtoCopyWithImpl<$Res>
    implements $SuccessResponseDtoCopyWith<$Res> {
  _$SuccessResponseDtoCopyWithImpl(this._self, this._then);

  final SuccessResponseDto _self;
  final $Res Function(SuccessResponseDto) _then;

/// Create a copy of SuccessResponseDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = freezed,}) {
  return _then(_self.copyWith(
success: freezed == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _SuccessResponseDto implements SuccessResponseDto {
  const _SuccessResponseDto({this.success});
  factory _SuccessResponseDto.fromJson(Map<String, dynamic> json) => _$SuccessResponseDtoFromJson(json);

@override final  String? success;

/// Create a copy of SuccessResponseDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SuccessResponseDtoCopyWith<_SuccessResponseDto> get copyWith => __$SuccessResponseDtoCopyWithImpl<_SuccessResponseDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SuccessResponseDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SuccessResponseDto&&(identical(other.success, success) || other.success == success));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success);

@override
String toString() {
  return 'SuccessResponseDto(success: $success)';
}


}

/// @nodoc
abstract mixin class _$SuccessResponseDtoCopyWith<$Res> implements $SuccessResponseDtoCopyWith<$Res> {
  factory _$SuccessResponseDtoCopyWith(_SuccessResponseDto value, $Res Function(_SuccessResponseDto) _then) = __$SuccessResponseDtoCopyWithImpl;
@override @useResult
$Res call({
 String? success
});




}
/// @nodoc
class __$SuccessResponseDtoCopyWithImpl<$Res>
    implements _$SuccessResponseDtoCopyWith<$Res> {
  __$SuccessResponseDtoCopyWithImpl(this._self, this._then);

  final _SuccessResponseDto _self;
  final $Res Function(_SuccessResponseDto) _then;

/// Create a copy of SuccessResponseDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = freezed,}) {
  return _then(_SuccessResponseDto(
success: freezed == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
