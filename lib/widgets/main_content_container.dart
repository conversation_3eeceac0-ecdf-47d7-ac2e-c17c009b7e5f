import 'package:flutter/material.dart';

class MainContentContainer extends StatefulWidget {
  final Widget child;

  const MainContentContainer({super.key, required this.child});

  @override
  State<MainContentContainer> createState() => _MainContentContainerState();
}

class _MainContentContainerState extends State<MainContentContainer> {
  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 2.0,
            spreadRadius: 1.0,
            offset: Offset(1.0, 1.0),
          ),
        ],
      ),
      child: widget.child,
    );
  }
}
