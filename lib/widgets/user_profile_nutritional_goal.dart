// import 'package:ai_vanse/models/user_profile_service.dart';
// import 'package:ai_vanse/utilities/event_bus.dart';
// import 'package:ai_vanse/utilities/events.dart';
// import 'package:flutter/material.dart';
//
// class NutritionalGoalsScreen extends StatefulWidget {
//   const NutritionalGoalsScreen({super.key});
//
//   @override
//   State<NutritionalGoalsScreen> createState() => _NutritionalGoalsScreenState();
// }
//
// class _NutritionalGoalsScreenState extends State<NutritionalGoalsScreen> {
//   bool hasChanges = false;
//   double calorieLimit = 2000;
//   double calorieMax = 5000;
//   double fatLimit = 70; // in grams
//   double fatMax = 200;
//   double carbLimit = 300; // in grams
//   double carbMax = 2000;
//   double proteinLimit = 50; // in grams
//   double proteinMax = 300;
//   double fiberLimit = 30; // in grams
//   double fiberMax = 200;
//   double waterIntakeGoal = 2000; // in ml
//   double waterMax = 3000;
//   double sugarIntakeGoal = 5; // in grams
//   double sugarMax = 100;
//   double saltIntakeGoal = 6; // in grams
//   double saltMax = 100;
//
//   @override
//   void initState() {
//     super.initState();
//     _loadNutritionalGoals();
//   }
//
//   void _loadNutritionalGoals() {
//     var nutritionalGoals = UserProfileService().getNutritionalGoals();
//     setState(() {
//       calorieLimit = nutritionalGoals['calorie_limit'];
//       fatLimit = nutritionalGoals['fat_limit'];
//       carbLimit = nutritionalGoals['carb_limit'];
//       proteinLimit = nutritionalGoals['protein_limit'];
//       fiberLimit = nutritionalGoals['fiber_limit'];
//       waterIntakeGoal = nutritionalGoals['water_intake_goal'];
//       sugarIntakeGoal = nutritionalGoals['sugar_intake_goal'];
//       saltIntakeGoal = nutritionalGoals['salt_intake_goal'];
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(AppLocalizations.of(context)!.nutritionalGoals),
//         backgroundColor: Colors.teal,
//       ),
//       body: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: ListView(
//           children: [
//             buildGoalField(
//               AppLocalizations.of(context)!.calorieLimit,
//               calorieLimit,
//               calorieMax,
//               Icons.local_fire_department,
//             ),
//             buildGoalField(
//               AppLocalizations.of(context)!.fatLimit,
//               fatLimit,
//               fatMax,
//               Icons.egg_outlined,
//             ),
//             buildGoalField(
//               AppLocalizations.of(context)!.carbLimit,
//               carbLimit,
//               carbMax,
//               Icons.rice_bowl,
//             ),
//             buildGoalField(
//               AppLocalizations.of(context)!.proteinLimit,
//               proteinLimit,
//               proteinMax,
//               Icons.fitness_center,
//             ),
//             buildGoalField(
//               AppLocalizations.of(context)!.fiberLimit,
//               fiberLimit,
//               fiberMax,
//               Icons.leak_add,
//             ),
//             // buildGoalField(
//             //   AppLocalizations.of(context)!.waterIntakeGoal,
//             //   waterIntakeGoal,
//             //   Icons.local_drink,
//             // ),
//             buildGoalField(
//               AppLocalizations.of(context)!.sugarIntakeGoal,
//               sugarIntakeGoal,
//               sugarMax,
//               Icons.crop_square,
//             ),
//             buildGoalField(
//               AppLocalizations.of(context)!.saltIntakeGoal,
//               saltIntakeGoal,
//               saltMax,
//               Icons.eco_outlined,
//             ),
//             ElevatedButton(
//               onPressed:
//                   hasChanges
//                       ? () {
//                         _saveNutritionalGoalChanges();
//                         ScaffoldMessenger.of(context).showSnackBar(
//                           SnackBar(
//                             content: Text(
//                               AppLocalizations.of(context)!.goalUpdated,
//                             ),
//                           ),
//                         );
//                         eventBus.fire(ProfileGoalBtnTriggerEmit(hasChanges));
//                       }
//                       : null,
//               child: Text(
//                 AppLocalizations.of(context)!.saveChange,
//                 style: TextStyle(fontSize: 18),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   void _saveNutritionalGoalChanges() {
//     UserProfileService().handleNutritionalGoalChanged(
//       calorieLimit,
//       fatLimit,
//       carbLimit,
//       proteinLimit,
//       fiberLimit,
//       waterIntakeGoal,
//       sugarIntakeGoal,
//       saltIntakeGoal,
//     );
//     setState(() {
//       hasChanges = false;
//     });
//   }
//
//   Widget buildGoalField(
//     String label,
//     double value,
//     double maxLimit,
//     IconData icon,
//   ) {
//     return GestureDetector(
//       onTap: () => _showGoalDialog(label, value, maxLimit),
//       child: Padding(
//         padding: const EdgeInsets.symmetric(vertical: 8.0),
//         child: Card(
//           elevation: 4,
//           color: Colors.teal.shade50,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(10),
//           ),
//           child: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Row(
//                   children: [
//                     Icon(icon, color: Colors.teal, size: 30),
//                     SizedBox(width: 10),
//                     Text(label, style: TextStyle(fontSize: 16)),
//                   ],
//                 ),
//                 Text(value.toStringAsFixed(0), style: TextStyle(fontSize: 16)),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   void _updateLimit(String label, double currentValue, double updatedValue) {
//     switch (label) {
//       case 'Calorie Limit' || '卡路里限制':
//         calorieLimit = updatedValue;
//       case 'Fat Limit (g)' || '脂肪限制 (克)':
//         fatLimit = updatedValue;
//       case 'Carb Limit (g)' || '碳水化合物限制 (克)':
//         carbLimit = updatedValue;
//       case 'Protein Limit (g)' || '蛋白质限制 (克)':
//         proteinLimit = updatedValue;
//       case 'Fiber Limit (g)' || '纤维限制 (克)':
//         fiberLimit = updatedValue;
//       case 'Water Intake Goal (ml)' || '水分摄入目标 (毫升)':
//         waterIntakeGoal = updatedValue;
//       case 'Sugar Intake Goal (g)' || '糖摄入目标 (克)':
//         sugarIntakeGoal = updatedValue;
//       case 'Salt Intake Goal (g)' || '盐摄入目标 (克)':
//         saltIntakeGoal = updatedValue;
//     }
//   }
//
//   void _showGoalDialog(String label, double currentValue, double maxLimit) {
//     double updatedValue = currentValue;
//     TextEditingController controller = TextEditingController(
//       text: currentValue.toStringAsFixed(0),
//     );
//
//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: Text(label, style: TextStyle(color: Colors.teal)),
//           content: SingleChildScrollView(
//             child: StatefulBuilder(
//               builder: (context, setDialogState) {
//                 return Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     TextField(
//                       controller: controller,
//                       keyboardType: TextInputType.number,
//                       decoration: InputDecoration(
//                         labelText: AppLocalizations.of(context)!.value,
//                         labelStyle: TextStyle(color: Colors.teal),
//                         focusedBorder: OutlineInputBorder(
//                           borderSide: BorderSide(color: Colors.teal),
//                         ),
//                         border: OutlineInputBorder(),
//                       ),
//                       onChanged: (value) {
//                         if (value.isNotEmpty) {
//                           double? parsedValue = double.tryParse(value);
//                           if (parsedValue != null) {
//                             setDialogState(() {
//                               updatedValue = parsedValue.clamp(0, maxLimit);
//                             });
//                           }
//                         }
//                       },
//                     ),
//                     SizedBox(height: 20),
//                     Text(
//                       AppLocalizations.of(context)!.adjustUsingSlider,
//                       style: TextStyle(color: Colors.teal),
//                     ),
//                     Slider(
//                       value: updatedValue,
//                       min: 0,
//                       max: maxLimit,
//                       divisions: 100,
//                       label: updatedValue.toStringAsFixed(0),
//                       activeColor: Colors.teal,
//                       onChanged: (value) {
//                         setDialogState(() {
//                           updatedValue = value;
//                           controller.text = value.toStringAsFixed(0);
//                         });
//                       },
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 setState(() {
//                   _updateLimit(label, currentValue, updatedValue);
//                   hasChanges = true;
//                 });
//                 Navigator.of(context).pop();
//               },
//               child: Text(
//                 AppLocalizations.of(context)!.save,
//                 style: TextStyle(color: Colors.teal),
//               ),
//             ),
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: Text(
//                 AppLocalizations.of(context)!.cancel,
//                 style: TextStyle(color: Colors.teal),
//               ),
//             ),
//           ],
//         );
//       },
//     );
//   }
// }
