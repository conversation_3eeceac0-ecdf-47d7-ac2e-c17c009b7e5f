import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/extensions/color_extension.dart';
import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:ai_vanse/extensions/num_extension.dart';
import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/screens/home/<USER>/home_summary_controller.dart';
import 'package:ai_vanse/screens/home/<USER>/meal_item.dart';
import 'package:ai_vanse/widgets/app_page_container.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:ai_vanse/widgets/date_page_view.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HomeSummaryPage extends StatefulWidget {
  const HomeSummaryPage({super.key});

  @override
  State<HomeSummaryPage> createState() => _HomeSummaryPageState();
}

class _HomeSummaryPageState extends State<HomeSummaryPage> {
  late final HomeSummaryController controller = Get.find();

  double get radius => 160.sp;

  double get dateTopPadding => 8.sp;

  double get dateBottomPadding => 16.sp;

  double get dateHeight => 66.sp;

  double get curveHeight => 75.sp;

  @override
  Widget build(BuildContext context) {
    return AppPageContainer(
      extendBody: true,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        title: AppText('home_today'.tr, appTextStyle: AppTextStyle.bodyBold),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Assets.svgs.icoCalender.svg(),
            onPressed: controller.onCalendar,
          ),
        ],
      ),
      body: Stack(
        children: [
          Container(
            height:
                context.safeAreaTopSize +
                kToolbarHeight +
                dateTopPadding +
                dateHeight +
                dateBottomPadding +
                radius +
                curveHeight,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  context.theme.extraColors.energy.withLightness(0.9),
                  context.theme.extraColors.energy,
                  context.theme.extraColors.energy.withLightness(0.2),
                ],
                stops: [0, .5, 1],
              ),
            ),
          ),
          Column(
            children: [
              Container(
                padding: EdgeInsets.only(
                  bottom: dateBottomPadding,
                  top: context.safeAreaTopSize + kToolbarHeight,
                ),
                child: buildDate(),
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: context.safeAreaBottomPadding,
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildSummary(),
                      ColoredBox(
                        color: context.theme.colorScheme.primaryContainer,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [_buildMealSection()],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return ClipPath(
      clipper: RoundedClipper(curveHeight),
      child: Container(
        color: context.theme.colorScheme.primaryContainer,
        height:
            // context.safeAreaTopSize +
            // kToolbarHeight +
            // dateTopPadding +
            // dateHeight +
            // dateBottomPadding +
            radius,
        // curveHeight +
        // curveHeight,
      ),
    );
  }

  Widget buildDate() {
    return DatePageView(
      onDateSelected: controller.onDateChange,
      height: dateHeight,
    );
  }

  Widget _buildSummary() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Positioned(bottom: 0, left: 0, right: 0, child: _buildBackground()),
        buildGraph(),
      ],
    );
  }

  Widget buildGraph() {
    return Container(
      padding: EdgeInsets.all(12.sp),
      decoration: BoxDecoration(
        color: context.theme.colorScheme.primaryContainer,
        shape: BoxShape.circle,
      ),
      child: Obx(() {
        return CircularPercentIndicator(
          radius: radius,
          lineWidth: 20.sp,
          animation: true,
          animateFromLastPercent: true,
          backgroundColor: context.theme.extraColors.progressBG,
          percent: controller.progress.value,
          // (1547 / 2000) - The progress value (0.0 to 1.0)
          center: _buildCenterSummaryDetail(),
          circularStrokeCap: CircularStrokeCap.round,
          linearGradient: LinearGradient(
            colors: [
              context.theme.colorScheme.tertiary,
              context.theme.primaryColor,
            ],
            stops: [0.0, 0.3],
          ),
        );
      }),
    );
  }

  Widget _buildCenterSummaryDetail() {
    return Obx(() {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppText(
            'calories'.tr,
            appTextStyle: AppTextStyle.h4,
            appTextColor: AppTextColor.energy,
          ),
          Skeletonizer(
            enabled: controller.isLoading.value,
            child: AppText(
              controller.totalEnergy.toCurrencyFormat(),
              appTextStyle: AppTextStyle.h1,
            ),
          ),
          AppText(
            'target'.tr,
            appTextStyle: AppTextStyle.h6,
            appTextColor: AppTextColor.target,
          ),
          AppText(
            '2,000',
            appTextStyle: AppTextStyle.h5,
            appTextColor: AppTextColor.target,
          ),
        ],
      );
    });
  }

  Widget _buildMealSection() {
    return Padding(
      padding: EdgeInsets.all(16.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                'Your Meal',
                appTextStyle: AppTextStyle.bodyText,
                appTextColor: AppTextColor.energy,
              ),
            ],
          ),
          SizedBox(height: 16.sp),
          Obx(
            () => Column(
              children: [
                ...controller.meals
                    .map((meal) => _buildMealItem(meal))
                    .toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealItem(MealItem meal) {
    return Column(
      children: [
        Divider(),
        Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8.sp),
              child: CachedNetworkImage(
                imageUrl: meal.imageUrl,
                width: 80.sp,
                height: 80.sp,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 16.sp),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(meal.name, appTextStyle: AppTextStyle.titleSmall),
                  // Row(
                  //   children: [
                  //     AppText(
                  //       'Cal ',
                  //       appTextStyle: AppTextStyle.bodyText,
                  //       appTextColor: AppTextColor.tertiary,
                  //     ),
                  //     // AppText(
                  //     //   meal.calories.toString(),
                  //     //   appTextStyle: AppTextStyle.h4,
                  //     //   appTextColor: AppTextColor.energy,
                  //     // ),
                  //   ],
                  // ),
                  SizedBox(height: 8.sp),
                  AppText(
                    meal.time,
                    appTextStyle: AppTextStyle.bodyText,
                    appTextColor: AppTextColor.tertiary,
                  ),
                  SizedBox(height: 8.sp),
                ],
              ),
            ),
            Assets.svgs.icoMeal.svg(),
            SizedBox(width: 8.sp),

            SizedBox(
              width: 36.sp,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  AppText('Cal', appTextStyle: AppTextStyle.titleSmall),
                  AppText(
                    meal.calories.toString(),
                    appTextStyle: AppTextStyle.titleSmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class RoundedClipper extends CustomClipper<Path> {
  final double curveHeight;

  const RoundedClipper(this.curveHeight);

  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(0, size.height);
    path.lineTo(0, 0);

    path.relativeQuadraticBezierTo(size.width / 2, curveHeight, size.width, 0);
    path.lineTo(size.width, size.height);

    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class ShapesPainter extends CustomPainter {
  final LinearGradient linearGradient;
  final double curveHeight;

  const ShapesPainter({
    super.repaint,
    required this.linearGradient,
    required this.curveHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0.0, 0.0, size.width, size.height);
    final p = Path();
    p.lineTo(0, size.height - curveHeight);
    p.relativeQuadraticBezierTo(size.width / 2, 2 * curveHeight, size.width, 0);
    p.lineTo(size.width, 0);
    p.close();

    final Paint paint = Paint();

    // paint.shader = linearGradient.createShader(rect);
    canvas.drawPath(p, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
