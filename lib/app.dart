import 'package:ai_vanse/l10n/translate.dart';
import 'package:ai_vanse/routes/app_page.dart';
import 'package:ai_vanse/routes/app_route.dart';
import 'package:ai_vanse/theme/dark.dart';
import 'package:ai_vanse/theme/light.dart';
import 'package:ai_vanse/utils/app_screen_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  @override
  Widget build(BuildContext context) {
    final appTranslation = AppTranslation();
    final AppScreenType screenType = AppScreenType.fromMinSize(
      MediaQuery.of(context).size.shortestSide,
    );
    ScreenUtil.configure(
      data: context.mediaQuery,
      designSize: screenType.size,
      minTextAdapt: true,
      splitScreenMode: true,
    );
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      enableLog: true,
      smartManagement: SmartManagement.keepFactory,
      defaultTransition: Transition.cupertino,
      opaqueRoute: Get.isOpaqueRouteDefault,
      popGesture: Get.isPopGestureEnable,
      initialRoute: AppRoutes.home,
      getPages: pages,
      themeMode: ThemeMode.light,
      theme: LightTheme().themeData(),
      darkTheme: DarkTheme().themeData(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      locale: Get.deviceLocale,
      supportedLocales: appTranslation.supportedLocales,
      translations: appTranslation,
      builder: (context, child) {
        // independent font size from device
        return MediaQuery(
          data: MediaQuery.of(
            context,
          ).copyWith(textScaler: TextScaler.noScaling),
          child: child ?? Container(),
        );
      },
    );
  }
}
