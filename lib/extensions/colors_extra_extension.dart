import 'package:flutter/material.dart';

@immutable
class ExtraColors extends ThemeExtension<ExtraColors> {
  final Map<int, Color> slate;
  final Map<int, Color> red;
  final Color hint;
  final Color divider;
  final Color unselected;
  final Color selected;
  final Color selectedWhite;
  final Color progressBG;
  final Color defaultTextColor;
  final Color energy;
  final Color target;

  const ExtraColors({
    required this.slate,
    required this.red,
    required this.hint,
    required this.divider,
    required this.unselected,
    required this.selected,
    required this.selectedWhite,
    required this.progressBG,
    required this.defaultTextColor,
    required this.energy,
    required this.target,
  });

  @override
  ExtraColors copyWith({
    Map<int, Color>? slate,
    Map<int, Color>? red,
    Color? hint,
    Color? divider,
    Color? unselected,
    Color? selected,
    Color? selectedWhite,
    Color? progressBG,
    Color? defaultTextColor,
    Color? energy,
    Color? target,
  }) {
    return ExtraColors(
      slate: slate ?? this.slate,
      red: red ?? this.red,
      hint: hint ?? this.hint,
      divider: divider ?? this.divider,
      unselected: unselected ?? this.unselected,
      selected: selected ?? this.selected,
      selectedWhite: selectedWhite ?? this.selectedWhite,
      progressBG: progressBG ?? this.progressBG,
      defaultTextColor: defaultTextColor ?? this.defaultTextColor,
      energy: energy ?? this.energy,
      target: target ?? this.target,
    );
  }

  @override
  ExtraColors lerp(ThemeExtension<ExtraColors>? other, double t) {
    if (other is! ExtraColors) {
      return this;
    }
    return ExtraColors(
      slate: lerpSwatch(slate, other.slate, t) ?? slate,
      red: lerpSwatch(red, other.red, t) ?? red,
      hint: Color.lerp(hint, other.hint, t) ?? hint,
      divider: Color.lerp(divider, other.divider, t) ?? divider,
      unselected: Color.lerp(unselected, other.unselected, t) ?? unselected,
      selected: Color.lerp(selected, other.selected, t) ?? selected,
      selectedWhite:
          Color.lerp(selectedWhite, other.selectedWhite, t) ?? selectedWhite,
      progressBG: Color.lerp(progressBG, other.progressBG, t) ?? progressBG,
      defaultTextColor:
          Color.lerp(defaultTextColor, other.defaultTextColor, t) ??
          defaultTextColor,
      energy: Color.lerp(energy, other.energy, t) ?? energy,
      target: Color.lerp(target, other.target, t) ?? target,
    );
  }

  Map<int, Color>? lerpSwatch(
    Map<int, Color>? a,
    Map<int, Color>? b,
    double t,
  ) {
    if (a == null || b == null) return a ?? b;
    return Map.fromIterable(
      a.keys,
      value: (key) => Color.lerp(a[key], b[key], t)!,
    );
  }
}

extension ColorSwatchExtension on Map<int, Color>? {
  Color get c50 => this?[50] ?? Colors.transparent;

  Color get c100 => this?[100] ?? Colors.transparent;

  Color get c200 => this?[200] ?? Colors.transparent;

  Color get c300 => this?[300] ?? Colors.transparent;

  Color get c400 => this?[400] ?? Colors.transparent;

  Color get c500 => this?[500] ?? Colors.transparent;

  Color get c600 => this?[600] ?? Colors.transparent;

  Color get c700 => this?[700] ?? Colors.transparent;

  Color get c800 => this?[800] ?? Colors.transparent;

  Color get c900 => this?[900] ?? Colors.transparent;

  Color get c950 => this?[950] ?? Colors.transparent;
}
