import 'dart:io';

import 'package:ai_vanse/utilities/snackbar.dart';
import 'package:ai_vanse/widgets/control_buttons.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

class CameraScreen extends StatefulWidget {
  final void Function(XFile) onCaptureConfirmed;

  const CameraScreen({super.key, required this.onCaptureConfirmed});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  CameraController? _controller;
  List<CameraDescription> cameras = [];

  // Focus pivot animation
  late AnimationController _focusIndicatorController;
  late Animation<double> _focusIndicatorSizeAnimation;
  late Animation<double> _focusIndicatorOpacityAnimation;
  Offset? _tapPosition;
  bool _showFocusIndicator = false;

  // Flash
  static const List<FlashMode> flashModes = [
    FlashMode.off,
    FlashMode.always,
    FlashMode.auto,
  ];
  static const List<IconData> flashIcons = [
    Icons.flash_off,
    Icons.flash_on,
    Icons.flash_auto,
  ];
  int selectedFlashModeIndex = 0;

  // Zoom, focus and exposure
  final int _pointers = 0;
  double _baseScale = 1.0;
  double _currentScale = 1.0;
  final double _minAvailableZoom = 1.0;
  final double _maxAvailableZoom = 1.0;

  // double _minAvailableExposureOffset = 0.0;
  // double _maxAvailableExposureOffset = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _focusIndicatorController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 1300),
    );

    _focusIndicatorSizeAnimation = Tween<double>(
      begin: 80.0,
      end: 50.0,
    ).animate(
      CurvedAnimation(
        parent: _focusIndicatorController,
        curve: Interval(0.0, 0.3, curve: Curves.easeOut),
      ),
    );

    _focusIndicatorOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(
      CurvedAnimation(
        parent: _focusIndicatorController,
        curve: Interval(0.7, 1.0, curve: Curves.easeOut),
      ),
    );
    _initializeCameras(null);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _focusIndicatorController.dispose();
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final CameraController? cameraController = _controller;
    if (cameraController == null || !cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCameras(cameraController.description);
    }
  }

  void _logError(String code, String? message) {
    debugPrint(
      'Error: $code${message == null ? '' : '\nError Message: $message'}',
    );
  }

  void showInSnackBar(String message) {
    showSnackBar(context, message);
  }

  void _showCameraException(CameraException e) {
    _logError(e.code, e.description);
    showInSnackBar('Error: ${e.code}\n${e.description}');
  }

  Future<void> _initializeCameras(CameraDescription? cameraDescription) async {
    if (cameraDescription == null) {
      cameras = await availableCameras();
      cameraDescription = cameras.first;
    }

    final CameraController cameraController = CameraController(
      cameraDescription,
      ResolutionPreset.veryHigh,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    _controller = cameraController;

    cameraController.addListener(() {
      if (mounted) {
        setState(() {});
      }
      if (cameraController.value.hasError) {
        showInSnackBar(
          'Camera error ${cameraController.value.errorDescription}',
        );
      }
    });

    try {
      await cameraController.initialize();
      // await Future.wait(<Future<Object?>>[
      //   // The exposure mode is currently not supported on the web.
      //   ...<Future<Object?>>[
      //     cameraController
      //         .getMinExposureOffset()
      //         .then((double value) => _minAvailableExposureOffset = value),
      //     cameraController
      //         .getMaxExposureOffset()
      //         .then((double value) => _maxAvailableExposureOffset = value)
      //   ]
      // ]);
    } on CameraException catch (e) {
      switch (e.code) {
        case 'CameraAccessDenied':
          showInSnackBar('You have denied camera access.');
        case 'CameraAccessDeniedWithoutPrompt':
          // iOS only
          showInSnackBar('Please go to Settings app to enable camera access.');
        case 'CameraAccessRestricted':
          // iOS only
          showInSnackBar('Camera access is restricted.');
        case 'AudioAccessDenied':
          showInSnackBar('You have denied audio access.');
        case 'AudioAccessDeniedWithoutPrompt':
          // iOS only
          showInSnackBar('Please go to Settings app to enable audio access.');
        case 'AudioAccessRestricted':
          // iOS only
          showInSnackBar('Audio access is restricted.');
        default:
          _showCameraException(e);
      }
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future _onCapturePressed() async {
    final CameraController? cameraController = _controller;
    if (cameraController == null || !cameraController.value.isInitialized) {
      showInSnackBar('Error: select a camera first.');
      return;
    }

    if (cameraController.value.isTakingPicture) {
      return;
    }

    try {
      final XFile file = await cameraController.takePicture();
      final fixedImage = await FlutterImageCompress.compressAndGetFile(
        file.path,
        file.path,
        quality: 100,
        minWidth: 2048,
        minHeight: 2048,
        format: CompressFormat.jpeg,
      );
      if (!mounted || fixedImage == null) {
        return;
      }
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => DisplayPictureScreen(
                image: fixedImage,
                onCaptureConfirmed: widget.onCaptureConfirmed,
              ),
        ),
      );
    } on CameraException catch (e) {
      _showCameraException(e);
      return;
    }
  }

  void onFlashModePressed() {
    final CameraController? cameraController = _controller;
    setState(() {
      selectedFlashModeIndex = (selectedFlashModeIndex + 1) % flashModes.length;
    });
    cameraController?.setFlashMode(flashModes[selectedFlashModeIndex]);
  }

  Future<void> _handleScaleUpdate(ScaleUpdateDetails details) async {
    // When there are not exactly two fingers on screen don't scale
    if (_controller == null || _pointers != 2) {
      return;
    }

    _currentScale = (_baseScale * details.scale).clamp(
      _minAvailableZoom,
      _maxAvailableZoom,
    );

    await _controller!.setZoomLevel(_currentScale);
  }

  void _handleScaleStart(ScaleStartDetails details) {
    _baseScale = _currentScale;
  }

  void onViewFinderTap(TapDownDetails details, BoxConstraints constraints) {
    if (_controller == null) {
      return;
    }

    final CameraController cameraController = _controller!;

    final Offset offset = Offset(
      details.localPosition.dx / constraints.maxWidth,
      details.localPosition.dy / constraints.maxHeight,
    );
    cameraController.setExposurePoint(offset);
    cameraController.setFocusPoint(offset);

    setState(() {
      _tapPosition = details.localPosition;
      _showFocusIndicator = true;
    });

    _focusIndicatorController.reset();
    _focusIndicatorController.forward().then((_) {
      setState(() {
        _showFocusIndicator = false;
      });
    });
  }

  Widget _buildCameraPreviewWidget(BuildContext context) {
    final CameraController? cameraController = _controller;
    if (cameraController == null || !cameraController.value.isInitialized) {
      return Center(child: CircularProgressIndicator());
    } else {
      return CameraPreview(
        cameraController,
        child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onScaleStart: _handleScaleStart,
              onScaleUpdate: _handleScaleUpdate,
              onTapDown:
                  (TapDownDetails details) =>
                      onViewFinderTap(details, constraints),
            );
          },
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final assetGridHeight = MediaQuery.of(context).size.height * 0.05;
    final screenWidth = MediaQuery.of(context).size.width;

    return Stack(
      children: [
        Positioned.fill(child: _buildCameraPreviewWidget(context)),
        if (_tapPosition != null && _showFocusIndicator) _buildFocusPivot(),
        // _buildCaptureButton(assetGridHeight, screenWidth),
        _buildFlashToggleButton(),
      ],
    );
  }

  AnimatedBuilder _buildFocusPivot() {
    return AnimatedBuilder(
      animation: _focusIndicatorSizeAnimation,
      builder: (context, child) {
        return Positioned(
          left: _tapPosition!.dx - _focusIndicatorSizeAnimation.value / 2,
          top: _tapPosition!.dy - _focusIndicatorSizeAnimation.value / 2,
          child: FadeTransition(
            opacity: _focusIndicatorOpacityAnimation,
            child: Container(
              width: _focusIndicatorSizeAnimation.value,
              height: _focusIndicatorSizeAnimation.value,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
            ),
          ),
        );
      },
    );
  }

  Positioned _buildCaptureButton(double assetGridHeight, double screenWidth) {
    return Positioned.fill(
      bottom: assetGridHeight,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: IconButton(
          icon: Icon(
            Icons.radio_button_on_outlined,
            color: Colors.white,
            size: screenWidth * 0.15,
          ),
          onPressed: _onCapturePressed,
        ),
      ),
    );
  }

  Align _buildFlashToggleButton() {
    return Align(
      alignment: Alignment.topRight,
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.black.withOpacity(0.5),
          ),
          child: IconButton(
            icon: Icon(flashIcons[selectedFlashModeIndex], color: Colors.white),
            onPressed: onFlashModePressed,
          ),
        ),
      ),
    );
  }
}

class DisplayPictureScreen extends StatelessWidget {
  final void Function(XFile) onCaptureConfirmed;
  final XFile image;

  const DisplayPictureScreen({
    super.key,
    required this.image,
    required this.onCaptureConfirmed,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: Image.file(fit: BoxFit.cover, File(image.path)),
            ),
            Align(
              alignment: Alignment.topRight,
              child: confirmButton(() => onCaptureConfirmed(image)),
            ),
            Align(
              alignment: Alignment.topLeft,
              child: cancelButton(Navigator.of(context).pop),
            ),
          ],
        ),
      ),
    );
  }
}
