import 'dart:convert';

import 'package:ai_vanse/api/converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'activity_year.freezed.dart';
part 'activity_year.g.dart';

ActivityYear activityYearFromJson(String str) =>
    ActivityYear.fromJson(json.decode(str));

String activityYearToJson(ActivityYear data) => json.encode(data.toJson());

@freezed
abstract class ActivityYear with _$ActivityYear {
  const factory ActivityYear({
    @MonthOnlyConverter() DateTime? month,
    Energy? energy,
    ActivityDuration? duration,
    int? steps,
    int? distance,
    int? noOfDate,
  }) = _ActivityYear;

  factory ActivityYear.fromJson(Map<String, dynamic> json) =>
      _$ActivityYearFromJson(json);
}

@freezed
abstract class ActivityDuration with _$ActivityDuration {
  const factory ActivityDuration({
    int? lying,
    int? lyingOnBack,
    int? lyingOnStomach,
    int? lyingOnLeft,
    int? lyingOnRight,
    int? sitting,
    int? standing,
    int? walking,
  }) = _ActivityDuration;

  factory ActivityDuration.fromJson(Map<String, dynamic> json) =>
      _$ActivityDurationFromJson(json);
}

@freezed
abstract class Energy with _$Energy {
  const factory Energy({
    int? lying,
    int? sitting,
    int? standing,
    int? walking,
    int? total,
  }) = _Energy;

  factory Energy.fromJson(Map<String, dynamic> json) => _$EnergyFromJson(json);
}
