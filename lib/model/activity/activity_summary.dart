import 'package:ai_vanse/asset/assets.gen.dart';
import 'package:ai_vanse/extensions/datetime_extension.dart';
import 'package:ai_vanse/extensions/num_extension.dart';
import 'package:ai_vanse/model/activity/activity.dart';
import 'package:ai_vanse/model/activity/activity_unit.dart';
import 'package:ai_vanse/model/activity/activity_year.dart';
import 'package:get/get.dart';

class ActivitySummary {
  final StepByActivity step;
  final DistanceByActivity distance;
  final CalorieByActivity calorie;
  final DurationByActivity duration;
  final String display;
  final DateTime dateTime;

  factory ActivitySummary.fromActivity({Activity? activity, DateTime? date}) {
    return ActivitySummary(
      step: StepByActivity(walkAmount: activity?.steps ?? 0),
      distance: DistanceByActivity(walkAmount: activity?.distance ?? 0),
      calorie: CalorieByActivity(
        walkAmount: activity?.energy?.walking ?? 0,
        standAmount: activity?.energy?.standing ?? 0,
        sitAmount: activity?.energy?.sitting ?? 0,
        layAmount: activity?.energy?.lying ?? 0,
        total: activity?.energy?.total ?? 0,
      ),
      duration: DurationByActivity(
        walkAmount: activity?.duration?.walking ?? 0,
        standAmount: activity?.duration?.standing ?? 0,
        sitAmount: activity?.duration?.sitting ?? 0,
        layAmount: activity?.duration?.lying ?? 0,
        total: activity?.energy?.total ?? 0,
      ),
      display: (activity?.createdDate ?? date)?.format('dd/MM') ?? '',
      dateTime: activity?.createdDate ?? date ?? DateTime.now(),
    );
  }

  factory ActivitySummary.fromActivityYear({
    ActivityYear? activity,
    DateTime? date,
  }) {
    return ActivitySummary(
      step: StepByActivity(walkAmount: activity?.steps ?? 0),
      distance: DistanceByActivity(walkAmount: activity?.distance ?? 0),
      calorie: CalorieByActivity(
        walkAmount: activity?.energy?.walking ?? 0,
        standAmount: activity?.energy?.standing ?? 0,
        sitAmount: activity?.energy?.sitting ?? 0,
        layAmount: activity?.energy?.lying ?? 0,
        total: activity?.energy?.total ?? 0,
      ),
      duration: DurationByActivity(
        walkAmount: activity?.duration?.walking ?? 0,
        standAmount: activity?.duration?.standing ?? 0,
        sitAmount: activity?.duration?.sitting ?? 0,
        layAmount: activity?.duration?.lying ?? 0,
        total: activity?.energy?.total ?? 0,
      ),
      display: (activity?.month ?? date).format('MM'),
      dateTime: activity?.month ?? date ?? DateTime.now(),
    );
  }

  ActivitySummary({
    required this.step,
    required this.distance,
    required this.calorie,
    required this.duration,
    required this.display,
    required this.dateTime,
  });

  ActivityByUnit getActivityFromUnit(ActivityUnit unit) => switch (unit) {
    ActivityUnit.step => step,
    ActivityUnit.distance => distance,
    ActivityUnit.calorie => calorie,
    ActivityUnit.duration => duration,
  };
}

sealed class ActivityByUnit {
  final WalkActivity walkAmount;
  final StandActivity standAmount;
  final SitActivity sitAmount;
  final LayActivity layAmount;
  final int total;

  ActivityByUnit({
    required int walkAmount,
    required int standAmount,
    required int sitAmount,
    required int layAmount,
    required this.total,
  }) : walkAmount = WalkActivity(amount: walkAmount),
       standAmount = StandActivity(amount: standAmount),
       sitAmount = SitActivity(amount: sitAmount),
       layAmount = LayActivity(amount: layAmount);

  String get displayWalkAmount => format(walkAmount.amount);

  String get displayStandAmount => format(standAmount.amount);

  String get displaySitAmount => format(sitAmount.amount);

  String get displayLayAmount => format(layAmount.amount);

  String getDisplayFromType(ActivityType type) {
    return format(type.amount);
  }

  String getRawDisplayFromType(ActivityType type) {
    return rawFormat(type.amount);
  }

  double getTotalConverted() {
    return convert(total);
  }

  String getRawTotalDisplay() {
    return rawFormat(total);
  }

  String getTotalDisplay() {
    return format(total);
  }

  double getAmountConvertedFromType(ActivityType type) {
    return convert(type.amount);
  }

  String format(int value) {
    if (value == 0) return '--';
    if (value > 10000) return '>10000';
    return value.toCurrencyFormat();
  }

  String rawFormat(int value) {
    return value.toCurrencyFormat();
  }

  double convert(int value) {
    return value.toDouble();
  }

  String get tr;

  List<ActivityType> get activities => [
    walkAmount,
    standAmount,
    sitAmount,
    layAmount,
  ];
}

class StepByActivity extends ActivityByUnit {
  StepByActivity({required super.walkAmount})
    : super(layAmount: 0, sitAmount: 0, standAmount: 0, total: walkAmount);

  @override
  String get tr => 'home_unit_step'.tr;
}

class DistanceByActivity extends ActivityByUnit {
  DistanceByActivity({required super.walkAmount})
    : super(layAmount: 0, sitAmount: 0, standAmount: 0, total: walkAmount);

  @override
  String get tr => 'home_unit_distance'.tr;

  @override
  String format(int value) {
    final double km = convert(value);
    if (km == 0) return '--';
    if (km < 0.01) return '<0.01';
    if (km > 100) return '>100';
    return km.toCurrencyFormat(showDecimal: true);
  }

  @override
  double convert(int value) {
    return value / 1000.0;
  }
}

class CalorieByActivity extends ActivityByUnit {
  CalorieByActivity({
    required super.walkAmount,
    required super.standAmount,
    required super.sitAmount,
    required super.layAmount,
    required super.total,
  });

  @override
  String get tr => 'home_unit_calorie'.tr;
}

class DurationByActivity extends ActivityByUnit {
  DurationByActivity({
    required super.walkAmount,
    required super.standAmount,
    required super.sitAmount,
    required super.layAmount,
    required super.total,
  });

  @override
  String get tr => 'home_unit_duration'.tr;

  @override
  String format(int value) {
    final double hour = convert(value);
    if (hour == 0) return '--';
    if (hour < 0.01) return '<0:01';
    if (hour > 100) return '>100';
    return rawFormat(value);
  }

  @override
  String rawFormat(int value) {
    return value.toHourMinute();
  }

  @override
  double convert(int value) {
    return value / (60.0 * 60.0);
  }
}

sealed class ActivityType {
  SvgGenImage get activeIcon;

  SvgGenImage get inactiveIcon;

  SvgGenImage get icon => active ? activeIcon : inactiveIcon;

  final int amount;
  final bool active;

  ActivityType({required this.amount, bool? active})
    : active = active ?? amount != 0;
}

class WalkActivity extends ActivityType {
  @override
  final SvgGenImage activeIcon = Assets.svgs.icoActivityActive;
  @override
  final SvgGenImage inactiveIcon = Assets.svgs.icoActivityInactive;

  WalkActivity({required super.amount, super.active});
}

class StandActivity extends ActivityType {
  @override
  final SvgGenImage activeIcon = Assets.svgs.icoStandingActive;
  @override
  final SvgGenImage inactiveIcon = Assets.svgs.icoStandingInactive;

  StandActivity({required super.amount, super.active});
}

class SitActivity extends ActivityType {
  @override
  final SvgGenImage activeIcon = Assets.svgs.icoSittingActive;
  @override
  final SvgGenImage inactiveIcon = Assets.svgs.icoSittingInactive;

  SitActivity({required super.amount, super.active});
}

class LayActivity extends ActivityType {
  @override
  final SvgGenImage activeIcon = Assets.svgs.icoLayingActive;
  @override
  final SvgGenImage inactiveIcon = Assets.svgs.icoLayingInactive;

  LayActivity({required super.amount, super.active});
}
