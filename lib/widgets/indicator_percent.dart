import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class IndicatorPercent extends StatelessWidget {
  const IndicatorPercent({
    super.key,
    required this.color,
    required this.text,
    this.size = 16,
    this.textColor,
    this.isSquare = false,
  });

  final Color color;
  final String text;
  final bool isSquare;
  final double size;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Container(
          width: size.sp,
          height: size.sp,
          decoration: BoxDecoration(
            shape: isSquare ? BoxShape.rectangle : BoxShape.circle,
            color: color,
          ),
        ),
        const SizedBox(width: 6),
        Text(text, style: TextStyle(fontSize: 16.sp, color: textColor)),
      ],
    );
  }
}
