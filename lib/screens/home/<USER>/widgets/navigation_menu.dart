import 'package:ai_vanse/extensions/theme_extension.dart';
import 'package:ai_vanse/screens/home/<USER>/home_controller.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class NavigationMenu extends GetView<HomeController> {
  const NavigationMenu({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final icoSize = 24.sp;
    final borderRadius = BorderRadius.only(
      topLeft: Radius.circular(16.r),
      topRight: Radius.circular(16.r),
    );

    return Obx(() {
      final currentPage = controller.menuIndex;
      final menus = controller.menus;

      return BottomNavigationBar(
        currentIndex: currentPage,
        iconSize: icoSize,
        backgroundColor: colorScheme.primaryContainer.withOpacity(0.8),
        selectedItemColor: colorScheme.onPrimary,
        unselectedItemColor: theme.extraColors.hint,
        landscapeLayout: BottomNavigationBarLandscapeLayout.centered,
        showSelectedLabels: false,
        showUnselectedLabels: false,
        type: BottomNavigationBarType.fixed,
        selectedLabelStyle: AppTextStyle.bodyBold.textStyle,
        unselectedLabelStyle: AppTextStyle.bodyText.textStyle,
        onTap: (index) => controller.menuIndex = index,
        items:
            menus.map((menu) {
              return BottomNavigationBarItem(
                activeIcon: SvgPicture.asset(
                  menu.iconActive ?? menu.icon,
                  width: menu.iconSize ?? icoSize,
                  height: menu.iconSize ?? icoSize,
                ),
                icon: SvgPicture.asset(
                  menu.icon,
                  width: menu.iconSize ?? icoSize,
                  height: menu.iconSize ?? icoSize,
                ),
                label: menu.label.tr,
              );
            }).toList(),
      );
    });
  }
}
