import 'package:flutter/material.dart';

class BoxPlot extends StatelessWidget {
  final double min;
  final double q1;
  final double median;
  final double q3;
  final double max;

  const BoxPlot({
    super.key,
    required this.min,
    required this.q1,
    required this.median,
    required this.q3,
    required this.max,
  });

  @override
  Widget build(BuildContext context) {
    final double deviceWidth = MediaQuery.of(context).size.width;
    final double plotWidth = deviceWidth * 0.8;
    return SizedBox(
      height: 120,
      width: plotWidth,
      child: CustomPaint(painter: BoxPlotPainter(min, q1, median, q3, max)),
    );
  }
}

class BoxPlotPainter extends CustomPainter {
  final double min;
  final double q1;
  final double median;
  final double q3;
  final double max;

  BoxPlotPainter(this.min, this.q1, this.median, this.q3, this.max);

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;

    final double minPos = 0;
    final double range = max - min;
    final double q1Pos = width * (q1 - min) / range;
    final double medianPos = width * (median - min) / range;
    final double q3Pos = width * (q3 - min) / range;
    final double maxPos = width;

    final paint =
        Paint()
          ..color = Colors.black
          ..style = PaintingStyle.stroke;

    final paintWhiskers =
        Paint()
          ..color = Colors.redAccent
          ..style = PaintingStyle.stroke
          ..strokeWidth = 4.0;

    // Draw whiskers (min and max)
    canvas.drawLine(
      Offset(minPos, height / 2),
      Offset(q1Pos, height / 2),
      paintWhiskers,
    );
    canvas.drawLine(
      Offset(q3Pos, height / 2),
      Offset(maxPos, height / 2),
      paintWhiskers,
    );

    // Draw the box
    paint.strokeWidth = 2.0;
    canvas.drawRect(
      Rect.fromPoints(Offset(q1Pos, height * 0.4), Offset(q3Pos, height * 0.6)),
      paint..color = Colors.blue,
    );

    // Draw longer min and max lines
    paint.color = Colors.black;
    paint.strokeWidth = 1;
    canvas.drawLine(
      Offset(minPos, height * 0.3),
      Offset(minPos, height * 0.7),
      paint,
    );
    canvas.drawLine(
      Offset(maxPos, height * 0.3),
      Offset(maxPos, height * 0.7),
      paint,
    );

    // Draw labels
    final textPainter = TextPainter(
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    // Function to draw text
    void drawText(String text, double x, double y) {
      textPainter.text = TextSpan(
        text: text,
        style: TextStyle(color: Colors.grey.shade800, fontSize: 12),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, y));
    }

    // Draw the median line
    paint.color = Colors.green;
    paint.strokeWidth = 3;
    canvas.drawLine(
      Offset(medianPos, height * 0.3),
      Offset(medianPos, height * 0.7),
      paint,
    );

    // Draw labels for min, Q1, median, Q3, and max
    drawText(min.toString(), minPos, height * 0.2);
    drawText(q1.toString(), q1Pos, height * 0.2);
    drawText(median.toStringAsFixed(3), medianPos, height * 0.8);
    drawText(q3.toString(), q3Pos, height * 0.2);
    drawText(max.toString(), maxPos, height * 0.2);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
