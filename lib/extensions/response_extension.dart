import 'package:ai_vanse/exceptions/dio_exception.dart';
import 'package:dio/dio.dart';
import 'package:get/get_connect/http/src/exceptions/exceptions.dart';
import 'package:retrofit/retrofit.dart';

Future<HttpResponse<T>> extractApiResponse<T>(
  Future<HttpResponse<T>> apiFutureResponse,
) async {
  final HttpResponse<T> response = await apiFutureResponse;
  if (response.response.statusCode != null &&
      response.response.statusCode! < 300) {
    return response;
  }

  throw DioException(
    response: response.response,
    requestOptions: response.response.requestOptions,
  );
}

// https://stackoverflow.com/a/73277805
Future<T> onDioException<T>(
  DioException exception,
  StackTrace stackTrace,
  DioErrorHandler? handler,
) {
  if (handler != null) {
    final handledException = handler.fromDioException(exception, stackTrace);
    return Future.error(handledException, stackTrace);
  }
  switch (exception.type) {
    case DioExceptionType.badResponse:
      switch (exception.response?.statusCode) {
        case 401:
          return Future.error(UnauthorizedException(), stackTrace);
      }
      break;
    case DioExceptionType.connectionTimeout:
    case DioExceptionType.connectionError:
    default:
  }
  return Future.error(exception, stackTrace);
}

extension ResponseExtension<R> on Future<HttpResponse<R>> {
  Future<R> toResponse({DioErrorHandler? handler}) async {
    try {
      final response = await extractApiResponse(this);
      return response.data!;
    } on DioException catch (exception, stackTrace) {
      return onDioException(exception, stackTrace, handler);
    } catch (exception, stackTrace) {
      return Future.error(exception, stackTrace);
    }
  }

  Future<void> toVoidResponse({DioErrorHandler? handler}) async {
    try {
      await extractApiResponse(this);
      return;
    } on DioException catch (exception, stackTrace) {
      return onDioException(exception, stackTrace, handler);
    } catch (exception) {
      rethrow;
    }
  }
}
