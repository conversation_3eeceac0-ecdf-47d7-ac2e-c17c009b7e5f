// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'server_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ServerDto {

 int get id; String get name; String get apiUrl; String get notificationUrl; String get code;
/// Create a copy of ServerDto
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerDtoCopyWith<ServerDto> get copyWith => _$ServerDtoCopyWithImpl<ServerDto>(this as ServerDto, _$identity);

  /// Serializes this ServerDto to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerDto&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.apiUrl, apiUrl) || other.apiUrl == apiUrl)&&(identical(other.notificationUrl, notificationUrl) || other.notificationUrl == notificationUrl)&&(identical(other.code, code) || other.code == code));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,apiUrl,notificationUrl,code);

@override
String toString() {
  return 'ServerDto(id: $id, name: $name, apiUrl: $apiUrl, notificationUrl: $notificationUrl, code: $code)';
}


}

/// @nodoc
abstract mixin class $ServerDtoCopyWith<$Res>  {
  factory $ServerDtoCopyWith(ServerDto value, $Res Function(ServerDto) _then) = _$ServerDtoCopyWithImpl;
@useResult
$Res call({
 int id, String name, String apiUrl, String notificationUrl, String code
});




}
/// @nodoc
class _$ServerDtoCopyWithImpl<$Res>
    implements $ServerDtoCopyWith<$Res> {
  _$ServerDtoCopyWithImpl(this._self, this._then);

  final ServerDto _self;
  final $Res Function(ServerDto) _then;

/// Create a copy of ServerDto
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? apiUrl = null,Object? notificationUrl = null,Object? code = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,apiUrl: null == apiUrl ? _self.apiUrl : apiUrl // ignore: cast_nullable_to_non_nullable
as String,notificationUrl: null == notificationUrl ? _self.notificationUrl : notificationUrl // ignore: cast_nullable_to_non_nullable
as String,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc

@JsonSerializable(explicitToJson: true)
class _ServerDto implements ServerDto {
  const _ServerDto({required this.id, required this.name, required this.apiUrl, required this.notificationUrl, required this.code});
  factory _ServerDto.fromJson(Map<String, dynamic> json) => _$ServerDtoFromJson(json);

@override final  int id;
@override final  String name;
@override final  String apiUrl;
@override final  String notificationUrl;
@override final  String code;

/// Create a copy of ServerDto
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ServerDtoCopyWith<_ServerDto> get copyWith => __$ServerDtoCopyWithImpl<_ServerDto>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ServerDtoToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ServerDto&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.apiUrl, apiUrl) || other.apiUrl == apiUrl)&&(identical(other.notificationUrl, notificationUrl) || other.notificationUrl == notificationUrl)&&(identical(other.code, code) || other.code == code));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,apiUrl,notificationUrl,code);

@override
String toString() {
  return 'ServerDto(id: $id, name: $name, apiUrl: $apiUrl, notificationUrl: $notificationUrl, code: $code)';
}


}

/// @nodoc
abstract mixin class _$ServerDtoCopyWith<$Res> implements $ServerDtoCopyWith<$Res> {
  factory _$ServerDtoCopyWith(_ServerDto value, $Res Function(_ServerDto) _then) = __$ServerDtoCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String apiUrl, String notificationUrl, String code
});




}
/// @nodoc
class __$ServerDtoCopyWithImpl<$Res>
    implements _$ServerDtoCopyWith<$Res> {
  __$ServerDtoCopyWithImpl(this._self, this._then);

  final _ServerDto _self;
  final $Res Function(_ServerDto) _then;

/// Create a copy of ServerDto
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? apiUrl = null,Object? notificationUrl = null,Object? code = null,}) {
  return _then(_ServerDto(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,apiUrl: null == apiUrl ? _self.apiUrl : apiUrl // ignore: cast_nullable_to_non_nullable
as String,notificationUrl: null == notificationUrl ? _self.notificationUrl : notificationUrl // ignore: cast_nullable_to_non_nullable
as String,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
