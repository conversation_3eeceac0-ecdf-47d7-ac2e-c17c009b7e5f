import 'dart:math';
import 'dart:typed_data';

String formatDate(DateTime date) {
  return '${date.day}/${date.month}/${date.year}';
}

String capitalize(String s) {
  if (s.isEmpty) return s;
  return s[0].toUpperCase() + s.substring(1);
}

List<int> imuByteDataToInt16(List<int> value) {
  ByteData byteData = ByteData.sublistView(Uint8List.fromList(value));
  List<int> intValues = [];
  for (int i = 1; i < value.length; i += 2) {
    int int16Value = byteData.getInt16(i, Endian.little);
    intValues.add(int16Value);
  }
  return intValues;
}

bool isLocalPeak(Iterable<double> data, int index) {
  if (index == 0 || index == data.length - 1) {
    return false;
  }
  final prev = data.elementAt(index - 1);
  final current = data.elementAt(index);
  final next = data.elementAt(index + 1);
  return current > prev && current > next;
}

bool isLocalTrough(Iterable<double> data, int index) {
  if (index == 0 || index == data.length - 1) {
    return false;
  }
  final prev = data.elementAt(index - 1);
  final current = data.elementAt(index);
  final next = data.elementAt(index + 1);
  return current < prev && current < next;
}

bool isTurningPoint(Iterable<double> data, int index) {
  if (index == 0 || index == data.length - 1) {
    return false;
  }
  final prev = data.elementAt(index - 1);
  final current = data.elementAt(index);
  final next = data.elementAt(index + 1);
  return (current > prev && current > next) ||
      (current < prev && current < next);
}

double firstDerivative(Iterable<double> data, int index) {
  if (index == 0 || index == data.length - 1) {
    return -1000.0;
  }
  return (data.elementAt(index + 1) - data.elementAt(index - 1)) / 2;
}

double secondDerivative(Iterable<double> data, int index) {
  if (index <= 0 || index >= data.length - 1) {
    return -1000.0;
  }
  return (data.elementAt(index + 1) -
      2 * data.elementAt(index) +
      data.elementAt(index - 1));
}

double movingAverage(List<double> data, int movingAverageWindow) {
  if (data.length < movingAverageWindow) return 0.0;
  double sum = 0.0;
  for (int i = data.length - movingAverageWindow; i < data.length; i++) {
    sum += data[i];
  }
  return sum / movingAverageWindow;
}

double standardDeviation(
  List<double> data,
  double movingAverageValue,
  int movingAverageWindow,
) {
  if (data.length < movingAverageWindow) return 0.0;

  double sum = 0.0;

  for (int i = data.length - movingAverageWindow; i < data.length; i++) {
    sum += pow(data[i] - movingAverageValue, 2).toDouble();
  }

  return sqrt(sum / movingAverageWindow);
}

Map<String, double> calculateBollingerBands(
  List<double> data,
  int movingAverageWindow,
  double bollingerMultiplier,
) {
  double mean = movingAverage(data, movingAverageWindow);
  double sd = standardDeviation(data, mean, movingAverageWindow);

  double upperBand = mean + (bollingerMultiplier * sd);
  double lowerBand = mean - (bollingerMultiplier * sd);
  return {
    'movingAverage': mean,
    'upperBand': upperBand,
    'lowerBand': lowerBand,
    'variance': sd * sd,
  };
}

double mean(List<double> data) {
  if (data.isEmpty) {
    return 0.0;
  }

  double sum = 0.0;
  for (var value in data) {
    sum += value;
  }
  return sum / data.length;
}

double variance(List<double> data) {
  if (data.isEmpty) {
    return 0.0;
  }

  double meanValue = mean(data);
  double sum = 0.0;
  for (var value in data) {
    sum += pow(value - meanValue, 2).toDouble();
  }
  return sum / data.length;
}
