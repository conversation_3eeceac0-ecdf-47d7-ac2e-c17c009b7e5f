import 'package:ai_vanse/model/activity/activity.dart';
import 'package:ai_vanse/model/activity/activity_year.dart' as year;
import 'package:ai_vanse/utils/common.dart';

class ActivitySleepSummary {
  final ActivitySleepPercent sleepPercent;
  final int totalSleep;
  final List<Activity> activities;

  ActivitySleepSummary({
    required this.sleepPercent,
    required this.totalSleep,
    required this.activities,
  });

  factory ActivitySleepSummary.fromActivities(List<Activity> activities) {
    return _createSummary(activities);
  }

  factory ActivitySleepSummary.fromActivity({Activity? activity}) {
    return _createSummary(activity != null ? [activity] : []);
  }

  factory ActivitySleepSummary.fromActivityYear(
    List<year.ActivityYear> activities,
  ) {
    final List<Activity> convertedActivities =
        activities.map((yearActivity) {
          return Activity(
            duration: ActivityDuration(
              lying: yearActivity.duration?.lying ?? 0,
              lyingOnBack: yearActivity.duration?.lyingOnBack ?? 0,
              lyingOnStomach: yearActivity.duration?.lyingOnStomach ?? 0,
              lyingOnLeft: yearActivity.duration?.lyingOnLeft ?? 0,
              lyingOnRight: yearActivity.duration?.lyingOnRight ?? 0,
              sitting: yearActivity.duration?.sitting ?? 0,
              standing: yearActivity.duration?.standing ?? 0,
              walking: yearActivity.duration?.walking ?? 0,
            ),
          );
        }).toList();

    return _createSummary(convertedActivities);
  }

  static ActivitySleepSummary _createSummary(List<Activity> activities) {
    int summarizeData(int? Function(ActivityDuration? duration) index) =>
        activities.fold(
          0,
          (sum, activity) => sum + (index(activity.duration) ?? 0),
        );

    return ActivitySleepSummary(
      sleepPercent: ActivitySleepPercent._(
        onBack: summarizeData((d) => d?.lyingOnBack),
        onLeft: summarizeData((d) => d?.lyingOnLeft),
        onRight: summarizeData((d) => d?.lyingOnRight),
        onStomach: summarizeData((d) => d?.lyingOnStomach),
        total: summarizeData((d) => d?.lying),
      ),
      totalSleep: summarizeData((d) => d?.lying),
      activities: activities,
    );
  }

  int get avgSleepPerDay => _calculateAvgSleep();

  int get avgSleepPerMonth => _calculateAvgSleep();

  String get formattedTotalSleep => _formatSleepDuration(totalSleep);

  String get formattedAvgSleepPerDay => _formatSleepDuration(avgSleepPerDay);

  int _calculateAvgSleep() {
    final validActivities = _getNonEmptyLying();
    return validActivities.isNotEmpty
        ? (totalSleep / validActivities.length).round()
        : 0;
  }

  List<Activity> _getNonEmptyLying() =>
      activities
          .where((activity) => (activity.duration?.lying ?? 0) > 0)
          .toList();

  String _formatSleepDuration(int durationInSeconds) =>
      durationInSeconds > 0
          ? Common.convertSecondsToHoursMinutes(durationInSeconds)
          : '--';
}

class ActivitySleepPercent {
  final int _onBackDuration;
  final int _onLeftDuration;
  final int _onRightDuration;
  final int _onStomachDuration;
  final int _totalDuration;

  ActivitySleepPercent._({
    required int onBack,
    required int onLeft,
    required int onRight,
    required int onStomach,
    required int total,
  }) : _onBackDuration = onBack,
       _onLeftDuration = onLeft,
       _onRightDuration = onRight,
       _onStomachDuration = onStomach,
       _totalDuration = total;

  int _calculatePercentage(int value) =>
      _totalDuration > 0 ? ((value * 100) / _totalDuration).round() : 0;

  int get onBack => _calculatePercentage(_onBackDuration);

  int get onLeft => _calculatePercentage(_onLeftDuration);

  int get onRight => _calculatePercentage(_onRightDuration);

  int get onStomach => _calculatePercentage(_onStomachDuration);

  int get total => _totalDuration;
}
