# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - build/**
    - lib/**.g.dart
    - lib/**.gen.dart
    - lib/**.freezed.dart
    - lib/l10n/locales/**
  errors:
    invalid_annotation_target: ignore

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    avoid_print: true
    always_use_package_imports: true
    avoid_relative_lib_imports: true
    prefer_single_quotes: true
    require_trailing_commas: true
    camel_case_extensions: true
    camel_case_types: true
    curly_braces_in_flow_control_structures: true
    eol_at_end_of_file: true
    file_names: true
    unnecessary_new: true

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
