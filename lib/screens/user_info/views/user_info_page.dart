import 'package:ai_vanse/extensions/context_extension.dart';
import 'package:ai_vanse/screens/user_info/controllers/user_info_controller.dart';
import 'package:ai_vanse/screens/user_info/views/user_info_detail.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserInfoPage extends GetView<UserInfoController> {
  const UserInfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(
            height: 136.sp + context.safeAreaTopSize,
            child: Stack(
              children: [
                Positioned.fill(
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0x1F08D2B4), Color(0xff08D2B4)],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: CustomPaint(painter: ProfileCurveBackground()),
                ),
                Positioned.fill(
                  child: Center(
                    child: SizedBox.square(
                      dimension: 90.sp,
                      child: const CircleAvatar(
                        backgroundImage: AssetImage(
                          'assets/images/icon_no_image.png',
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const UserInfoDetail(),
        ],
      ),
    );
  }
}

class ProfileCurveBackground extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paintFill =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    final Path path = Path();
    path.moveTo(0, size.height * 0.7); // เริ่มที่ขอบโค้งซ้าย
    path.quadraticBezierTo(
      size.width * 0.15, // ปรับความโค้ง X
      size.height * 0.55, // ปรับความโค้ง Y
      size.width * 0.5, // จุดตก X
      size.height * 0.5, // จุดตก Y
    ); // โค้งด้านซ้าย
    path.quadraticBezierTo(
      size.width * 0.85, // ปรับความโค้ง X
      size.height * 0.55, // ปรับความโค้ง Y
      size.width, // จุดตก X
      size.height * 0.7, // จุดตก Y
    ); // โค้งด้านขาว
    path.lineTo(size.width, size.height); // ลากเส้น
    path.lineTo(0, size.height); // ลากเส้น
    path.close();

    canvas.drawPath(path, paintFill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
