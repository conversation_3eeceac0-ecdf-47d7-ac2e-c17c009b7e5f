import 'package:ai_vanse/utils/common.dart';
import 'package:ai_vanse/widgets/app_text.dart';
import 'package:ai_vanse/widgets/text_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppPageContainer extends StatelessWidget {
  final String? title;
  final Widget body;
  final Widget? bottomNavigationBar;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final bool resizeToAvoidBottomInset;
  final bool autoHideKeyboard;
  final Color? appBarFontColor;
  final Color? appBarBackgroundColor;
  final Color? backgroundColor;
  final Drawer? leftDrawer;
  final Drawer? rightDrawer;
  final List<Widget>? actions;
  final Widget? leading;
  final SystemUiOverlayStyle systemOverlayStyle;
  final PreferredSizeWidget? appBar;
  final bool? extendBody;
  final bool? extendBodyBehindAppBar;

  const AppPageContainer({
    super.key,
    required this.body,
    this.appBar,
    this.title,
    this.resizeToAvoidBottomInset = false,
    this.autoHideKeyboard = false,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.appBarFontColor,
    this.appBarBackgroundColor,
    this.backgroundColor,
    this.leftDrawer,
    this.rightDrawer,
    this.actions,
    this.leading,
    this.systemOverlayStyle = SystemUiOverlayStyle.dark,
    this.extendBody,
    this.extendBodyBehindAppBar,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final overlayStyle = systemOverlayStyle.copyWith(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    );
    final appContainer = AnnotatedRegion<SystemUiOverlayStyle>(
      value: overlayStyle,
      child: Scaffold(
        appBar: appBar ?? _buildAppBar(context, overlayStyle),
        backgroundColor: backgroundColor ?? theme.colorScheme.primaryContainer,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        extendBody: extendBody ?? false,
        extendBodyBehindAppBar: extendBodyBehindAppBar ?? false,
        body: body,
        bottomNavigationBar: bottomNavigationBar,
        floatingActionButton: floatingActionButton,
        floatingActionButtonLocation: floatingActionButtonLocation,
        drawer: leftDrawer,
        endDrawer: rightDrawer,
      ),
    );

    if (autoHideKeyboard) {
      return GestureDetector(
        onTap: () {
          Common.hideKeyboard(context);
        },
        child: appContainer,
      );
    }

    return appContainer;
  }

  PreferredSizeWidget? _buildAppBar(
    BuildContext context,
    SystemUiOverlayStyle overlayStyle,
  ) {
    if (title == null && actions == null) return null;
    return AppBar(
      centerTitle: true,
      titleSpacing: 0.0,
      title: AppText(
        title ?? '',
        appTextStyle: AppTextStyle.bodyBold,
        color: appBarFontColor,
      ),
      actions: actions,
      backgroundColor: appBarBackgroundColor,
      leading: leading ?? _buildDefaultLeading(context),
      leadingWidth: 100.sp,
      iconTheme: IconThemeData(color: appBarFontColor),
      systemOverlayStyle: overlayStyle,
    );
  }

  Widget? _buildDefaultLeading(BuildContext context) {
    if (ModalRoute.of(context)?.impliesAppBarDismissal ?? false) {
      return const TextBackButton();
    }
    return null;
  }
}
